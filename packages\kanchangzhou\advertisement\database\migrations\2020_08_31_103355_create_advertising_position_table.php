<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdvertisingPositionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('advertising_position', function (Blueprint $table) {
            $table->id();
            $table->string("position_name", 30);
            $table->string("key");
            $table->bigInteger("position_type_id");
            $table->integer("type")->comment("广告位，广告提取方式，0为权重方式 1为随机 2为提取所有广告");
            $table->timestamps(0);
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('advertising_position');
    }
}
