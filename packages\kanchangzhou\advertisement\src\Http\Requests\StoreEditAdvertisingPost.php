<?php

namespace Kanchangzhou\Advertisement\Http\Requests;

use Kanchangzhou\Advertisement\Exceptions\AdException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;

class StoreEditAdvertisingPost extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "title"=>"required|max:50|min:3",
            "is_show_title"=>"required|numeric|min:0|max:1",
            "advertisers_id"=>"required|numeric|min:1",
            "advertising_position_id"=>"required|numeric|min:1",
            "status"=>"required|boolean",
            "username"=>"required|min:1",
            "thumb_img"=>"nullable|array",
            "start_show_time"=>"required|date_format:Y-m-d H:i:s",
            "stop_show_time"=>"required|date_format:Y-m-d H:i:s",
            "bz"=>"",
            "type"=>"required|numeric|min:0|max:2",
            "style"=>"required|numeric|min:0|max:1",
            "redirectable"=>"",
            "clickNum"=>"",
            "power"=>"required|numeric|min:0|max:100",
            "serve_time"=>"required|numeric|min:1|max:100",
            "media_type"=>"required|numeric|min:0|max:2",
            "display_times"=>"required|numeric|min:1"
        ];
    }

    public function messages()
    {
        return [
            "title.required"=>"广告名称必填",
            "title.max"=>"广告名称最长为50个字符",
            "is_show_title.required"=>"是否显示标题必填",
            "is_show_title.numeric"=>"是否显示标题只能是0或1",
            "is_show_title.min"=>"是否显示标题只能是0或1",
            "is_show_title.max"=>"是否显示标题只能是0或1",
            "title.min"=>"广告名称最短为3个字符",
            "advertisers_id.required"=>"广告商ID必填",
            'advertisers_id.numeric'=>"广告商ID必须为数字",
            "advertisers_id.min"=>"广告商ID必须大于0",
            "advertising_position_id.required"=>"广告位ID必填",
            'advertising_position_id.numeric'=>"广告位ID必须为数字",
            "advertising_position_id.min"=>"广告位ID必须大于0",
            "status.required"=>"广告状态必传",
            "status.boolean"=>"广告状态只限定在上架或下架",
            "username.required"=>"操作人必传",
            "username.min"=>"操作人不能为空",
            "thumb_img.array"=>"广告媒体信息必须是数组",
            "start_show_time.required"=>"开始显示时间必传",
            "start_show_time.date_format"=>"开始显示时间格式必须是Y-m-d H:i:s",
            "stop_show_time.required"=>"结束显示时间必传",
            "stop_show_time.date_format"=>"结束显示时间格式必须是Y-m-d H:i:s",
            "type.required"=>"广告类型必传",
            "type.numeric"=>"广告类型必须是数字",
            "type.min"=>"广告类型最小为0",
            "type.max"=>"广告类型最大为2",
            "style.required"=>"广告样式必传",
            "style.numeric"=>"广告样式必须是数字",
            "style.min"=>"广告样式最小为0",
            "style.max"=>"广告样式最大为1",
            "power.required"=>"广告权重必传",
            "power.numeric"=>"广告权重必须是数字",
            "power.min"=>"广告权重最小为0",
            "power.max"=>"广告权重最大为100",
            "serve_time.required"=>"投放时长必填",
            "serve_time.numeric"=>"投放时长必须是数字",
            "serve_time.min"=>"投放时长最小为1",
            "serve_time.max"=>"投放时长最大为100",
            "media_type.required"=>"媒体资源文件类型必传",
            "media_type.numeric"=>"媒体资源文件类型必须是数字",
            "media_type.min"=>"媒体资源文件类型最小为0",
            "media_type.max"=>"媒体资源文件类型最大为2",
            "display_times.required"=>"每日显示次数必传",
            "display_times.numeric"=>"每日显示次数必须是数字",
            "display_times.min"=>"每日显示次数最小为1",
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw(new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $validator->errors()));
    }
}
