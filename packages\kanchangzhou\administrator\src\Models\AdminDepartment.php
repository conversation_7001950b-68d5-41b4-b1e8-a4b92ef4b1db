<?php

namespace Kanchangzhou\Administrator\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Kanchangzhou\Administrator\Models\AdminUser;

/**
 * Kanchangzhou\Administrator\Models\AdminDepartment
 *
 * @property int $id
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AdminUser> $adminUsers
 * @property-read int|null $admin_users_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminDepartment whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAdminDepartment
 */
class AdminDepartment extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function adminUsers(){
        return $this->hasMany(AdminUser::class);
    }
}
