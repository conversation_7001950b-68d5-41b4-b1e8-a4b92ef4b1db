<?php

namespace Kanchangzhou\Administrator\Http\AdminControllers;


use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Exceptions\PermissionFailException;
use Kanchangzhou\Administrator\Http\Resources\PermissionResource;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Spatie\Permission\Models\Permission;

class PermissionController extends BaseController
{
    /**
     * 权限列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index() {
        PermissionHook::can('权限.列表');

        $permissions = Permission::query()
                                 ->where('guard_name', 'kadmin')
                                 ->get()
                                 ->map(function ($permission) {
                                     $l = explode('.', $permission->name);
                                     [
                                         $group,
                                         $name,
                                     ] = count($l) > 1 ? $l : [
                                         $l[0],
                                         null,
                                     ];

                                     return [
                                         'id' => $permission->id,
                                         'name' => $name ?? $group,
                                         'group' => $name ? $group : '其他',
                                     ];
                                 })
                                 ->groupBy('group');

        return Respond::respondWithData(JsonResource::collection($permissions));
    }

    /**
     * 权限详情
     *
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     */
    public function show($id) {
        PermissionHook::can('权限.详情', $id);

        try {
            $permission = Permission::findById($id, 'kadmin');
        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        return Respond::respondWithData(PermissionResource::make($permission));
    }

    /**
     * 通用验证
     *
     * @param Request $request
     * @param Permission|null $model
     *
     * @throws ValidateFailedException
     */
    protected function _validate(Request $request, $model = null) {
        try {
            $this->validate($request, [
                'group' => 'required',
                'name' => [
                    'required',
                    $model ? function ($attr, $value, $fail) use ($request, $model) {
                        if (Permission::where('name', $request->input('group') . '.' . $value)
                                      ->where('id', '<>', $model->id)
                                      ->exists()) {
                            return $fail('该权限值已经存在');
                        }

                        return true;
                    } : function ($attr, $value, $fail) use ($request) {
                        if (Permission::where('name', $request->input('group') . '.' . $value)
                                      ->exists()) {
                            return $fail('该权限值已经存在');
                        }
                    },
                ],
            ], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }
    }

    /**
     * 新建权限
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidateFailedException
     */
    public function store(Request $request) {
        PermissionHook::can('权限.新建');

        $this->_validate($request);

        $permission = Permission::create([
            'name' => $request->input('group') . '.' . $request->input('name'),
            //            'title' => $request->input('title'),
            'guard_name' => 'kadmin',
        ]);

        return Respond::respondWithData(PermissionResource::make($permission));
    }

    /**
     * 更新权限
     *
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     * @throws ValidateFailedException
     */
    public function update(Request $request, $id) {
        PermissionHook::can('权限.更新', $id);

        try {
            $permission = Permission::findById($id, 'kadmin');
        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        $this->_validate($request, $permission);

        $permission->name = $request->input('group') . '.' . $request->input('name');
//        $permission->title = $request->input('title');
//        $permission->group = $request->input('group');
        $permission->save();

        return Respond::respondWithData(PermissionResource::make($permission));
    }

    /**
     * 删除权限
     *
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     */
    public function destroy($id) {
        PermissionHook::can('权限.删除', $id);

        try {
            $permission = Permission::findById($id);
        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        $permission->delete();

        return Respond::respondWithData();
    }
}
