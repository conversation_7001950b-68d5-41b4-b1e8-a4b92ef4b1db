<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('ai_user_packages', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')
                  ->comment('用户ID');
            $table->integer('total_tokens');
            $table->integer('used_tokens');
            $table->string('package_type')
                  ->comment('套餐类型');
            $table->bigInteger('order_id')
                  ->comment('订单ID');
            $table->dateTime('expired_at')
                  ->comment('过期时间');
            $table->integer('resource_package_id');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('ai_user_packages');
    }
};
