<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AiOrderException;
use Kanchangzhou\AI\Exceptions\MediaAiException;
use Kanchangzhou\AI\Http\Resources\ChatRoleCategoryResource;
use Kanchangzhou\AI\Http\Resources\ChatRoleResource;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Models\ChatRoleCategory;
use Kanchangzhou\AI\Models\ChatSetting;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Services\Audio\AiAudioTtsManager;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\AI\Supports\GreenContent;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Hooks\SensitiveWordsHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class AIChatController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('智能对话.对话');

        $role = ChatRole::where('role_uuid', "{$request->input('role_id')}")
                        ->first();

        $this->validate($request, [
            'prompt' => [
                Rule::requiredIf(function () use ($request, $role) {
                    if ($role && $role->type == ChatRole::TYPE_NORMAL && !$request->input('is_retry')) {
                        return true;
                    }

                    if ($role && $role->type == ChatRole::TYPE_STEP) {
                        return false;
                    }

                    if (!$request->input('is_retry')) {
                        return true;
                    }

                    return false;
                }),
            ],
            'module_key' => Rule::requiredIf(function () use ($request, $role) {
                if ($role) {
                    return false;
                }

                return true;
            }),
            'chat_uuid' => 'required_if:is_retry,1',
            'is_retry' => '',
            'role_id' => '',
            'step' => function ($attribute, $value, $fail) use ($request, $role) {
                if ($role && !empty($value) && !key_exists($value, $role->options)) {
                    return $fail('参数错误');
                }
            },
            'step_input' => [
                'nullable',
                Rule::requiredIf(function () use ($request, $role) {
                    return $role && $role->type == ChatRole::TYPE_STEP;
                }),
                'array',
            ],
            'step_input.*' => [
                function ($attribute, $value, $fail) use ($request, $role) {
                    if ($role && $role->type == ChatRole::TYPE_STEP) {
                        $fields = $role->options[$request->input('step') ?? 'step_1']['fields'] ?? [];
                        foreach ($fields as $field) {
                            if ('step_input.' . $field['field'] == $attribute) {
                                if ($field['required'] && empty($value)) {
                                    return $fail('请填写' . $field['title']);
                                }
                            }
                        }
                    }
                },
            ],
        ]);

//        if (!SensitiveWordsHook::isLegal($request->input('prompt'), SensitiveWordsHook::SCENES_ALL, false)) {
//            throw new MediaAiException(MediaAiException::HAS_SENSITIVE);
//        }

        $defaultChatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                                     ->where('status', ChatModel::STATUS_VALID)
                                     ->first();

        $chatModel = ChatModel::where('model_uuid', "{$request->input('module_key')}")
                              ->where('status', ChatModel::STATUS_VALID)
                              ->first() ?? $defaultChatModel;

        if ($role && $role->module_key) {
            $chatModel = ChatModel::where('model_uuid', $role->module_key)
                                  ->where('status', ChatModel::STATUS_VALID)
                                  ->first() ?? $defaultChatModel;
        }

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatModel->provider];

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');

        $service = new $serviceProvider([
            'prompt' => $request->input('prompt'),
            'chat_uuid' => $request->input('chat_uuid'),
            'role_id' => $request->input('role_id'),
            'role_step' => $request->input('step'),
            'role_step_input' => $request->input('step_input'),
            'is_retry' => $request->input('is_retry'),
        ], $chatModel, $aiUser);

        $res = $service->handle();

//        if (!SensitiveWordsHook::isLegal($res->message, SensitiveWordsHook::SCENES_ALL, false)) {
//            throw new MediaAiException(MediaAiException::HAS_SENSITIVE);
//        }

        return Respond::respondWithData($res->toArray());
    }

    public function streamIndex(Request $request) {
        PermissionHook::can('智能对话.对话');

        ini_set('output_buffering', 'off');
        while (@ob_end_flush()) ;

        $role = ChatRole::where('role_uuid', "{$request->input('role_id')}")
                        ->first();

        try {
            $this->validate($request, [
                'prompt' => [
                    Rule::requiredIf(function () use ($request, $role) {
                        if ($role && $role->type == ChatRole::TYPE_NORMAL && !$request->input('is_retry')) {
                            return true;
                        }

                        if ($role && $role->type == ChatRole::TYPE_STEP) {
                            return false;
                        }

                        if (!$request->input('is_retry')) {
                            return true;
                        }

                        return false;
                    }),
                ],
                'module_key' => Rule::requiredIf(function () use ($request, $role) {
                    if ($role) {
                        return false;
                    }

                    return true;
                }),
                'chat_uuid' => 'required_if:is_retry,1',
                'is_retry' => '',
                'role_id' => '',
                'step' => function ($attribute, $value, $fail) use ($request, $role) {
                    if ($role && !empty($value) && !key_exists($value, $role->options)) {
                        return $fail('参数错误');
                    }
                },
                'step_input' => [
                    'nullable',
                    Rule::requiredIf(function () use ($request, $role) {
                        return $role && $role->type == ChatRole::TYPE_STEP;
                    }),
                    'array',
                ],
                'step_input.*' => [
                    function ($attribute, $value, $fail) use ($request, $role) {
                        if ($role && $role->type == ChatRole::TYPE_STEP) {
                            $fields = $role->options[$request->input('step') ?? 'step_1']['fields'] ?? [];
                            foreach ($fields as $field) {
                                if ('step_input.' . $field['field'] == $attribute) {
                                    if ($field['required'] && empty($value)) {
                                        return $fail('请填写' . $field['title']);
                                    }
                                }
                            }
                        }
                    },
                ],
                'fileids' => '',
            ]);
        } catch (ValidationException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => ValidateFailedException::VALIDATION_ERROR,
                        'message' => $e->validator->errors()
                                                  ->first(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        } catch (AIException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }


        if (!GreenContent::plusIsLegal($request->input('prompt'), 'llm_query_moderation')) {
            return response()->stream(function () {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => MediaAiException::HAS_SENSITIVE,
                        'message' => '输入内容包含敏感信息',
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }

        $defaultChatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                                     ->where('status', ChatModel::STATUS_VALID)
                                     ->first();

        $chatModel = ChatModel::when($role && $role->module_key, function ($query) use ($role) {
            $query->where('model_uuid', "{$role->module_key}");
        }, function ($query) use ($request) {
            $query->where('model_uuid', "{$request->input('module_key')}");
        })
                              ->where('status', ChatModel::STATUS_VALID)
                              ->first() ?? $defaultChatModel;


        if (!$chatModel) {
            return response()->stream(function () {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => MediaAiException::MISS_INTERFACE,
                        'message' => '缺少服务提供者',
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }

        try {
            ChatLimit::checkLimit($chatModel->model_type, $chatModel->provider, $chatModel->model_id, AuthFacade::adminUser()
                                                                                                                ->getId());
        } catch (AIException|AiOrderException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);

        }

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatModel->provider];

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider([
            'prompt' => $request->input('prompt'),
            'chat_uuid' => $request->input('chat_uuid'),
            'role_id' => $request->input('role_id'),
            'role_step' => $request->input('step'),
            'role_step_input' => $request->input('step_input'),
            'is_retry' => $request->input('is_retry'),
            'fileids' => $request->input('fileids'),
        ], $chatModel, $aiUser);

        return response()->stream(function () use ($service) {
            $service->streamHandle();
        }, 200, [
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'text/event-stream',
            'X-Accel-Buffering' => 'no',
        ]);
    }


    public function audioChat(Request $request) {
        ini_set('output_buffering', 'off');
        while (@ob_end_flush()) ;


        try {
            $this->validate($request, [
                'audio' => 'required|file',
            ], [
                'audio.required' => '请说话',
                'audio.file' => '音频文件格式不正确',
                'audio.mimes' => '音频文件格式不正确',
            ]);
        } catch (ValidationException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => ValidateFailedException::VALIDATION_ERROR,
                        'message' => $e->validator->errors()
                                                  ->first(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        } catch (AIException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }

        return response()->stream(function () use ($request) {
            // 语音转文字
            $client = (new AiAudioTtsManager())->client('aliyun')
                                               ->asrToText($request->file('audio')
                                                                   ->getContent());

//        // 获取音频内容
            $resText = $client->json('result');

            if (!GreenContent::plusIsLegal($resText, 'llm_query_moderation')) {
                return response()->stream(function () {
                    echo "id:" . time() . PHP_EOL;
                    echo "event:error" . PHP_EOL;
                    echo "data: " . json_encode([
                            'error_code' => MediaAiException::HAS_SENSITIVE,
                            'message' => '输入内容包含敏感信息',
                        ]) . PHP_EOL . PHP_EOL;
                    flush();
                }, 200, [
                    'Cache-Control' => 'no-cache',
                    'Content-Type' => 'text/event-stream',
                    'X-Accel-Buffering' => 'no',
                ]);
            }

            // 调用模型的聊天方法
            $body = [
                'model' => 'qwen-max',
                'input' => [
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => '你是一个智能语音聊天机器人, 所有的回复内容尽量保持口语化, 请勿涉及政治、色情、暴力等违法违规内容。',
                        ],
                        [
                            'role' => 'user',
                            'content' => $resText,
                        ],
                    ],
                ],
                'parameters' => [
                    'result_format' => 'message',
                    'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                    'temperature' => 0.85,
                    'repetition_penalty' => 1.0,
                    'enable_search' => true,
                    'incremental_output' => true,

                ],
            ];

            $ttsClient = (new AiAudioTtsManager())->client('aliyun')
                                                  ->setRequestConfig([
                                                      'voice' => 'zhixiaobai',
                                                      'format' => 'pcm',
                                                      'sample_rate' => 16000,
                                                      'volume' => 50,
                                                      'speech_rate' => 0,
                                                      'pitch_rate' => 0,
                                                  ]);

            $tempstr = '';
            $originstr = [];
            $changedstr = [];
            $response = Http::withHeaders([
                'X-DashScope-SSE' => 'enable',
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'application/json',
                'Accept' => 'text/event-stream',
            ])
                            ->withToken(config('kai.chat.providers.aliyun.api_key'))
                            ->withOptions([
                                'curl' => [
                                    CURLOPT_WRITEFUNCTION => function ($curl, $data) use ($ttsClient, &$tempstr, &$originstr, &$changedstr) {
                                        preg_match('/data:(.*)/', $data, $matches);
                                        $dataStr = $matches[1];
                                        $resData = json_decode($dataStr, true);

                                        if (isset($resData['code'])) {
                                            echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                            echo "event:error" . PHP_EOL;
                                            echo "data:" . json_encode([
                                                    'time' => microtime(true),
                                                    'message' => __('ai::ai.' . $resData['message']),
                                                    'code' => $resData['code'],
                                                ]) . PHP_EOL . PHP_EOL;
                                            flush();

                                            return strlen($data);
                                        }

                                        $messageText = $resData['output']['choices'][0]['message']['content'];
                                        if (empty($messageText)) {
                                            return strlen($data);
                                        }
                                        $originstr[] = $messageText;
                                        $tempstr .= $messageText;
                                        // 检查临时字符串是否包含句子结束标点
                                        if (preg_match("/[。？！]/u", $tempstr)) {
                                            // 分割临时字符串成完整句子
                                            $sentences = preg_split("/(?<=[。？！])\s*/u", $tempstr, -1, PREG_SPLIT_NO_EMPTY);
                                            // 保证最后一个部分可能不是完整句子，暂时保留在 $tempString
                                            if (count($sentences) > 1) {
                                                $tempstr = array_pop($sentences);
                                                // 将完整句子添加到新数组
                                                $changedstr = array_merge($changedstr, $sentences);

                                                $ttsData = $ttsClient->textToSpeechStream($sentences[0]);

                                                echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                                echo "event:audio" . PHP_EOL;
                                                echo "data:" . json_encode([
                                                        'time' => microtime(true),
                                                        'message' => base64_encode($ttsData),
                                                    ]) . PHP_EOL . PHP_EOL;
                                                flush();
//
//                                            broadcast(new TalkToTalk(base64_encode($ttsData)));

//                                                $fileName = 'audio_' . microtime(true) . '.pcm';
//                                                $filePath = 'audio/' . $fileName;
//
//                                                // 保存文件到存储中 (可以是本地存储，S3 等等)
//                                                \Storage::disk('local')
//                                                        ->put($filePath, $ttsData);


                                            } else {
                                                // 仅一个句子，保留在 $tempString
                                                $tempstr = $sentences[0];
                                            }
                                        }

                                        return strlen($data);
                                    },
                                ],
                            ])
                            ->post('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', $body);

            if (!empty($tempstr)) {
                $changedstr[] = $tempstr;

                $ttsData = $ttsClient->textToSpeechStream($tempstr);

                echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                echo "event:audio" . PHP_EOL;
                echo "data:" . json_encode([
                        'time' => microtime(true),
                        'message' => base64_encode($ttsData),
                    ]) . PHP_EOL . PHP_EOL;
                flush();

//
//            broadcast(new TalkToTalk(base64_encode($ttsData)));
//
//                $fileName = 'audio_' . microtime(true) . '.pcm';
//                $filePath = 'audio/' . $fileName;
//
//                // 保存文件到存储中 (可以是本地存储，S3 等等)
//                \Storage::disk('local')
//                        ->put($filePath, $ttsData);

            }

        }, 200, [
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'text/event-stream',
            'X-Accel-Buffering' => 'no',
        ]);
    }


    public function uploadFiles(Request $request) {
        $this->validate($request, [
            'file' => 'required|file|mimes:txt,pdf,doc,docx,xls,xlsx,csv|max:20480',
            'module_key' => 'required',
        ]);

        $chatModel = ChatModel::where('model_uuid', $request->input('module_key'))
                              ->where('status', ChatModel::STATUS_VALID)
                              ->firstOrFail();

        if ($chatModel->can_files == ChatModel::CAN_FILES_NO) {
            throw new AIException(AIException::NOT_SUPPORT_FILES);
        }

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatModel->provider];

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider([
            'prompt' => $request->input('prompt'),
            'chat_uuid' => $request->input('chat_uuid'),
            'role_id' => $request->input('role_id'),
            'role_step' => $request->input('step'),
            'role_step_input' => $request->input('step_input'),
            'is_retry' => $request->input('is_retry'),
        ], $chatModel, $aiUser);

        $res = $service->uploadFile($request);

        return $res;
//        return Respond::respondWithData($res);
    }


    public function chatModules() {
        PermissionHook::can('智能对话.对话');
        $chatModels = ChatModel::where('status', ChatModel::STATUS_VALID)
                               ->where('chat_used', ChatModel::CHAT_USED_YES)
                               ->orderBy('sort')
                               ->orderByDesc('id')
                               ->get()
                               ->map(function ($item) {
                                   return [
                                       'module' => $item->model_uuid,
                                       'label' => $item->title,
                                       'can_files' => $item->can_files,
                                   ];
                               });

        return Respond::respondWithData($chatModels);
    }


    public function creatorRoles(Request $request) {
        PermissionHook::can('智能对话.场景列表');

        $roles = ChatRole::where('is_show', ChatRole::IS_SHOW_YES)
                         ->where('type', ChatRole::TYPE_STEP)
                         ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                         ->where('owner_id', 0)
                         ->when($request->input('is_hot'), function ($query) use ($request) {
                             $query->where('is_hot', $request->input('is_hot'));
                         })
                         ->when($request->input('category_id'), function ($query, $categoryId) {
                             $query->where('chat_role_category_id', $categoryId);
                         })
                         ->when($request->input('title'), function ($query, $title) {
                             $query->where('title', 'like', '%' . $title . '%');
                         })
                         ->orderBy('sort')
                         ->orderByDesc('id')
                         ->paginate(12);

        return Respond::respondWithData(ChatRoleResource::collection($roles));
    }

    public function creatorRoleCategory() {
        PermissionHook::can('智能对话.场景列表');

        $categories = ChatRoleCategory::where('is_show', ChatRoleCategory::IS_SHOW_YES)
                                      ->orderBy('sort')
                                      ->paginate();

        return Respond::respondWithData(ChatRoleCategoryResource::collection($categories));
    }

    public function roleDetail($roleUuid) {
        PermissionHook::can('智能对话.场景详情');

        $role = ChatRole::where('role_uuid', $roleUuid)
                        ->where('is_show', ChatRole::IS_SHOW_YES)
                        ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                        ->firstOrFail();

        return Respond::respondWithData(ChatRoleResource::make($role));
    }

    public function assistantRoles(Request $request) {
        PermissionHook::can('智能对话.智能助手');

        $roles = ChatRole::where('is_show', ChatRole::IS_SHOW_YES)
                         ->where('type', ChatRole::TYPE_NORMAL)
                         ->where('chat_role_category_id', 8)
                         ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                         ->when($request->input('title'), function ($query, $title) {
                             $query->where('title', 'like', '%' . $title . '%');
                         })
                         ->where('owner_id', 0)
                         ->orderBy('sort')
                         ->orderByDesc('id')
                         ->paginate(12);

        return Respond::respondWithData(ChatRoleResource::collection($roles));
    }

    public function statistic($type = 'all') {
        PermissionHook::can('智能对话.对话历史');

        $totalTokens = ChatHistory::withTrashed()
                                  ->selectRaw('SUM(output_tokens + input_tokens) as total')
                                  ->where('type', ChatHistory::TYPE_CHAT)
                                  ->value('total');

        $totalCreatorTokens = ChatHistory::withTrashed()
                                         ->selectRaw('SUM(output_tokens + input_tokens) as total')
                                         ->whereHas('chatRole', function ($query) {
                                             $query->where('type', ChatRole::TYPE_STEP);
                                         })
                                         ->where('type', ChatHistory::TYPE_CHAT)
                                         ->value('total');

        $totalCreatorTimes = ChatHistory::withTrashed()
                                        ->where('type', ChatHistory::TYPE_CHAT)
                                        ->whereHas('chatRole', function ($query) {
                                            $query->where('type', ChatRole::TYPE_STEP);
                                        })
                                        ->where('role', ChatHistory::ROLE_ASSISTANT)
                                        ->count();

        $totalRoles = ChatRole::where('is_show', ChatRole::IS_SHOW_YES)
                              ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                              ->count();

        return Respond::respondWithData(compact('totalTokens', 'totalCreatorTokens', 'totalRoles', 'totalCreatorTimes'));
    }

    public function myLeftTokens() {
        $userId = AuthFacade::adminUser()
                            ->getId();

        return Respond::respondWithData([
            ChatHistory::TYPE_CHAT => Cache::tags(['chat_limit'])
                                           ->remember('chat_daily_limit_tokens', 7200, function () {
                                               return ChatSetting::where('key', 'chat_daily_limit_tokens')
                                                                 ->value('value') ?? 0;
                                           }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_CHAT, '', '', $userId),
            ChatHistory::TYPE_IMAGE => Cache::tags(['chat_limit'])
                                            ->remember('image_daily_limit_tokens', 7200, function () {
                                                return ChatSetting::where('key', 'image_daily_limit_tokens')
                                                                  ->value('value') ?? 0;
                                            }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_IMAGE, '', '', $userId),
            ChatHistory::TYPE_VIDEO => Cache::tags(['chat_limit'])
                                            ->remember('video_daily_limit_tokens', 7200, function () {
                                                return ChatSetting::where('key', 'video_daily_limit_tokens')
                                                                  ->value('value') ?? 0;
                                            }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_VIDEO, '', '', $userId),
            ChatHistory::TYPE_AUDIO => Cache::tags(['chat_limit'])
                                            ->remember('audio_daily_limit_tokens', 7200, function () {
                                                return ChatSetting::where('key', 'audio_daily_limit_tokens')
                                                                  ->value('value') ?? 0;
                                            }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_AUDIO, '', '', $userId),
        ]);
    }
}
