<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Hooks\SensitiveWordsHook;
use Kanchangzhou\Kernel\Supports\Respond;

use AlibabaCloud\SDK\Imageenhan\V20190930\Imageenhan;
use AlibabaCloud\SDK\Imageseg\V20191230\Imageseg;

use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

use AlibabaCloud\SDK\Imageenhan\V20190930\Models\GenerateImageWithTextRequest;
use AlibabaCloud\SDK\Imageenhan\V20190930\Models\GenerateSuperResolutionImageRequest;
use AlibabaCloud\SDK\Imageenhan\V20190930\Models\MakeSuperResolutionImageRequest;
use AlibabaCloud\SDK\Imageenhan\V20190930\Models\GetAsyncJobResultRequest as GetImageenhanAsyncJobResultRequest;

use AlibabaCloud\SDK\Imageseg\V20191230\Models\SegmentCommonImageRequest;
use AlibabaCloud\SDK\Imageseg\V20191230\Models\GetAsyncJobResultRequest as GetImagesegAsyncJobResultRequest;

class ImageHanController extends BaseController
{

    /**
     * 使用AK&SK初始化账号Client
     * @return Imageenhan Client
     */
    public static function createImageenhanClient(){
        $config = new Config([
            // 必填，您的 AccessKey ID
            "accessKeyId" => config('kai.image.providers.aliyun.access_key_id'),
            // 必填，您的 AccessKey Secret
            "accessKeySecret" => config('kai.image.providers.aliyun.access_key_secret')
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/imageenhan
        $config->endpoint = "imageenhan.cn-shanghai.aliyuncs.com";
        return new Imageenhan($config);
    }

    //视觉智能开放平台，图像生成
    public function generateImageWithText(Request $request) {

        PermissionHook::can('智能.文生图');

        $this->validate($request, [
            'text' => 'required|string',   //文本内容，支持中英文，中文不超过75个字，英文不超过75个字母，超过部分会自动截断
            //'resolution' =>"string",    //生成的图像分辨率。支持512512、10241024、1024768、7681024、7201280和1280720像素，默认1024*1024像素。
            //'number' =>"integer",    //图片生成数量。支持1~4张，默认1张。
        ]);

        $client = self::createImageenhanClient();

        $generateImageWithTextRequest = new GenerateImageWithTextRequest(
            [
                'text' => $request->input('text'),
                'resolution' => $request->input('resolution','1024*1024'),
                'number' => $request->input('number',1),
            ]
        );

        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $client->generateImageWithTextWithOptions($generateImageWithTextRequest, $runtime);

            if($response->statusCode == 200){

                $return = [
                    'image' => $response->body->images,
                    'log_id' => $response->body->log_id,
                ];

                return Respond::respondWithData($response->body);
            }
            throw new \Exception("数据获取失败");
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw new \Exception($error->message);
        }
    }

    //视觉智能开放平台，图像超分
    public function makeSuperResolutionImage(Request $request) {

        PermissionHook::can('智能.图像超分');

        $this->validate($request, [
            'url' => 'required|string',   //图像URL地址。
            //'mode' =>"string",    //图像输出模式，默认为base
            //'upscaleFactor' =>"integer",    //放大倍数。取值为1、2、3、4。缺省时默认为2。
            //'outputFormat' =>"string",    //输出图像的存储格式。可选范围：png、jpg、bmp，默认png。
            //'outputQuality' =>"integer",    //输出图像的质量因子，值越大质量越高。取值范围[30,100]，默认95，仅当OutputFormat是jpg时有效。
        ]);

        $client = self::createImageenhanClient();
        $makeSuperResolutionImageRequest = new MakeSuperResolutionImageRequest(
            [
                'url' => $request->input('url'),
                'mode' => $request->input('mode','base'),
                'upscaleFactor' => $request->input('upscaleFactor',2),
                'outputFormat' => $request->input('outputFormat','png'),
                'outputQuality' => $request->input('outputQuality',95),
            ]
        );
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $client->makeSuperResolutionImageWithOptions($makeSuperResolutionImageRequest, $runtime);

            if($response->statusCode == 200){
                return Respond::respondWithData($response->body);
            }
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw new \Exception($error->message);
        }
    }

    //视觉智能开放平台，查询异步任务结果
    public function getImageenhanJobResult(Request $request) {

        $this->validate($request, [
            'jobId' => 'required|string',   //异步接口返回的RequestId
        ]);

        $client = self::createImageenhanClient();
        $getAsyncJobResultRequest = new GetImageenhanAsyncJobResultRequest($request->all());
        $runtime = new RuntimeOptions([]);
        try {
            $response = $client->getAsyncJobResultWithOptions($getAsyncJobResultRequest, $runtime);
            if($response->statusCode == 200){
                if($response->body->data->errorCode){
                    throw new \Exception($response->body->data->errorMessage);
                }
                return Respond::respondWithData($response->body->data);
            }

            throw new \Exception("数据获取失败");
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw new \Exception($error->message);
        }
    }

    /**
     * 使用AK&SK初始化账号Client
     * @return Imageenhan Client
     */
    public static function createImagesegClient(){
        $config = new Config([
            // 必填，您的 AccessKey ID
            "accessKeyId" => config('kai.image.providers.aliyun.access_key_id'),
            // 必填，您的 AccessKey Secret
            "accessKeySecret" => config('kai.image.providers.aliyun.access_key_secret')
        ]);
        // 访问的域名
        $config->endpoint = "imageseg.cn-shanghai.aliyuncs.com";
        return new Imageseg($config);
    }

    //视觉智能开放平台，通用图像分割
    public function segmentCommonImage(Request $request) {

        PermissionHook::can('智能.图像分割');

        $this->validate($request, [
            'imageURL' => 'required|string',   //图像URL地址。
            //'returnForm' =>"string",    //指定返回的图像形式。
        ]);

        $client = self::createImagesegClient();
        $segmentCommonImageRequest = new SegmentCommonImageRequest(
            [
                'imageURL' => $request->input('imageURL'),
                'returnForm' => $request->input('returnForm'),
            ]
        );
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $client->segmentCommonImageWithOptions($segmentCommonImageRequest, $runtime);
            if($response->statusCode == 200){
                return Respond::respondWithData($response->body);
            }
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw new \Exception($error->message);
        }
    }

    //图片分割，查询异步任务结果
    public function getImagesegJobResult(Request $request) {

        $this->validate($request, [
            'jobId' => 'required|string',   //异步接口返回的RequestId
        ]);

        $client = self::createImagesegClient();
        $getAsyncJobResultRequest = new GetImagesegAsyncJobResultRequest($request->all());
        $runtime = new RuntimeOptions([]);
        try {
            $response = $client->getAsyncJobResultWithOptions($getAsyncJobResultRequest, $runtime);
            if($response->statusCode == 200){
                if($response->body->data->errorCode){
                    throw new \Exception($response->body->data->errorMessage);
                }
                return Respond::respondWithData($response->body->data);
            }

            throw new \Exception("数据获取失败");
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw new \Exception($error->message);
        }
    }
}

