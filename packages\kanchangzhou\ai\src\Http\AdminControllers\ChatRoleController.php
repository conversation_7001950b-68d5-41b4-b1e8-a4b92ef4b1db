<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ChatRoleController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('智能场景.场景列表');

        $roles = ChatRole::with(['category'])
                         ->when($request->input('is_show'), function ($query, $isShow) {
                             $query->where('is_show', $isShow);
                         })
                         ->when($request->input('is_hot'), function ($query) use ($request) {
                             $query->where('is_hot', $request->input('is_hot'));
                         })
                         ->when($request->input('category_id'), function ($query, $categoryId) {
                             $query->where('chat_role_category_id', $categoryId);
                         })
                         ->when($request->input('type'), function ($query, $type) {
                             $query->where('type', $type);
                         })
                         ->when($request->input('module_key'), function ($query, $moduleKey) {
                             $query->where('module_key', $moduleKey);
                         })
                         ->when($request->input('title'), function ($query, $title) {
                             $query->where('title', 'like', '%' . $title . '%');
                         })
                         ->where('owner_id', 0)
                         ->orderBy('sort')
                         ->orderByDesc('id')
                         ->paginate(12);

        return Respond::respondWithData(JsonResource::collection($roles));
    }

    public function show($id) {
        PermissionHook::can('智能场景.场景详情');

        $role = ChatRole::with(['category'])
                        ->where('id', $id)
                        ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($role));
    }

    public function formOptions() {
        $types = ChatRole::TYPE_MAP;
        $isShow = ChatRole::IS_SHOW_MAP;
        $isHot = ChatRole::IS_HOT_MAP;
        $showHistory = ChatRole::SHOW_HISTORY_MAP;

        $modules = ChatModel::where('status', ChatModel::STATUS_VALID)
                            ->orderBy('sort')
                            ->orderByDesc('id')
                            ->get()
                            ->map(function ($item) {
                                return [
                                    'module' => $item->model_uuid,
                                    'label' => $item->title,
                                    'temperature_range' => ChatModel::TEMPERATURE_RANGE[$item->provider],
                                    'penalty_range' => ChatModel::PENALTY_RANGE[$item->provider],
                                ];
                            })
                            ->values();

        $enableContext = ChatRole::ENABLE_CONTEXT_MAP;
        $isPublished = ChatRole::IS_PUBLISHED_MAP;

        return Respond::respondWithData(compact('types', 'modules', 'isShow', 'isHot', 'showHistory', 'enableContext', 'isPublished'));
    }

    public function store(Request $request) {
        PermissionHook::can('智能场景.场景创建');

        $validated = $this->validate($request, [
            'type' => 'required|in:' . implode(',', array_keys(ChatRole::TYPE_MAP)),
            'title' => 'required|max:25',
            'system_prompt' => 'required_if:type,' . ChatRole::TYPE_NORMAL . '|max:1000',
            'examples' => 'nullable|array',
            'chat_role_category_id' => 'required',
            'enable_context' => '',
            'context_count' => '',
            'temperature' => '',
            'penalty' => '',
            'is_published' => '',
            'module_key' => '',
            'is_show' => '',
            'is_hot' => '',
            'show_history' => '',
            'icon' => '',
            'description' => '',
            'sort' => '',
            'step_prompt' => 'required_if:type,' . ChatRole::TYPE_STEP . '|nullable|array',
            'options' => 'required_if:type,' . ChatRole::TYPE_STEP . '|nullable|array',
            //            'options .*.type' => 'required_if:type,' . ChatRole::TYPE_STEP,
        ]);
        $validated['is_published'] = $validated['is_published'] ?? ChatRole::IS_PUBLISHED_YES;
        $validated['role_uuid'] = Str::uuid();
        $role = ChatRole::create($validated);

        return Respond::respondWithData(JsonResource::make($role));
    }

    public function update(Request $request, $id) {
        PermissionHook::can('智能场景.场景编辑');

        $validated = $this->validate($request, [
            'type' => 'required|in:' . implode(',', array_keys(ChatRole::TYPE_MAP)),
            'title' => 'required|max:25',
            'system_prompt' => 'required_if:type,' . ChatRole::TYPE_NORMAL . '|max:1000',
            'examples' => 'nullable|array',
            'chat_role_category_id' => 'required',
            'enable_context' => '',
            'context_count' => '',
            'temperature' => '',
            'penalty' => '',
            'is_published' => '',
            'module_key' => '',
            'is_show' => '',
            'is_hot' => '',
            'show_history' => '',
            'icon' => '',
            'description' => '',
            'sort' => '',
            'step_prompt' => 'required_if:type,' . ChatRole::TYPE_STEP . '|nullable|array',
            'options' => 'required_if:type,' . ChatRole::TYPE_STEP . '|nullable|array',
            //            'options .*.type' => 'required_if:type,' . ChatRole::TYPE_STEP,
        ]);

        $role = ChatRole::where('id', $id)
                        ->firstOrFail();

        $validated['is_published'] = $validated['is_published'] ?? ChatRole::IS_PUBLISHED_YES;
        $role = $role->update($validated);

        return Respond::respondWithData();
    }

    public function destroy($id) {
        PermissionHook::can('智能场景.场景删除');

        $role = ChatRole::where('id', $id)
                        ->firstOrFail();

        $role->delete();

        return Respond::respondWithData();
    }

    public function myRoles(Request $request) {
        PermissionHook::can('智能场景.我的场景');

        $roles = ChatRole::with(['category'])
                         ->where($request->input('is_published'), function ($query, $isPublished) {
                             $query->where('is_published', $isPublished);
                         })
                         ->when($request->input('title'), function ($query, $title) {
                             $query->where('title', 'like', '%' . $title . '%');
                         })
                         ->where('owner_id', '<>', 0)
                         ->where('guard_name', 'kadmin')
                         ->orderBy('sort')
                         ->orderByDesc('id')
                         ->paginate(12);

        return Respond::respondWithData(JsonResource::collection($roles));
    }

    public function approve($id) {
        PermissionHook::can('智能场景.场景审核');

        $role = ChatRole::when(Str::isUuid($id), function ($query) use ($id) {
            $query->where('role_uuid', $id);
        }, function ($query) use ($id) {
            $query->where('id', $id);
        })
                        ->firstOrFail();

        $role->update([
            'is_published' => ($role->is_published + 1) % 2 ? ChatRole::IS_PUBLISHED_NO : ChatRole::IS_PUBLISHED_YES,
        ]);

        return Respond::respondWithData([
            'is_published' => $role->is_published,
            'is_published_str' => $role->is_published_str,
        ]);
    }
}
