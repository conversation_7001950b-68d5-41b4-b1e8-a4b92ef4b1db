<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Kanchangzhou\User\Models\User;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Article\Models\Article;
use Kanchangzhou\Article\Models\ArticleCategory;
use Kanchangzhou\HomePage\Models\HomeColumn;

class DemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run() {
        $faker = \Faker\Factory::create('zh_CN');

        // 创建模拟用户
        $user1 = User::create([
            'uuid' => $faker->uuid,
            'username' => 'user1',
            'password' => bcrypt('123456'),
            'mobile' => '12345678901',
            'id_number' => '6666666666',
            'true_name' => $faker->name,
            'gender' => \Arr::random(array_keys(User::genderArr())),
            'avatar' => 'https://dummyimage.com/100x100?text=user1',
        ]);

        $user2 = User::create([
            'uuid' => $faker->uuid,
            'username' => 'user2',
            'password' => bcrypt('123456'),
            'mobile' => '12345678901',
            'id_number' => '6666666666',
            'true_name' => $faker->name,
            'gender' => \Arr::random(array_keys(User::genderArr())),
            'avatar' => 'https://dummyimage.com/100x100?text=user2',
        ]);

        $user3 = User::create([
            'uuid' => $faker->uuid,
            'username' => 'user3',
            'password' => bcrypt('123456'),
            'mobile' => '12345678901',
            'id_number' => '6666666666',
            'true_name' => $faker->name,
            'gender' => \Arr::random(array_keys(User::genderArr())),
            'avatar' => 'https://dummyimage.com/100x100?text=user3',
        ]);

        // 创建管理员
        $admin1 = AdminUser::create([
            'username' => 'admin1',
            'password' => bcrypt('123456'),
            'mobile' => '12345567',
            'nickname' => $faker->name,
            'true_name' => $faker->name,
            'status' => AdminUser::STATUS_VALID,
            'avatar' => 'https://dummyimage.com/100x100?text=admin1',
        ]);
        $admin2 = AdminUser::create([
            'username' => 'admin2',
            'password' => bcrypt('123456'),
            'mobile' => '12345567',
            'nickname' => $faker->name,
            'true_name' => $faker->name,
            'status' => AdminUser::STATUS_VALID,
            'avatar' => 'https://dummyimage.com/100x100?text=admin2',
        ]);

        // 创建首页栏目
        HomeColumn::insert([
            [
                'id' => 1,
                'title' => '推荐',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_NORMAL,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_YES,
                'can_sort' => HomeColumn::CAN_SORT_NO,
                'can_delete' => HomeColumn::CAN_DELETE_NO,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 1,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
            [
                'id' => 2,
                'title' => '民生',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_NORMAL,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_YES,
                'can_sort' => HomeColumn::CAN_SORT_NO,
                'can_delete' => HomeColumn::CAN_DELETE_NO,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 2,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
            [
                'id' => 3,
                'title' => '时政',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_NORMAL,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_NO,
                'can_sort' => HomeColumn::CAN_SORT_YES,
                'can_delete' => HomeColumn::CAN_DELETE_YES,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 3,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
            [
                'id' => 4,
                'title' => '生活',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_NORMAL,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_NO,
                'can_sort' => HomeColumn::CAN_SORT_YES,
                'can_delete' => HomeColumn::CAN_DELETE_YES,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 4,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
            [
                'id' => 5,
                'title' => '视频',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_NORMAL,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_NO,
                'can_sort' => HomeColumn::CAN_SORT_YES,
                'can_delete' => HomeColumn::CAN_DELETE_YES,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 5,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
            [
                'id' => 6,
                'title' => '微视频',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_TWO_COLUMN,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_NO,
                'can_sort' => HomeColumn::CAN_SORT_YES,
                'can_delete' => HomeColumn::CAN_DELETE_YES,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 6,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
            [
                'id' => 7,
                'title' => '辖市区',
                'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
                'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
                'template' => HomeColumn::TEMPLATE_TWO_COLUMN,
                'redirect_to' => '',
                'is_recommended' => HomeColumn::IS_RECOMMENDED_NO,
                'can_sort' => HomeColumn::CAN_SORT_YES,
                'can_delete' => HomeColumn::CAN_DELETE_YES,
                'is_show' => HomeColumn::IS_SHOW_YES,
                'default_sort' => 6,
                'created_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
                'updated_at' => \Carbon\Carbon::now()
                                              ->toDateTimeString(),
            ],
        ]);

        $homeColumns = HomeColumn::all()
                                 ->pluck('id')
                                 ->toArray();

        HomeColumn::where('id', 7)
                  ->first()
                  ->categories()
                  ->createMany([
                      [
                          'title' => '溧阳市',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                      [
                          'title' => '金坛区',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                      [
                          'title' => '武进区',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                      [
                          'title' => '新北区',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                      [
                          'title' => '天宁区',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                      [
                          'title' => '钟楼区',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                      [
                          'title' => '经开区',
                          'icon' => '',
                          'url' => '',
                          'sort' => 10,
                          'is_show' => \Kanchangzhou\HomePage\Models\HomeCategory::IS_SHOW_YES,
                      ],
                  ]);

        // 创建文稿分类
        foreach (ArticleCategory::typeArr() as $type => $v) {
            for ($i = 0; $i < 10; $i++) {
                $category = ArticleCategory::create([
                    'type' => $type,
                    'title' => $faker->word(),
                    'sort' => $i,
                    'status' => ArticleCategory::STATUS_VALID,
                ]);

                for ($j = 0; $j < 25; $j++) {
                    switch ($type) {
                        case ArticleCategory::TYPE_NORMAL:
                            $listTemplate = \Arr::random(array_keys(Article::listTemplateArr()));
                            switch ($listTemplate) {
                                case Article::LIST_TEMPLATE_NO_IMG:
                                    $thumbnails = [];
                                    $articleType = Article::TYPE_NORMAL;
                                    break;
                                case Article::LIST_TEMPLATE_BIG_IMG:
                                    $thumbnails = [
                                        [
                                            'src' => 'https://dummyimage.com/750x422',
                                        ],
                                    ];
                                    $articleType = Article::TYPE_NORMAL;
                                    break;
                                case Article::LIST_TEMPLATE_ONE_IMG:
                                    $thumbnails = [
                                        [
                                            'src' => 'https://dummyimage.com/230x175',
                                        ],
                                    ];
                                    $articleType = Article::TYPE_NORMAL;
                                    break;
                                case Article::LIST_TEMPLATE_THREE_IMG:
                                    $thumbnails = [
                                        [
                                            'src' => 'https://dummyimage.com/230x175',
                                        ],
                                        [
                                            'src' => 'https://dummyimage.com/230x175',
                                        ],
                                        [
                                            'src' => 'https://dummyimage.com/230x175',
                                        ],
                                    ];
                                    $articleType = Article::TYPE_NORMAL;
                                    break;
                            }

                            $article = Article::create([
                                'uuid' => $faker->uuid,
                                'article_category_id' => $category->id,
                                'title' => $faker->sentence(rand(8, 12)),
                                'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
                                'summary' => $faker->sentences(rand(2, 4), true),
                                'content' => $faker->paragraphs(rand(3, 8), true),
                                'type' => $articleType,
                                'list_template' => $listTemplate,
                                'thumbnails' => $thumbnails,
                                'materials' => [],
                                'redirect_to' => '',
                                'author' => $faker->name,
                                'can_comment' => rand(1, 2),
                                'virtual_likes' => rand(20, 200),
                                'virtual_views' => rand(100, 800),
                                'true_views' => rand(100, 300),
                                'published_at' => \Carbon\Carbon::now()
                                                                ->addDays(rand(0, -30))
                                                                ->addMinutes(rand(-59, 59)),
                                'creator_id' => rand(1, 2),
                                'modifier_id' => rand(1, 2),
                                'status' => Article::STATUS_FINAL,
                            ]);

                            foreach (\Arr::random($homeColumns, rand(1, 4)) as $homeColumn) {
                                if ($homeColumn == 5 || $homeColumn == 6) {
                                    continue;
                                }

                                if ($homeColumn == 7) {
                                    $homeCategory = \Kanchangzhou\HomePage\Models\HomeCategory::inRandomOrder()
                                                                                              ->first();
                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn, $homeCategory->id);
                                } else {
                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn);
                                }
                            }

                            break;
                        case ArticleCategory::TYPE_VIDEO:
                            $thumbnails = [
                                [
                                    'src' => 'https://dummyimage.com/750x422',
                                ],
                            ];

                            $materials = [
                                'video' => [
                                    'title' => $faker->sentence,
                                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/uploads/20191023/ca3e2d6db8d445e97b101790d2e214c6.mp4',
                                ],
                            ];

                            $article = Article::create([
                                'uuid' => $faker->uuid,
                                'article_category_id' => $category->id,
                                'title' => $faker->sentence(rand(8, 12)),
                                'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
                                'summary' => $faker->sentences(rand(2, 4), true),
                                'content' => $faker->paragraphs(rand(3, 8), true),
                                'type' => Article::TYPE_VIDEO,
                                'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
                                'thumbnails' => $thumbnails,
                                'materials' => $materials,
                                'redirect_to' => '',
                                'author' => $faker->name,
                                'can_comment' => rand(1, 2),
                                'virtual_likes' => rand(20, 200),
                                'virtual_views' => rand(100, 800),
                                'true_views' => rand(100, 300),
                                'published_at' => \Carbon\Carbon::now()
                                                                ->addDays(rand(0, -30))
                                                                ->addMinutes(rand(-59, 59)),
                                'creator_id' => rand(1, 2),
                                'modifier_id' => rand(1, 2),
                                'status' => Article::STATUS_FINAL,
                            ]);

                            foreach (\Arr::random($homeColumns, rand(1, 3)) as $homeColumn) {
                                if ($homeColumn == 5 || $homeColumn == 6) {
                                    continue;
                                }
                                if ($homeColumn == 7) {
                                    $homeCategory = \Kanchangzhou\HomePage\Models\HomeCategory::inRandomOrder()
                                                                                              ->first();
                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn, $homeCategory->id);
                                } else {
                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn);
                                }
                            }

                            \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, 5);

                            break;
                        case ArticleCategory::TYPE_MINIVIDEO:
                            $thumbnails = [
                                [
                                    'src' => 'https://dummyimage.com/320x500',
                                ],
                            ];

                            $materials = [
                                'minivideo' => [
                                    'title' => $faker->sentence,
                                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/1569309388489VID_20190924_151620.mp4',
                                ],
                            ];

                            $article = Article::create([
                                'uuid' => $faker->uuid,
                                'article_category_id' => $category->id,
                                'title' => $faker->sentence(rand(8, 12)),
                                'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
                                'summary' => $faker->sentences(rand(2, 4), true),
                                'content' => $faker->paragraphs(rand(3, 8), true),
                                'type' => Article::TYPE_MINIVIDEO,
                                'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
                                'thumbnails' => $thumbnails,
                                'materials' => $materials,
                                'redirect_to' => '',
                                'author' => $faker->name,
                                'can_comment' => rand(1, 2),
                                'virtual_likes' => rand(20, 200),
                                'virtual_views' => rand(100, 800),
                                'true_views' => rand(100, 300),
                                'published_at' => \Carbon\Carbon::now()
                                                                ->addDays(rand(0, -30))
                                                                ->addMinutes(rand(-59, 59)),
                                'creator_id' => rand(1, 2),
                                'modifier_id' => rand(1, 2),
                                'status' => Article::STATUS_FINAL,
                            ]);

//                            foreach (\Arr::random($homeColumns, rand(1, 3)) as $homeColumn) {
//                                if ($homeColumn == 5 || $homeColumn == 7) {
//                                    continue;
//                                }
//                                if ($homeColumn == 7) {
//                                    $homeCategory = \Kanchangzhou\HomePage\Models\HomeCategory::inRandomOrder()
//                                                                                              ->first();
//                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn, $homeCategory->id);
//                                } else {
//                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn);
//                                }
//                            }

                            \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, 6);

                            break;
                        case ArticleCategory::TYPE_GALLERY:
                            $thumbnails = [
                                [
                                    'src' => 'https://dummyimage.com/750x422',
                                ],
                            ];

                            $materials = [
                                'gallery' => [
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery1',
                                    ],
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery2',
                                    ],
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery3',
                                    ],
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery4',
                                    ],
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery5',
                                    ],
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery6',
                                    ],
                                    [
                                        'title' => $faker->sentence,
                                        'src' => 'https://dummyimage.com/750x422?text=gallery7',
                                    ],
                                ],
                            ];

                            $article = Article::create([
                                'uuid' => $faker->uuid,
                                'article_category_id' => $category->id,
                                'title' => $faker->sentence(rand(8, 12)),
                                'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
                                'summary' => $faker->sentences(rand(2, 4), true),
                                'content' => $faker->paragraphs(rand(3, 8), true),
                                'type' => Article::TYPE_GALLERY,
                                'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
                                'thumbnails' => $thumbnails,
                                'materials' => $materials,
                                'redirect_to' => '',
                                'author' => $faker->name,
                                'can_comment' => rand(1, 2),
                                'virtual_likes' => rand(20, 200),
                                'virtual_views' => rand(100, 800),
                                'true_views' => rand(100, 300),
                                'published_at' => \Carbon\Carbon::now()
                                                                ->addDays(rand(0, -30))
                                                                ->addMinutes(rand(-59, 59)),
                                'creator_id' => rand(1, 2),
                                'modifier_id' => rand(1, 2),
                                'status' => Article::STATUS_FINAL,
                            ]);

                            foreach (\Arr::random($homeColumns, rand(1, 3)) as $homeColumn) {
                                if ($homeColumn == 5 || $homeColumn == 6) {
                                    continue;
                                }
                                if ($homeColumn == 7) {
                                    $homeCategory = \Kanchangzhou\HomePage\Models\HomeCategory::inRandomOrder()
                                                                                              ->first();
                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn, $homeCategory->id);
                                } else {
                                    \Kanchangzhou\HomePage\Facades\HomePageFacade::pushToHomePage($article, $homeColumn);
                                }
                            }
                            break;
                        case ArticleCategory::TYPE_AUDIO:

                            break;
                        case ArticleCategory::TYPE_SPECIAL:
                            if ($j > 3) {
                                break;
                            }

                            // 创建专题
                            $special = \Kanchangzhou\Article\Models\ArticleSpecial::create([
                                'title' => $faker->sentence,
                                'summary' => $faker->sentences(3, true),
                                'thumbnails' => [
                                    [
                                        'src' => 'https://dummyimage.com/750x211',
                                    ],
                                ],
                                'share_image' => 'https://dummyimage.com/200x200?text=share',
                                'content' => $faker->paragraph,
                                'can_comment' => \Kanchangzhou\Article\Models\ArticleSpecial::COMMENT_CAN,
                                'type' => \Kanchangzhou\Article\Models\ArticleSpecial::TYPE_ARTICLE,
                                'status' => \Kanchangzhou\Article\Models\ArticleSpecial::STATUS_FINAL,
                                'published_at' => \Carbon\Carbon::now(),
                                'creator_id' => rand(1, 2),
                                'modifier_id' => rand(1, 2),
                            ]);

                            $special->categories()
                                    ->createMany([
                                        ['title' => $faker->word()],
                                        ['title' => $faker->word()],
                                        ['title' => $faker->word()],
                                    ]);

                            $articles = Article::whereIn('type', [
                                Article::TYPE_NORMAL,
                                Article::TYPE_VIDEO,
                                Article::TYPE_GALLERY,
                            ])
                                               ->inRandomOrder()
                                               ->take(rand(4, 9))
                                               ->get();

                            $special->articles()
                                    ->attach($articles);

                            break;
                    }
                }
            }
        }
    }
}
