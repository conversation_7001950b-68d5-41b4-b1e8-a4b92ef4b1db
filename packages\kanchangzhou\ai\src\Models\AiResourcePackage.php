<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiResourcePackage
 *
 * @property int $id
 * @property string $name 资源包名称
 * @property string|null $description
 * @property string $type
 * @property string|null $provider AI服务提供商
 * @property string|null $model_type 模型类型
 * @property string|null $model_id 模型ID
 * @property int $is_general 是否通用
 * @property int $total_tokens 总token数
 * @property int $expired_days 过期天数
 * @property int $status
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $is_general_str
 * @property-read mixed $status_str
 * @property-read mixed $type_str
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereExpiredDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereIsGeneral($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereTotalTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiResourcePackage withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiResourcePackage
 */
class AiResourcePackage extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [
        'type_str',
        'status_str',
        'is_general_str',
    ];

    const STATUS_INVALID = 1;
    const STATUS_VALID = 2;

    const STATUS_MAP = [
        self::STATUS_INVALID => '无效',
        self::STATUS_VALID => '有效',
    ];

    const TYPE_CHAT = ChatModel::MODEL_TYPE_CHAT;
    const TYPE_IMAGE = ChatModel::MODEL_TYPE_IMAGE;
    const TYPE_VIDEO = ChatModel::MODEL_TYPE_VIDEO;
    const TYPE_AUDIO = ChatModel::MODEL_TYPE_AUDIO;

    const MODEL_TYPE_MAP = [
        self::TYPE_CHAT => '聊天',
        self::TYPE_IMAGE => '图片',
        self::TYPE_VIDEO => '视频',
        self::TYPE_AUDIO => '音频',
    ];

    const IS_GENERAL_NO=1;
    const IS_GENERAL_YES=2;

    const IS_GENERAL_MAP = [
        self::IS_GENERAL_NO => '否',
        self::IS_GENERAL_YES => '是',
    ];

    public function getTypeStrAttribute() {
        return self::MODEL_TYPE_MAP[$this->type] ?? '';
    }

    public function getStatusStrAttribute() {
        return self::STATUS_MAP[$this->status] ?? '';
    }

    public function getIsGeneralStrAttribute() {
        return self::IS_GENERAL_MAP[$this->is_general] ?? '';
    }
}
