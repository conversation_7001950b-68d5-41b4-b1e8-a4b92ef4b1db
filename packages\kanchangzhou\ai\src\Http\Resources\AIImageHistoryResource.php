<?php
namespace Kanchangzhou\Activity\Http\Admin\Resources;
namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\Kernel\Supports\Units;
use Kanchangzhou\Kernel\Supports\Redirectable;

class AIImageHistoryResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
        ];
    }
}
