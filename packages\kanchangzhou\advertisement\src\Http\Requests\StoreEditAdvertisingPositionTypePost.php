<?php

namespace Kanchangzhou\Advertisement\Http\Requests;

use Kanchangzhou\Advertisement\Exceptions\AdException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;

class StoreEditAdvertisingPositionTypePost extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "position_type_name"=>"required|max:50|min:3",
        ];
    }

    public function messages()
    {
        return [
            "position_type_name.required"=>"广告位类型名称必填",
            "position_type_name.max"=>"广告位类型名称最长为50个字符",
            "position_type_name.min"=>"广告位类型名称最短为3个字符",

        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw(new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $validator->errors()));
    }
}
