<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiNer
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $entity 词
 * @property string|null $type 词性
 * @property string|null $begin 起始时间
 * @property string|null $end 结束时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $type_name
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereBegin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereEntity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNer whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiNer
 */
class MediaAiNer extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'type_name',
    ];

    // type词语属性: 时间：TIME 组织：ORG 姓名：NAME 职务：JOB 地点：LOC 事件：ACT 地标：LMK 品牌：PRD
    const TYPE_TIME = 'TIME';
    const TYPE_ORG = 'ORG';
    const TYPE_NAME = 'NAME';
    const TYPE_JOB = 'JOB';
    const TYPE_LOC = 'LOC';
    const TYPE_ACT = 'ACT';
    const TYPE_LMK = 'LMK';
    const TYPE_PRD = 'PRD';

    public static $typeMap = [
        self::TYPE_TIME => '时间',
        self::TYPE_ORG => '组织',
        self::TYPE_NAME => '姓名',
        self::TYPE_JOB => '职务',
        self::TYPE_LOC => '地点',
        self::TYPE_ACT => '事件',
        self::TYPE_LMK => '地标',
        self::TYPE_PRD => '品牌',
    ];

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }

    public function getTypeNameAttribute() {
        return self::$typeMap[$this->type] ?? '';
    }
}
