<?php


namespace Kanchangzhou\Administrator\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Exceptions\AdminUserException;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ModelPermissionController extends BaseController
{
    public function formOptions() {
        $targetAliases = config('kadmin.permission_models_map');

        return Respond::respondWithData(compact('targetAliases'));
    }

    public function index(Request $request, $adminId, $targetAlias) {
        PermissionHook::can('权限.列表');

        $adminUser = AdminUser::where('id', $adminId)
                              ->firstOrFail();
        $targetModelClass = config('kadmin.permission_models_map.' . $targetAlias . '.model');

        if (!$targetModelClass) {
            throw (new AdminUserException())->setStatusCode(400)
                                            ->setErrMsg('无此独立权限类别');
        }

        $targetModels = $adminUser->targetModels($targetModelClass)
                                  ->paginate(20);

        return Respond::respondWithData(JsonResource::collection($targetModels));
    }

    public function update(Request $request, $adminId, $targetAlias) {
        PermissionHook::can('权限.更新', $adminId);

        $adminUser = AdminUser::where('id', $adminId)
                              ->firstOrFail();

        $targetModelClass = config('kadmin.permission_models_map.' . $targetAlias . '.model');

        if (!$targetModelClass) {
            throw (new AdminUserException())->setStatusCode(400)
                                            ->setErrMsg('无此独立权限类别');
        }

        $targetModel = new $targetModelClass;

        try {
            $this->validate($request, [
                'target_id' => [
                    'required',
                    function ($attr, $value, $fail) use ($targetModel) {
                        if ($targetModel->where($targetModel->getKeyName(), $value)
                                        ->doesntExist()) {
                            return $fail('添加的信息不存在');
                        }
                    },
                ],
            ], []);

        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        $adminUser->targetModels($targetModel)
                  ->syncWithoutDetaching($request->input('target_id'));

        return Respond::respondWithData();
    }

    public function destroy(Request $request, $adminId, $targetAlias) {
        PermissionHook::can('权限.删除',$adminId);

        $adminUser = AdminUser::where('id', $adminId)
                              ->firstOrFail();

        $targetModelClass = config('kadmin.permission_models_map.' . $targetAlias . '.model');

        if (!$targetModelClass) {
            throw (new AdminUserException())->setStatusCode(400)
                                            ->setErrMsg('无此独立权限类别');
        }

        $targetModel = new $targetModelClass;

        try {
            $this->validate($request, [
                'target_id' => [
                    'required',
                ],
            ], []);

        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        $adminUser->targetModels($targetModel)
                  ->detach($request->input('target_id'));

        return Respond::respondWithData();
    }

    public function checkChosen(Request $request, $adminId, $targetAlias) {
        $adminUser = AdminUser::where('id', $adminId)
                              ->firstOrFail();

        $targetModelClass = config('kadmin.permission_models_map.' . $targetAlias . '.model');

        if (!$targetModelClass) {
            throw (new AdminUserException())->setStatusCode(400)
                                            ->setErrMsg('无此独立权限类别');
        }

        $chosen = [];

        if ($request->input('ids')) {
            $ids = $request->input('ids');
            if (is_string($request->input('ids'))) {
                $ids = [$request->input('ids')];
            }

            $chosen = $adminUser->targetModels($targetModelClass)
                                ->whereIn('target_id', $ids)
                                ->get();
        }

        return Respond::respondWithData(JsonResource::collection($chosen));
    }
}
