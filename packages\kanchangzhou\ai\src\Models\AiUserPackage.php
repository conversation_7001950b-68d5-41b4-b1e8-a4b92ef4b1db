<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\AiUserPackage
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $guard_name
 * @property int $total_tokens
 * @property int $used_tokens
 * @property string $package_type 套餐类型
 * @property string|null $provider AI服务提供商
 * @property string|null $model_type 模型类型
 * @property string|null $model_id 模型ID
 * @property int $is_general 是否通用
 * @property int $order_id 订单ID
 * @property \Illuminate\Support\Carbon $expired_at 过期时间
 * @property int $resource_package_id
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $is_general_str
 * @property-read mixed $package_type_str
 * @property-read \Kanchangzhou\AI\Models\AiOrder|null $order
 * @property-read \Kanchangzhou\AI\Models\AiResourcePackage|null $resourcePackage
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereIsGeneral($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage wherePackageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereResourcePackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereTotalTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereUsedTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiUserPackage whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAiUserPackage
 */
class AiUserPackage extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'package_type_str',
        'is_general_str',
    ];

    protected $casts = [
        'expired_at' => 'datetime',
    ];

    const PACKAGE_TYPE_CHAT = AiResourcePackage::TYPE_CHAT;
    const PACKAGE_TYPE_IMAGE = AiResourcePackage::TYPE_IMAGE;
    const PACKAGE_TYPE_VIDEO = AiResourcePackage::TYPE_VIDEO;
    const PACKAGE_TYPE_AUDIO = AiResourcePackage::TYPE_AUDIO;

    const PACKAGE_TYPE_MAP = [
        self::PACKAGE_TYPE_CHAT => '聊天',
        self::PACKAGE_TYPE_IMAGE => '图片',
        self::PACKAGE_TYPE_VIDEO => '视频',
        self::PACKAGE_TYPE_AUDIO => '音频',
    ];

    const IS_GENERAL_NO=1;
    const IS_GENERAL_YES=2;

    const IS_GENERAL_MAP = [
        self::IS_GENERAL_NO => '否',
        self::IS_GENERAL_YES => '是',
    ];


    public function resourcePackage() {
        return $this->belongsTo(AiResourcePackage::class, 'resource_package_id');
    }

    public function order() {
        return $this->belongsTo(AiOrder::class, 'order_id');
    }

    public function getPackageTypeStrAttribute() {
        return self::PACKAGE_TYPE_MAP[$this->package_type] ?? '';
    }

    public function getIsGeneralStrAttribute() {
        return self::IS_GENERAL_MAP[$this->is_general] ?? '';
    }
}
