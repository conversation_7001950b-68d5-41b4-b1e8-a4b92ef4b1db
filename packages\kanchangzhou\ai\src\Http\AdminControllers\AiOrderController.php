<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\AI\Models\AiOrder;
use Kanchangzhou\Kernel\Supports\Respond;

class AiOrderController extends BaseController
{
    public function index(Request $request) {
        $orders = AiOrder::where('guard_name', 'kadmin')
                         ->when($request->input('status'), function ($query, $status) {
                             $query->where('status', $status);
                         })
                         ->when($request->input('user_id'), function ($query, $userId) {
                             $query->where('user_id', $userId);
                         })
                         ->when($request->input('order_no'), function ($query, $orderNo) {
                             $query->where('order_no', $orderNo);
                         })
                         ->when($request->input('order_type'), function ($query, $orderType) {
                             $query->where('order_type', $orderType);
                         })
                         ->when($request->input('created_start_at'), function ($query, $createdStartAt) {
                             $query->where('created_at', '>=', $createdStartAt);
                         })
                         ->when($request->input('created_end_at'), function ($query, $createdEndAt) {
                             $query->where('created_at', '<=', $createdEndAt);
                         })
                         ->when($request->input('paid_start_at'), function ($query, $paidStartAt) {
                             $query->where('paid_at', '>=', $paidStartAt);
                         })
                         ->when($request->input('paid_end_at'), function ($query, $paidEndAt) {
                             $query->where('paid_at', '<=', $paidEndAt);
                         })
                         ->orderByDesc('id')
                         ->paginate();

        return Respond::respondWithData(JsonResource::collection($orders));
    }

    public function show($orderSn) {
        $order = AiOrder::with('items')
                        ->where('order_no', $orderSn)
                        ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($order));
    }

    public function formOptions() {
        $status = AiOrder::STATUS_MAP;
        $orderTypes = AiOrder::ORDER_TYPE_MAP;
        $paymentMethods = AiOrder::PAYMENT_METHOD_MAP;

        return Respond::respondWithData(compact('status', 'orderTypes', 'paymentMethods'));
    }

    public function myOrders(Request $request) {
        $orders = AiOrder::where('user_id', AuthFacade::adminUser()
                                                      ->getId())
                         ->where('guard_name', 'kadmin')
                         ->when($request->input('status'), function ($query, $status) {
                             $query->where('status', $status);
                         })
                         ->when($request->input('order_no'), function ($query, $orderNo) {
                             $query->where('order_no', $orderNo);
                         })
                         ->when($request->input('order_type'), function ($query, $orderType) {
                             $query->where('order_type', $orderType);
                         })
                         ->when($request->input('created_start_at'), function ($query, $createdStartAt) {
                             $query->where('created_at', '>=', $createdStartAt);
                         })
                         ->when($request->input('created_end_at'), function ($query, $createdEndAt) {
                             $query->where('created_at', '<=', $createdEndAt);
                         })
                         ->when($request->input('paid_start_at'), function ($query, $paidStartAt) {
                             $query->where('paid_at', '>=', $paidStartAt);
                         })
                         ->when($request->input('paid_end_at'), function ($query, $paidEndAt) {
                             $query->where('paid_at', '<=', $paidEndAt);
                         })
                         ->orderByDesc('id')
                         ->paginate();

        return Respond::respondWithData(JsonResource::collection($orders));
    }


    public function myOrderShow($orderSn) {
        $order = AiOrder::with('items')
                        ->where('order_no', $orderSn)
                        ->where('user_id', AuthFacade::adminUser()
                                                     ->getId())
                        ->where('guard_name', 'kadmin')
                        ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($order));
    }
}
