<?php

namespace Kanchangzhou\AI\Http\Controllers;

use App\Events\TalkToTalk;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\AI\Exceptions\AiAudioException;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AiOrderException;
use Kanchangzhou\AI\Exceptions\MediaAiException;
use Kanchangzhou\AI\Models\ChatApiKey;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Services\Audio\AiAudioTtsManager;
use Kanchangzhou\AI\Services\PPT\Providers\Xunfei\Auth;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ChatController extends BaseController
{
    public function streamChat(Request $request) {
        ini_set('output_buffering', 'off');
        while (@ob_end_flush()) ;

        try {
            $this->validate($request, [
                'prompt' => 'required_unless:is_retry,1',
                'chat_uuid' => 'required_if:is_retry,1',
                'is_retry' => '',
            ], [
                'prompt.required_unless' => '输入内容不能为空',
                'chat_uuid.required_if' => '对话历史参数不正确',
            ]);
        } catch (ValidationException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => ValidateFailedException::VALIDATION_ERROR,
                        'message' => $e->validator->errors()
                                                  ->first(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        } catch (AIException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }

//        $sensitiveWords = SensitiveWordsHook::search($request->input('prompt'), SensitiveWordsHook::SCENES_ALL);
//
//        if (($sensitiveWords['suggestion'] ?? '') == 'block') {
//            return response()->stream(function () use ($sensitiveWords) {
//                echo "id:" . time() . PHP_EOL;
//                echo "event:error" . PHP_EOL;
//                echo "data: " . json_encode([
//                        'error_code' => MediaAiException::HAS_SENSITIVE,
//                        'message' => '根据相关法律法规和政策，无法为您提供服务。(文本中可能包含敏感词:"' . $sensitiveWords['words'][0]['context'] . '"))',
//                    ]) . PHP_EOL . PHP_EOL;
//                flush();
//            }, 200, [
//                'Cache-Control' => 'no-cache',
//                'Content-Type' => 'text/event-stream',
//                'X-Accel-Buffering' => 'no',
//            ]);
//        }

        $chatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                              ->where('status', ChatModel::STATUS_VALID)
                              ->first();

        if (!$chatModel) {
            return response()->stream(function () {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => MediaAiException::MISS_INTERFACE,
                        'message' => '缺少服务提供者',
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }

        try {
//            ChatLimit::checkLimit($chatModel->model_type, $chatModel->provider, $chatModel->model_id, AuthFacade::user()
//                                                                                                                ->getId());
        } catch (AIException|AiOrderException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);

        }

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatModel->provider];

        $aiUser = new AiUser(AuthFacade::user()
                                       ->getId(), 'kuser');

        $service = new $serviceProvider([
            'prompt' => $request->input('prompt'),
            'chat_uuid' => $request->input('chat_uuid'),
            'role_id' => $request->input('role_id'),
            'role_step' => $request->input('step'),
            'role_step_input' => $request->input('step_input'),
            'is_retry' => $request->input('is_retry'),
        ], $chatModel, $aiUser);

        return response()->stream(function () use ($service) {
            $service->streamHandle();
        }, 200, [
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'text/event-stream',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    public function chat(Request $request) {
        $this->validate($request, [
            'prompt' => 'required',
        ]);


        $chatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                              ->where('status', ChatModel::STATUS_VALID)
                              ->first();

        if (!$chatModel) {
            return false;
        }

        try {
//            ChatLimit::checkLimit($chatModel->model_type, $chatModel->provider, $chatModel->model_id, AuthFacade::user()
//                                                                                                                ->getId());
        } catch (AIException|AiOrderException $e) {
            return false;
        }

        $key = $request->header('XW-Api-Key');
        $user = ChatApiKey::where('key', $key)
                          ->first();

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatModel->provider];

        $aiUser = new AiUser(\Illuminate\Support\Facades\Auth::guard($user->guard_name)
                                                             ->user()
                                                             ->getId(), $user->guard_name);

        $service = new $serviceProvider([
            'prompt' => $request->input('prompt'),
            'chat_uuid' => $request->input('chat_uuid'),
            'role_id' => $request->input('role_id'),
            'role_step' => $request->input('step'),
            'role_step_input' => $request->input('step_input'),
            'is_retry' => $request->input('is_retry'),
        ], $chatModel, $aiUser);

        $res = $service->handle();

        return Respond::respondWithData(JsonResource::make($res));
    }

    public function audioChat(Request $request) {
        ini_set('output_buffering', 'off');
        while (@ob_end_flush()) ;

        try {
            $this->validate($request, [
                'audio' => 'required|file',
            ], [
                'audio.required' => '请说话',
                'audio.file' => '音频文件格式不正确',
                'audio.mimes' => '音频文件格式不正确',
            ]);
        } catch (ValidationException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => ValidateFailedException::VALIDATION_ERROR,
                        'message' => $e->validator->errors()
                                                  ->first(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        } catch (AIException $e) {
            return response()->stream(function () use ($e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }, 200, [
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'text/event-stream',
                'X-Accel-Buffering' => 'no',
            ]);
        }

        return response()->stream(function () use ($request) {
            try {
                // 语音转文字
                $client = (new AiAudioTtsManager())->client('aliyun')
                                                   ->asrToText($request->file('audio')
                                                                       ->getContent());

//        // 获取音频内容
                $resText = $client->json('result');
            } catch (\Exception $e) {
                echo "id:" . time() . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => AiAudioException::FAILED_ASR,
                        'message' => '音频转文字失败',
                    ]) . PHP_EOL . PHP_EOL;
                flush();
            }

            // 调用模型的聊天方法
            $body = [
                'model' => 'qwen-max',
                'input' => [
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => '你是一个智能语音聊天机器人, 所有的回复内容尽量保持口语化, 请勿涉及政治、色情、暴力等违法违规内容。',
                        ],
                        [
                            'role' => 'user',
                            'content' => $resText,
                        ],
                    ],
                ],
                'parameters' => [
                    'result_format' => 'message',
                    'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                    'temperature' => 0.85,
                    'repetition_penalty' => 1.0,
                    'enable_search' => true,
                    'incremental_output' => true,

                ],
            ];

            $ttsClient = (new AiAudioTtsManager())->client('aliyun')
                                                  ->setRequestConfig([
                                                      'voice' => 'zhixiaoxia',
                                                      'format' => 'pcm',
                                                      'sample_rate' => 16000,
                                                      'volume' => 50,
                                                      'speech_rate' => 0,
                                                      'pitch_rate' => 0,
                                                  ]);

            $tempstr = '';
            $originstr = [];
            $changedstr = [];
            $response = Http::withHeaders([
                'X-DashScope-SSE' => 'enable',
                'Cache-Control' => 'no-cache',
                'Content-Type' => 'application/json',
                'Accept' => 'text/event-stream',
            ])
                            ->withToken(config('kai.chat.providers.aliyun.api_key'))
                            ->withOptions([
                                'curl' => [
                                    CURLOPT_WRITEFUNCTION => function ($curl, $data) use ($ttsClient, &$tempstr, &$originstr, &$changedstr) {
                                        preg_match('/data:(.*)/', $data, $matches);
                                        $dataStr = $matches[1];
                                        $resData = json_decode($dataStr, true);

                                        if (isset($resData['code'])) {
                                            echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                            echo "event:error" . PHP_EOL;
                                            echo "data:" . json_encode([
                                                    'time' => microtime(true),
                                                    'message' => __('ai::ai.' . $resData['message']),
                                                    'code' => $resData['code'],
                                                ]) . PHP_EOL . PHP_EOL;
                                            flush();

                                            return strlen($data);
                                        }

                                        $messageText = $resData['output']['choices'][0]['message']['content'];
                                        if (empty($messageText)) {
                                            return strlen($data);
                                        }
                                        $originstr[] = $messageText;
                                        $tempstr .= $messageText;
                                        // 检查临时字符串是否包含句子结束标点
                                        if (preg_match("/[，。？！]/u", $tempstr)) {
                                            // 分割临时字符串成完整句子
                                            $sentences = preg_split("/(?<=[，。？！])\s*/u", $tempstr, -1, PREG_SPLIT_NO_EMPTY);
                                            // 保证最后一个部分可能不是完整句子，暂时保留在 $tempString
                                            if (count($sentences) > 1) {
                                                $tempstr = array_pop($sentences);
                                                // 将完整句子添加到新数组
                                                $changedstr = array_merge($changedstr, $sentences);

                                                $ttsData = $ttsClient->textToSpeechStream($sentences[0]);

                                                echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                                echo "event:audio" . PHP_EOL;
                                                echo "data:" . json_encode([
                                                        'time' => microtime(true),
                                                        'message' => base64_encode($ttsData),
                                                    ]) . PHP_EOL . PHP_EOL;
                                                flush();
//
//                                            broadcast(new TalkToTalk(base64_encode($ttsData)));
//
//                                                $fileName = 'audio_' . microtime(true) . '.mp3';
//                                                $filePath = 'audio/' . $fileName;
//
//                                                // 保存文件到存储中 (可以是本地存储，S3 等等)
//                                                \Storage::disk('local')
//                                                        ->put($filePath, $ttsData);


                                            } else {
                                                // 仅一个句子，保留在 $tempString
                                                $tempstr = $sentences[0];
                                            }
                                        }

                                        return strlen($data);
                                    },
                                ],
                            ])
                            ->post('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', $body);

            if (!empty($tempstr)) {
                $changedstr[] = $tempstr;

                $ttsData = $ttsClient->textToSpeechStream($tempstr);

                echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                echo "event:audio" . PHP_EOL;
                echo "data:" . json_encode([
                        'time' => microtime(true),
                        'message' => base64_encode($ttsData),
                    ]) . PHP_EOL . PHP_EOL;
                flush();
//
//            broadcast(new TalkToTalk(base64_encode($ttsData)));
//
//                $fileName = 'audio_' . microtime(true) . '.pcm';
//                $filePath = 'audio/' . $fileName;
//
//                // 保存文件到存储中 (可以是本地存储，S3 等等)
//                \Storage::disk('local')
//                        ->put($filePath, $ttsData);

            }

        }, 200, [
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'text/event-stream',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
