<?php

return [
    'serve' => [
        'is_local' => true,
        'online_serve' => '',
    ],

    'route' => [
        'middleware' => [
            'no_auth' => ['api'],
            'should_auth' => [
                'api',
                'auth.kadmin',
            ],
            'admin' => [
                'api',
                'auth.kadmin',
            ],
        ],
        'namesapce' => 'Kanchangzhou\\Administrator\\Http\\Controllers',
    ],

    'auth' => [
        'guard' => 'kadmin',

        'guards' => [
            'kadmin' => [
                'driver' => 'jwt',
                'provider' => 'kadmins',
            ],
        ],

        'providers' => [
            'kadmins' => [
                'driver' => 'eloquent',
                'model' => Kanchangzhou\Administrator\Models\AdminUser::class,
            ],
        ],
    ],
    'permission_models_map' => [],

    'auth_safe' => [
        'enable' => env('AUTH_SAFE_ENABLE', true),
        'pwd_expired' => env('AUTH_SAFE_PWD_EXPIRED', 90),
        'login_fail_lock' => env('AUTH_SAFE_LOGIN_FAIL_LOCK', true),
        'login_fail_times' => env('AUTH_SAFE_LOGIN_FAIL_TIMES', 5),
        'login_fail_lock_sec' => env('AUTH_SAFE_LOGIN_FAIL_LOCK_SEC', 10 * 60),
        'login_timeout' => env('AUTH_SAFE_LOGIN_TIMEOUT', 2 * 60 * 60),
    ],
];
