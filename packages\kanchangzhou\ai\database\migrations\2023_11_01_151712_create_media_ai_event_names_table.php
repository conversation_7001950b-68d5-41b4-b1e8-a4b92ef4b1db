<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiEventNamesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_ai_event_names', function (Blueprint $table) {
            $table->id();
            $table->integer('media_ai_file_id')->comment('文件ID');
            $table->string('name')->comment('事件名称')
                                  ->nullable();
            $table->string('type')->comment('事件类型')
                                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_ai_event_names');
    }
}
