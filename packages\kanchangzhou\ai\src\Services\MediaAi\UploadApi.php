<?php

namespace Kanchangzhou\AI\Services\MediaAi;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class UploadApi
{
    // 上传文件, 参数为 远程地址 ,如 https://www.baidu.com/img/bd_logo1.png, 通过 api/upload 接口, 使用 form-data 方式上传到文件服务器
    /**
     * @param $url
     *
     * @return \GuzzleHttp\Promise\PromiseInterface|\Illuminate\Http\Client\Response
     */
    public function remoteUpload($url) {
        $file = fopen($url, 'r');

        return Http::attach('file', $file)
                   ->post(config('kai.other.media_ai.host') . '/api/upload?totalapi=' . config('kai.other.media_ai.secret_key'));

    }

}
