<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpEventnamesJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpKeywordsJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpNerJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpNewsclassificationJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpSummaryJob;
use Kanchangzhou\AI\Models\MediaAiAsrAccurate;
use Kanchangzhou\AI\Models\MediaAiFile;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiAsrAccurateJob extends MediaAiBaseJob
{

    // data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":{"module":"asr_accurate"},"guid":"5040fc173f604912afdf1968c44154a6","subDataTypes":[{"type":"asr/accurate","source":"腾讯","version":null}],"asr/accurate":[{"fileId":"123123321321","statusCode":0,"statusInfo":"success","contents":[{"sentence":"在中国羽毛球公开赛的比赛间隙，国家羽毛球队队员们还走进校园，为常州小球迷开启了小灶。9月8号下午，中国羽毛球协会主席张军和曾获东京奥运会混合双打冠军的黄东平、全英公开赛男单冠军李诗峰以及蒋正邦、王芷怡四名国家羽毛球队队员走进江苏省羽毛球传统校、羽毛球特色学校新北区河海实验小学。","begin":2600000,"end":271600000},{"sentence":"国语队员与小球员热情互动，切磋球技，比赛现场充满欢乐，从来没有见过就是世界冠军就出现在我的眼前，跟他们互动结束之后，我就拿着我的衣服跑上去，一个一个问球员要签名，非常的开心激动了，经验和手法都比我们多，我要和他们一样，一起刻苦学习羽毛球，站到更高的舞台，拿到金牌。中国羽毛球队还向学校捐赠了羽毛球器材，副市长蒋鹏举参加活动，希望我能更多孩子可以看到我们队的世界冠军，让他从小有榜样的作用，然然后通过自身的努力加入到羽毛球的行列中。河海实验小学正在开展以羽毛球为抓手的三加二体育课程改革，通过实施羽毛球校本课程，举办羽毛球文化节等活动，推广和普及羽球运动。羽毛球运动员的参这样一种努力、拼搏、不屈。","begin":271600000,"end":874400000},{"sentence":"最不拿的精神是我们小学生学习的最好的榜样。在众多体育项目中，羽毛球是最受常州市民欢迎的运动项目之一，目前，常州共有羽毛球省级传统学校两家，羽毛球俱乐部超90家，羽毛球运动爱好者超20万人，先后走出卢兰、蒋艳姣、沈烨3万羽毛球世界冠军。","begin":874400000,"end":1107620000}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $taskType = $this->data['data']['subDataTypes'][0]['type'];
        $guid = $this->data['data']['guid'];
        $sentences = $this->data['data']['asr/accurate'][0]['contents'];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();

        if (!$task) {
            return;
        }

        $allContent = '';
        MediaAiAsrAccurate::where('media_ai_file_id', $task->media_ai_file_id)
                          ->delete();

        foreach ($sentences as $sentence) {
            MediaAiAsrAccurate::create([
                'media_ai_file_id' => $task->media_ai_file_id,
                'sentence' => $sentence['sentence'],
                'begin' => $sentence['begin'],
                'end' => $sentence['end'],
            ]);

            $allContent .= $sentence['sentence'];
        }

        $mediaAiFile = MediaAiFile::find($task->media_ai_file_id);
        $mediaAiFile->nlp_text = $allContent;
        $mediaAiFile->save();

        // 执行后续Job任务
        // 如果是音视频文件, 则合并文本信息, 再执行事件名提取关键词提取,命名实体提取,新闻文本分类,新闻摘要提取
        if ($task->mediaAiFile->media_type == MediaAiFile::MEDIA_TYPE_AUDIO || $task->mediaAiFile->media_type == MediaAiFile::MEDIA_TYPE_VIDEO) {
            MediaAiNlpEventnamesJob::dispatch($mediaAiFile);
            MediaAiNlpKeywordsJob::dispatch($mediaAiFile);
            MediaAiNlpNerJob::dispatch($mediaAiFile);
            MediaAiNlpNewsclassificationJob::dispatch($mediaAiFile);
            MediaAiNlpSummaryJob::dispatch($mediaAiFile);
        }

        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
