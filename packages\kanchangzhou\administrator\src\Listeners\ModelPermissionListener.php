<?php


namespace Kanchangzhou\Administrator\Listeners;


use Kanchangzhou\Kernel\Events\ModelPermissionEvent;

class ModelPermissionListener
{
    public function __construct() {

    }

    public function handle(ModelPermissionEvent $event) {
        $targetModelClass = $event->getTargetModelClass();
        $adminUser = $event->getAdminUser();

        if ($adminUser->hasRole(1) || $adminUser->id == 1) {
            return new $targetModelClass;
        }

        if ($adminUser->targetModels($targetModelClass)
                      ->count()) {
            return $adminUser->targetModels($targetModelClass);
        }

        return new $targetModelClass;
    }
}