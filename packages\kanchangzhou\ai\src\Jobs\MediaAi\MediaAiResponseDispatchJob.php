<?php

namespace Kanchangzhou\AI\Jobs\MediaAi;

use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiAsrAccurateJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiAudioClassificationJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiFaceJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiNlpEventnamesJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiNlpKeywordsJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiNlpNerJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiNlpNewsclassificationJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiNlpSummaryJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiOcrGeneralJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiShotDetectionJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiSqaJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiSsaJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\ResponseMediaAiStaJob;
use Kanchangzhou\AI\Models\MediaAiTask;

class MediaAiResponseDispatchJob extends MediaAiBaseJob
{

    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        if ($this->data['code'] != 0) {
            MediaAiTask::where('task_id', $this->data['guid'])
                       ->update([
                           'task_status' => MediaAiTask::STATUS_FAILED,
                       ]);
            return;
        }

        $module = $this->data['data']['subDataTypes'] ? $this->data['data']['subDataTypes'][0]['type'] : "";
//        $data = $this->data['data'][$module];
        switch ($module) {
            case "asr/accurate":
                dispatch(new ResponseMediaAiAsrAccurateJob($this->data));

                return;
            case "audioClassification":
                dispatch(new ResponseMediaAiAudioClassificationJob($this->data));

                return;
            case "face1":
            case "face":
                dispatch(new ResponseMediaAiFaceJob($this->data));

                return;
            case "nlp/eventnames":
                dispatch(new ResponseMediaAiNlpEventnamesJob($this->data));

                return;
            case "nlp/keywords":
                dispatch(new ResponseMediaAiNlpKeywordsJob($this->data));

                return;
            case "nlp/ner":
                dispatch(new ResponseMediaAiNlpNerJob($this->data));

                return;
            case "nlp/newsclassification":
                dispatch(new ResponseMediaAiNlpNewsclassificationJob($this->data));

                return;
            case "nlp/summary":
                dispatch(new ResponseMediaAiNlpSummaryJob($this->data));

                return;
            case "ocr/general":
                dispatch(new ResponseMediaAiOcrGeneralJob($this->data));

                return;
            case "shot_detection":
                dispatch(new ResponseMediaAiShotDetectionJob($this->data));

                return;
            case "ssa":
                dispatch(new ResponseMediaAiSsaJob($this->data));

                return;
            case "sqa":
                dispatch(new ResponseMediaAiSqaJob($this->data));

                return;
            case "sta":
                dispatch(new ResponseMediaAiStaJob($this->data));

                return;
            default:
                if (key_exists('nlp/newsclassification', $this->data['data'])) {
                    dispatch(new ResponseMediaAiNlpNewsclassificationJob($this->data));

                    return;
                }

                if (key_exists('ocr/general', $this->data['data'])) {
                    dispatch(new ResponseMediaAiOcrGeneralJob($this->data));

                    return;
                }

                if (key_exists('nlp/ner', $this->data['data'])) {
                    dispatch(new ResponseMediaAiNlpNerJob($this->data));

                    return;
                }
        }
    }
}
