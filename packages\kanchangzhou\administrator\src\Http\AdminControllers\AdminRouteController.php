<?php


namespace Kanchangzhou\Administrator\Http\AdminControllers;


use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Models\AdminRoute;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class AdminRouteController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('菜单管理.列表');
        $tree = (new AdminRoute)->withQuery(function ($query) use ($request) {
            return $query->with([
                'roles',
                'permissions',
            ])
                         ->when($request->input('platform'), function ($query, $platform) {
                             return $query->where('platform', $platform);
                         }, function ($query) {
                             return $query->where('platform', 'admin');
                         });
        })
                                ->toTree();

        return Respond::respondWithData($tree);
    }

    public function show($id) {
        PermissionHook::can('菜单管理.详情', $id);
        $route = AdminRoute::with([
            'roles',
            'permissions',
        ])
                           ->where('id', $id)
                           ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($route));
    }

    public function store(Request $request) {
        PermissionHook::can('菜单管理.新建');
        try {
            $this->validate($request, [
                'title' => 'required',
                'route_str' => '',
                'parent_id' => '',
                'platform' => '',
                'roles' => 'nullable|array',
                'roles.*' => 'exists:roles,id',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id',
            ], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        $route = AdminRoute::create([
            'title' => $request->input('title'),
            'route_str' => $request->input('route_str'),
            'parent_id' => $request->input('parent_id', 0),
            'platform' => $request->input('platform', 'admin'),
        ]);

        $route->syncRoles($request->input('roles'));
        $route->syncPermissions($request->input('permissions'));

        return Respond::respondWithData(JsonResource::make($route));
    }

    public function update(Request $request, $id) {
        PermissionHook::can('菜单管理.更新', $id);
        $route = AdminRoute::where('id', $id)
                           ->firstOrFail();

        try {
            $this->validate($request, [
                'title' => 'required',
                'route_str' => 'required',
                'parent_id' => '',
                'platform' => '',
                'roles' => 'nullable|array',
                'roles.*' => 'exists:roles,id',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id',
            ], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        $route->title = $request->input('title');
        $route->route_str = $request->input('route_str');
        $route->parent_id = $request->input('parent_id', 0) ? $request->input('parent_id') : $route->parent_id;
        $route->platform = $request->input('platform', 'admin');
        $route->save();

        $route->syncRoles($request->input('roles'));
        $route->syncPermissions($request->input('permissions'));

        return Respond::respondWithData(JsonResource::make($route));
    }

    public function destroy($id) {
        PermissionHook::can('菜单管理.删除', $id);
        $route = AdminRoute::where('id', $id)
                           ->firstOrFail();

        $route->delete();

        return Respond::respondWithData();
    }
}
