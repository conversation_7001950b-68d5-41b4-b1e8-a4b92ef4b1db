<?php

namespace Kanchangzhou\Advertisement\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AdvertisingPositionTypeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id"=>$this->id,
            "position_type_name"=>$this->position_type_name,
//            "position_info"=>AdvertisingPositionResource::collection($this->positionInfo)
        ];
    }
}
