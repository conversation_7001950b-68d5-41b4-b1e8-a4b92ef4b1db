<?php

namespace Kanchangzhou\Administrator\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PermissionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request) {
        $l = explode('.', $this->name);
        list($group, $name) = count($l) > 1 ? $l : [
            $l[0],
            null,
        ];

        return [
            'id' => $this->id,
            'name' => $name ?? $group,
            'group' => $name ? $group : '其他',
        ];
    }
}
