<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use EasyWeChat\Kernel\Messages\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Http\Resources\MediaAiFileResource;
use Kanchangzhou\AI\Jobs\MediaAi\ArticleForMediaAIJob;
use Kanchangzhou\AI\Jobs\MediaAi\VideoForMediaAIJob;
use Kanchangzhou\AI\Models\MediaAiAsrAccurate;
use Kanchangzhou\AI\Models\MediaAiEventName;
use Kanchangzhou\AI\Models\MediaAiFace;
use Kanchangzhou\AI\Models\MediaAiFile;
use Kanchangzhou\AI\Models\MediaAiKeyword;
use Kanchangzhou\AI\Models\MediaAiNer;
use Kanchangzhou\AI\Models\MediaAiNewsclassification;
use Kanchangzhou\AI\Models\MediaAiSummary;
use Kanchangzhou\AI\Services\MediaAi\TaskApi;
use Kanchangzhou\AI\Supports\StringTools;
use Kanchangzhou\Article\Models\Article;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Livechannel\Models\Backend\Video;

class MediaAiController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('智能媒资.智能调用');

        $this->validate($request, [
            'txt' => 'required_without:path',
            'id' => 'required',
            'path' => 'required_without:txt',
            'module' => 'required|in:asrAccurate,nlpEventnames,nlpKeywords,nlpNer,nlpNewsclassification,nlpSummary,audioClassification,face,ocrGeneral,shotDetection,sqa,ssa,sta',
        ]);

        $mediaAiService = app('media_ai');

        $files = [
            [
                'txt' => StringTools::trimString($request->input('txt')),
                'id' => $request->input('id'),
                'path' => $request->input('path'),
            ],
        ];

        return $mediaAiService->{Str::camel($request->input('module'))}($files);
    }

    public function queryTask(Request $request) {
        return (new TaskApi())->getTask($request->input('task_id'));
    }

    public function articleAiData($articleId) {
        PermissionHook::can('智能媒资.文稿数据');

//        $article = Article::where('id', $articleId)
//                          ->firstOrFail();

        $mediaAiFile = MediaAiFile::where('fileaiable_id', $articleId)
                                  ->where('fileaiable_type', Article::class)
                                  ->firstOrFail();

        $keywords = $mediaAiFile->keywords;
        $ners = $mediaAiFile->ners->map(function ($ner) {
            return [
                'type' => $ner->type,
                'entity' => $ner->entity,
                'type_name' => $ner->type_name ?? '',
            ];
        })
                                  ->groupBy('type');
        $eventNames = $mediaAiFile->eventnames->map(function ($eventName) {
            return [
                'name' => $eventName->name,
                'type' => $eventName->type,
                'type_name' => $eventName->type_name ?? '',
            ];
        })
                                              ->groupBy('type');
        $summary = $mediaAiFile->summary;
        $newclass = $mediaAiFile->newsclassifications;

        return Respond::respondWithData(compact('keywords', 'ners', 'eventNames', 'summary', 'newclass'));
    }

    public function videoAiData($videoId) {
        PermissionHook::can('智能媒资.视频数据');

        $mediaAiFile = MediaAiFile::where('fileaiable_id', $videoId)
                                  ->where('fileaiable_type', Video::class)
                                  ->firstOrFail();

        $asrAccurate = $mediaAiFile->asrAccurates;

        $faces = $mediaAiFile->faces()
                             ->get()
                             ->groupBy('is_known_name')
                             ->map(function ($faces) {
                                 return $faces->groupBy('person_id')
                                              ->values()
                                              ->map(function ($faces) {
                                                  return $faces->map(function ($face) {
                                                      return [
                                                          'name' => $face->name,
                                                          'person_id' => $face->person_id,
                                                          'keyframe' => $face->keyframe,
                                                          'begin' => $face->begin,
                                                          'end' => $face->end,
                                                      ];
                                                  });
                                              });
                             });


        $keywords = $mediaAiFile->keywords;
        $ners = $mediaAiFile->ners->map(function ($ner) {
            return [
                'type' => $ner->type,
                'entity' => $ner->entity,
                'type_name' => $ner->type_name ?? '',
            ];
        })
                                  ->groupBy('type');
        $eventNames = $mediaAiFile->eventnames->map(function ($eventName) {
            return [
                'name' => $eventName->name,
                'type' => $eventName->type,
                'type_name' => $eventName->type_name ?? '',
            ];
        })
                                              ->groupBy('type');
        $summary = $mediaAiFile->summary;
        $newclass = $mediaAiFile->newsclassifications;

        return Respond::respondWithData(compact('faces', 'asrAccurate', 'keywords', 'ners', 'eventNames', 'summary', 'newclass'));
    }

    public function videoMediaAiJob($videoId) {
        PermissionHook::can('智能媒资.视频智能任务');

        dispatch(new VideoForMediaAIJob($videoId));

        return Respond::respondWithData([
            'message' => '任务已提交，请稍后查看结果',
        ]);
    }

    public function articleMediaAiJob($articleId) {
        PermissionHook::can('智能媒资.文稿智能任务');

        dispatch(new ArticleForMediaAIJob($articleId));

        return Respond::respondWithData([
            'message' => '任务已提交，请稍后查看结果',
        ]);
    }


    public function formOptions() {
        $modules = [
            [
                'value' => 'asrAccurate',
                'label' => '语音识别',
            ],
            [
                'value' => 'keywords',
                'label' => '关键词',
            ],
            [
                'value' => 'ners',
                'label' => '命名实体',
            ],
            [
                'value' => 'eventNames',
                'label' => '事件名',
            ],
            [
                'value' => 'summary',
                'label' => '文本摘要',
            ],
            [
                'value' => 'newclass',
                'label' => '新闻分类',
            ],
        ];

        return Respond::respondWithData(compact('modules'));
    }

    public function search(Request $request) {
        $this->validate($request, [
            'keyword' => 'required',
            'module' => 'required|in:asrAccurate,keywords,ners,eventNames,summary,newclass',
        ]);
        $data = null;

        switch ($request->input('module')) {
            case 'asrAccurate':
                $data = MediaAiAsrAccurate::with([
                    'mediaAiFile.fileaiable',
                ])
                                          ->where('sentence', 'like', '%' . $request->input('keyword') . '%')
                                          ->orderByDesc('media_ai_file_id')
                                          ->paginate();

                break;
            case 'keywords':
                $data = MediaAiKeyword::with([
                    'mediaAiFile.fileaiable',
                ])
                                      ->where('keyword', $request->input('keyword'))
                                      ->orderByDesc('media_ai_file_id')
                                      ->paginate();

                break;
            case 'ners':
                $data = MediaAiNer::with([
                    'mediaAiFile.fileaiable',
                ])
                                  ->where('entity', $request->input('keyword'))
                                  ->orderByDesc('media_ai_file_id')
                                  ->paginate();

                break;
            case 'eventNames':
                $data = MediaAiEventName::with([
                    'mediaAiFile.fileaiable',
                ])
                                        ->where('name', $request->input('keyword'))
                                        ->orderByDesc('media_ai_file_id')
                                        ->paginate();

                break;
            case 'summary':
                $data = MediaAiSummary::with([
                    'mediaAiFile.fileaiable',
                ])
                                      ->where('summary', 'like', '%' . $request->input('keyword') . '%')
                                      ->orderByDesc('media_ai_file_id')
                                      ->paginate();
                break;
            case 'newclass':
                $tags = explode('/', $request->input('keyword'));

                $data = MediaAiNewsclassification::with([
                    'mediaAiFile.fileaiable',
                ])
                                                 ->whereRaw('IF(catalog_a_tag="' . ($tags[0] ?? '') . '",1,0) + IF(catalog_b_tag="' . ($tags[1] ?? '') . '",2,0) + IF(catalog_c_tag="' . ($tags[2] ?? '') . '",3,0) > 0')
                                                 ->orderByRaw('IF(catalog_a_tag="' . ($tags[0] ?? '') . '",1,0) + IF(catalog_b_tag="' . ($tags[1] ?? '') . '",2,0) + IF(catalog_c_tag="' . ($tags[2] ?? '') . '",3,0) DESC')
                                                 ->orderByDesc('media_ai_file_id')
                                                 ->paginate();
                break;
        }


        return Respond::respondWithData(MediaAiFileResource::collection($data));
    }
}
