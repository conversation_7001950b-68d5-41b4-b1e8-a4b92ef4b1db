<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiFilesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('media_ai_files', function (Blueprint $table) {
            $table->id();
            $table->morphs('fileaiable');
            $table->uuid('file_id')
                  ->comment('媒体文件ID');
            $table->string('media_file_url', 1000)
                  ->nullable()
                  ->comment('媒体文件地址');
            $table->string('media_type')
                  ->default('file')
                  ->comment('媒体类型');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('media_ai_files');
    }
}
