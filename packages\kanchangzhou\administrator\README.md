# 中吴网APP-后台管理员包

### 介绍
中吴网APP的后台管理员包

#### 安装教程
- 安装package
```
composer config repositories.kanchangzhou/<NAME_EMAIL>:kanchangzhou/administrator.git

composer require kanchangzhou/administrator
```

- 可选发布`config`文件以及`migration`文件
```
php artisan vendor:publish --provider=\Kanchangzhou\Administrator\AdministratorProvider
```

- 执行Migrate操作,创建数据库
```
php artisan migrate --path=vendor\kanchangzhou\administrator\database\migrations
```

### 使用说明

#### 配置参数ENV
.env 中增加配置项
```
JWT_PUBLIC_KEY="file://../certs/rsa_public_key.pem"
JWT_PRIVATE_KEY="file://../certs/rsa_private_key.pem"
JWT_ALGO=RS256
JWT_TTL=120
```
`public_key`和`private_key`默认存到放应用根目录`/certs` 中.

#### 路由使用:

使用middleware来限定路由是否需要登录
```php
Route::middleware(config('kadmin.route.middleware.should_auth'))->group(function(){

});

// OR
// kadmin为配置文件中定义的guard
Route::middleware(['kadmin.auth'])->group(function(){

});
```

无需登录:
```php
Route::middleware(config('kadmin.route.middleware.no_auth'))->group(function(){

});
```

#### 获取登录用户基础信息
```php
// 利用提供的Facede获取基础用户信息
Kanchangzhou\Administrator\Facades\AdminFacade::user();

// OR

auth('kadmin')->user();

// OR
auth(config('kadmin.auth.guard'))->user();
```



