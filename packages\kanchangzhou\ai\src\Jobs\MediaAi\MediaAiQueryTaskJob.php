<?php

namespace Kanchangzhou\AI\Jobs\MediaAi;

use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiAsrAccurateJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiFaceJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpEventnamesJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpKeywordsJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpNerJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpNewsclassificationJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiNlpSummaryJob;
use Kanchangzhou\AI\Jobs\MediaAi\Response\{ResponseMediaAiNlpKeywordsJob,
    ResponseMediaAiNlpNerJob,
    ResponseMediaAiNlpSummaryJob,
    ResponseMediaAiNlpEventnamesJob,
    ResponseMediaAiNlpNewsclassificationJob,
    ResponseMediaAiAsrAccurateJob,
    ResponseMediaAiAudioClassificationJob,
    ResponseMediaAiFaceJob,
    ResponseMediaAiOcrGeneralJob,
    ResponseMediaAiShotDetectionJob,
    ResponseMediaAiSqaJob,
    ResponseMediaAiSsaJob,
    ResponseMediaAiStaJob
};
use Kanchangzhou\AI\Models\MediaAiTask;
use Kanchangzhou\AI\Services\MediaAi\TaskApi;

class MediaAiQueryTaskJob extends MediaAiBaseJob
{
    protected $task;

    public $tries = 4;

    /**
     * Create a new job instance.
     *
     * @param MediaAiTask $task
     *
     * @return void
     */
    public function __construct(MediaAiTask $task) {
        $this->task = $task;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        if ($this->attempts() > 3) {
            $this->task->task_status = MediaAiTask::STATUS_FAILED;
            $this->task->save();

            return;
        }

        $taskApi = new TaskApi();
        $taskRes = $taskApi->getTask($this->task->task_id);
        if ($taskRes->successful() && $taskRes->json('code') == 0) {
            dispatch_sync(new MediaAiResponseDispatchJob($taskRes->json()));

            return;
        }

        if ($taskRes->successful() && $taskRes->json('code') == 10101) {
            $module = $this->task->module;
            $mediaAiFile = $this->task->mediaAiFile;

            $this->task->delete();
            switch ($module) {
                case "nlp/keywords":
                    dispatch(new MediaAiNlpKeywordsJob($mediaAiFile))->delay(10);
                    break;
                case "nlp/ner":
                    dispatch(new MediaAiNlpNerJob($mediaAiFile))->delay(10);
                    break;
                case "nlp/summary":
                    dispatch(new MediaAiNlpSummaryJob($mediaAiFile))->delay(10);
                    break;
                case "nlp/eventnames":
                    dispatch(new MediaAiNlpEventnamesJob($mediaAiFile))->delay(10);
                    break;
                case "nlp/newsclassification":
                    dispatch(new MediaAiNlpNewsclassificationJob($mediaAiFile))->delay(10);
                    break;
                case "asr/accurate":
                    dispatch(new MediaAiAsrAccurateJob($mediaAiFile))->delay(10);
                    break;
                case "face1":
                    dispatch(new MediaAiFaceJob($mediaAiFile))->delay(10);
                    break;
                case "ocr/general":
                case "shotDetection":
                case "ssa":
                case "sta":
                case "sqa":
                case "audioClassification":
                    break;
            }

            return;
        }

        switch ($this->attempts()) {
            case 1:
                $this->release(1);
                break;
            case 2:
                $this->release(2);
                break;
            case 3:
                $this->release(3);
                break;
        }
    }
}
