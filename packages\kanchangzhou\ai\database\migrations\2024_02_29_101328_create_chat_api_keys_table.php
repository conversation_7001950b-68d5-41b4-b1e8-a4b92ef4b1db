<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('chat_api_keys', function (Blueprint $table) {
            $table->id();
            $table->string('key')
                  ->unique()
                  ->comment('API Key');
            $table->integer('user_id')
                  ->comment('用户ID');
            $table->integer('tokens')
                  ->nullable()
                  ->default(100000);
            $table->integer('pics')
                  ->nullable()
                  ->default(50);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('chat_api_keys');
    }
};
