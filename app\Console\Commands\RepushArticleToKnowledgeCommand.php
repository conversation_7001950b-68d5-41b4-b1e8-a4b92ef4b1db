<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Kanchangzhou\Article\Jobs\AddToKnowledge;
use Kanchangzhou\Article\Models\Article;

class RepushArticleToKnowledgeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'article:repush';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        Article::where('status', Article::STATUS_FINAL)
               ->where('type', Article::TYPE_NORMAL)
               ->where('redirect_to', '')
               ->whereNull('ai_agent_knowledge_id')
               ->where('published_at', '>', '2024-07-01')
               ->orderBy('published_at', 'desc')
               ->chunk(200, function ($articles) {
                   foreach ($articles as $article) {
                       dispatch(new AddToKnowledge($article))->onQueue('media_ai');
                   }
               });
    }
}
