<?php


namespace Kanchangzhou\Administrator\Facades;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Facade;
use Kanchangzhou\Administrator\Exceptions\NotHasPermissionException;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Auth\Contracts\UserInterface;

class AdministratorFacade extends Facade
{

    public static function auth() {
        return auth(config('kadmin.auth.guard'));
    }

    /**
     * @return AdminUser|null
     */
    public static function user() {
        return auth(config('kadmin.auth.guard'))->user();
    }

    /**
     * @param UserInterface|AdminUser $adminUser
     * @param $permission
     * @param null|Model $targetModel
     * @param bool $isThr
     *
     * @return bool
     * @throws
     */
    public static function can(UserInterface $adminUser, $permission, $targetModel = null, $isThr = true) {
        if ($adminUser->hasRole(1) || $adminUser->id == 1) {
            return true;
        }

        // 判断是否有权限
        if (is_string($permission) && str_contains($permission, '|')) {
            $permission = static::convertPipeToArray($permission);
        }

        $hasPermission = $adminUser->hasAnyPermission($permission);

        // 判断该用户是否仅有目标数据模型权限
        $onlyPermission = true;

        if ($targetModel) {
            $onlyPermission = $adminUser->targetModels($targetModel)
                                        ->count() == 0 || $adminUser->targetModels($targetModel)
                                                                          ->where('target_id', $targetModel->getKey())
                                                                          ->exists();
        }

        if (!($hasPermission && $onlyPermission) && $isThr) {
            throw new NotHasPermissionException(NotHasPermissionException::HAS_NO_PERMISSION, [
                'permission' => $permission,
            ]);
        }

        return $hasPermission && $onlyPermission;
    }

    /**
     * @param UserInterface|AdminUser $adminUser
     * @param Model $targetModel
     * @param bool $isThr
     *
     * @return bool
     * @throws NotHasPermissionException
     */
    public static function canOnly(UserInterface $adminUser, Model $targetModel, $isThr = true) {
        if ($adminUser->hasRole(1) || $adminUser->id == 1) {
            return true;
        }

        $onlyPermission = $adminUser->targetModels($targetModel)
                                    ->count() == 0 ? true : $adminUser->targetModels($targetModel)
                                                                      ->where('target_id', $targetModel->getKey())
                                                                      ->exists();

        if (!$onlyPermission && $isThr) {
            throw new NotHasPermissionException(NotHasPermissionException::HAS_NO_PERMISSION);
        }

        return $onlyPermission;
    }

    /**
     * @param UserInterface|AdminUser $adminUser
     * @param string|int|array|\Spatie\Permission\Contracts\Role|\Illuminate\Support\Collection $roles
     *
     * @return mixed
     */
    public static function hasRole(UserInterface $adminUser, $roles) {
        if ($adminUser->hasRole(1) || $adminUser->id == 1) {
            return true;
        }

        return $adminUser->hasRole($roles);
    }

    protected static function convertPipeToArray(string $pipeString) {
        $pipeString = trim($pipeString);

        if (strlen($pipeString) <= 2) {
            return $pipeString;
        }

        $quoteCharacter = substr($pipeString, 0, 1);
        $endCharacter = substr($quoteCharacter, -1, 1);

        if ($quoteCharacter !== $endCharacter) {
            return explode('|', $pipeString);
        }

        if (!in_array($quoteCharacter, [
            "'",
            '"',
        ])) {
            return explode('|', $pipeString);
        }

        return explode('|', trim($pipeString, $quoteCharacter));
    }
}
