<?php

use Illuminate\Support\Facades\Route;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingPositionException;
use Kanchangzhou\Advertisement\Http\Resources\AdvertisingResource;
use Kanchangzhou\Kernel\Supports\Respond;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API Routes for your application. These
| Routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//Route::middleware('auth:api')->get('/user', function (Request $request) {
//    return $request->user();
//});
Route::prefix("/api/v1")
     ->group(function () {
         Route::prefix("/admin")
              ->middleware([
                  'api',
                  "auth.kadmin",
              ])
              ->group(function () {//后台管理接口的统一入口
                  Route::prefix("/kczavert")
                       ->group(function () {//广告商管理入口
                           Route::post("/add", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertiserController@add");//新增一个广告位
                           Route::delete("/delete/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertiserController@delete");//删除一个广告位
                           Route::put("/edit/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertiserController@edit");//编辑一个广告位
                           Route::get("/list", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertiserController@list");//获取广告位列表
                           Route::get("/detail/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertiserController@detail");//获取广告位详情
                       });
                  Route::prefix("/advertising")
                       ->group(function () {//广告管理入口
                           Route::get("/pre-add/{positionid}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@preAdd");
                           Route::get("/form-options", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@formOptions");
                           Route::post("/add/{positionid}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@add");//新增一个广告
                           Route::delete("/delete/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@delete");//删除一个广告
                           Route::put("/updown/{id}/{updown}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@updown");//上下架一个广告
                           Route::get("/copy/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@copy");//拷贝一个广告
                           Route::put("/edit/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@edit");//编辑一个广告
                           Route::get("/detail/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@detail");//获取广告详情
                           Route::get("/list", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@list");//获取广告列表
                           Route::post("/search", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingController@search");//搜索广告
                           Route::get('/type', 'Kanchangzhou\\Advertisement\\Http\\AdminControllers\\AdvertisingController@types');
//            Route::get("/type", function(){
//                return Respond::respondWithData(config("kadvertisement.advertising-type"), AdvertisingPositionException::OPERATION_SUCCESS);
//            });

                       });
                  Route::prefix("/advertising/position")
                       ->group(function () {//广告位管理入口
                           Route::post("/add", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@add");//新增一个广告位
                           Route::delete("/delete/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@delete");//删除一个广告位
                           Route::put("/edit/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@edit");//编辑一个广告位
                           Route::get("/list", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@list");//获取广告位列表
                           Route::get("/detail/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@detail");//获取广告位详情
                           Route::post("/push-to-home/{id}", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@pushToHome");
                           Route::get("/all-pos-with-adv", "Kanchangzhou\\Advertisement\\Http\AdminControllers\\AdvertisingPositionController@allPosWithAdv");
                       });

              });

         Route::middleware([
             'api',
             'version_filter',
         ])
              ->group(function () {
                  Route::get("/click/{id}", "Kanchangzhou\\Advertisement\\Http\\Controllers\\AdvertisingController@click");//上报广告点击量+1
                  Route::get("/ad-position", "Kanchangzhou\\Advertisement\\Http\\Controllers\\AdvertisingPositionController@adPosition");//获取所有的系统广告位
//        Route::get("/ad/{key}", "Kanchangzhou\\Advertisement\\Http\\Controllers\\AdvertisingController@ad");//根据广告位的key，获取广告
                  Route::get("/advertising-type", "Kanchangzhou\\Advertisement\\Http\\Controllers\\AdvertisingController@types");
//        Route::get("/advertising-type", function(){
//            return Respond::respondWithData(config("kadvertisement.advertising-type"), AdvertisingPositionException::OPERATION_SUCCESS);
//        });
                  Route::get("/all-pos-with-adv", "Kanchangzhou\\Advertisement\\Http\\Controllers\\AdvertisingPositionController@allPosWithAdv");
                  Route::get("/ad/{key}", "Kanchangzhou\\Advertisement\\Http\\Controllers\\AdvertisingController@ad");//根据广告位的key，获取广告
              });
     });

