<?php


namespace Kanchangzhou\Administrator\Exceptions;


use Kanchangzhou\Kernel\Exceptions\BaseException;

class AuthSafeException extends BaseException
{
    const AUTH_SAFE_LOGIN_FAIL_LOCK = 20311;
    const AUTH_SAFE_LOGIN_TIMEOUT = 20312;
    const AUTH_SAFE_PWD_EXP = 20313;

    public function __construct($errCode = 10000, $errData = [], $statusCode = 403) {
        parent::__construct($errCode, $errData, $statusCode);
    }

    public static function message($code) {
        $msgArr = [
            static::AUTH_SAFE_LOGIN_FAIL_LOCK => '账号尝试次数过多,请' . (config('kadmin.auth_safe.login_fail_lock_sec') / 60) . '分钟后再试',
            static::AUTH_SAFE_LOGIN_TIMEOUT => '账号长时间未操作, 请重新登录',
            static::AUTH_SAFE_PWD_EXP => '账号密码已经到期,请联系管理员进行密码修改',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : parent::message($code);
    }

    public function dontReport() {
        return true;
    }
}
