<?php

namespace Changguan\SDK\Exceptions;

/**
 * 认证异常类
 * 用于处理JWT Token、签名验证等认证相关的异常
 */
class AuthException extends \Exception
{
    /**
     * Token 格式错误
     */
    public const ERROR_INVALID_FORMAT = 1001;

    /**
     * Token 解码失败
     */
    public const ERROR_DECODE_FAILED = 1002;

    /**
     * Token 已过期
     */
    public const ERROR_EXPIRED = 1003;

    /**
     * Token 尚未生效
     */
    public const ERROR_NOT_VALID_YET = 1004;

    /**
     * 签名无效
     */
    public const ERROR_INVALID_SIGNATURE = 1005;

    /**
     * 缺少验证密钥
     */
    public const ERROR_MISSING_KEY = 1006;
}
