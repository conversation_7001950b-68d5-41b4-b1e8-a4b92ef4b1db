<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Kanchangzhou\Article\Models\IdeHelperArticleContentAudit;

/**
 * Kanchangzhou\AI\Models\ArticleContentAudit
 *
 * @property int $id
 * @property string $source_type
 * @property string $source_id
 * @property string $content_hash
 * @property array|null $audit_result
 * @property int|null $has_notice
 * @property int $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit query()
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereAuditResult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereContentHash($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereHasNotice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereSourceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ArticleContentAudit whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperArticleContentAudit
 */
class ArticleContentAudit extends Model
{
    use HasFactory;

    public $guarded = [];

    protected $casts = [
        'audit_result' => 'json',
    ];

    const TYPE_BLACK = 1;
    const TYPE_CORRECTION = 2;

    const TYPE_MAPPING = [
        self::TYPE_BLACK => '黑名单',
        self::TYPE_CORRECTION => '纠错',
    ];

    const HAS_NOTICE_NO = 1;
    const HAS_NOTICE_YES = 2;

}
