<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BehaviorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $module, $id) {

//        dd($module,$id);
        $request->headers->set('statistic-module', $module);
        $request->headers->set('statistic-id', $id);

        return $next($request);
    }
}
