<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Aliyun;

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;

class Auth
{
    private $accessToken;

    public function __construct($accessKeyId, $accessKeySecret) {
        $this->setAccessToken($accessKeyId, $accessKeySecret);
    }

    public function setAccessToken($accessKeyId, $accessKeySecret) {
        try {
            AlibabaCloud::accessKeyClient($accessKeyId, $accessKeySecret)
                        ->regionId('cn-shanghai')
                        ->asDefaultClient();

            $response = AlibabaCloud::nlsCloudMeta()
                                    ->v20180518()
                                    ->createToken()
                                    ->request();

            $this->accessToken = $response['Token']['Id'];
        } catch (ClientException|ServerException $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function getAccessToken(){
        return $this->accessToken;
    }
}
