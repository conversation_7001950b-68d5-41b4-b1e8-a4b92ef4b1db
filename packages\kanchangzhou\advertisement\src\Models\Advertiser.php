<?php

namespace Kanchangzhou\Advertisement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\Advertisement\Models\Advertiser
 *
 * @property int $id
 * @property string $advertiser_name 客户名称
 * @property string $phone 联系电话
 * @property string $email 邮箱
 * @property string $addr 地址
 * @property string $start_coperation 合作开始时间
 * @property string $stop_coperation 合作结束时间
 * @property string $bz 备注信息
 * @property string $salesman 业务员
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser query()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereAddr($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereAdvertiserName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereBz($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereSalesman($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereStartCoperation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereStopCoperation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertiser withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdvertiser
 */
class Advertiser extends Model
{
    use SoftDeletes;
    //
    protected $table = 'advertiser';


}
