<?php

namespace Rjustice\CloudFiles\Providers;

use League\Flysystem\Util;

abstract class AbstractCloudFiles
{
    abstract public function getPath($file);

    abstract public function signatureUrl($file);

    abstract public function getUrl($path);

    abstract public function policy();

    abstract public function verify();

    abstract public function write($path, $contents, $config);

    abstract public function writeStream($path, $resource, $config);

    abstract public function update($path, $contents, $config);

    abstract public function updateStream($path, $resource, $config);

    abstract public function rename($path, $newPath);

    abstract public function copy($path, $newPath);

    abstract public function delete($path);

    abstract public function deleteDir($dirname);

    abstract public function createDir($dirname);

    abstract public function setVisibility($path, $visibility);

    abstract public function getVisibility($path);

    abstract public function has($path);

    abstract public function read($path);

    abstract public function readStream($path);

    abstract public function listContents($dirname);

    protected function removePathPrefix($path) {
        return substr($path, strlen(''));
    }

    protected function normalizeResponse(array $object, $path = null) {
        $result = ['path' => $path ?: $this->removePathPrefix($object['Key'] ?? $object['Prefix'])];
        $result['dirname'] = Util::dirname($result['path']);

        if (isset($object['LastModified'])) {
            $result['timestamp'] = strtotime($object['LastModified']);
        }

        if (str_ends_with($result['path'], '/')) {
            $result['type'] = 'dir';
            $result['path'] = rtrim($result['path'], '/');

            return $result;
        }

        return array_merge($result, Util::map($object, static::$resultMap), ['type' => 'file']);
    }

    public function debug() {
        if($this->debug){

        }
    }
}
