<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('ai_music_histories', function (Blueprint $table) {
            $table->text('result1')->nullable()->change();
        });
        Schema::table('ai_music_histories', function (Blueprint $table) {
            $table->text('result2')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {

    }
};
