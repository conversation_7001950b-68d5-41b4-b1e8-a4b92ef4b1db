<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiOrderItem
 *
 * @property int $id
 * @property int $ai_order_id 订单ID
 * @property string|null $model_id 模型ID
 * @property string|null $model_uuid 模型UUID
 * @property string|null $provider 供应商
 * @property int $input_tokens 输入
 * @property int $output_tokens
 * @property int $total_tokens
 * @property string $price 价格
 * @property int $unit_count 单位数量
 * @property string $total
 * @property string $order_type 订单类型
 * @property int $status 状态
 * @property int|null $user_package_id 资源包ID
 * @property int|null $chat_history_id 聊天记录ID
 * @property string|null $unique_hash 唯一ID
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\AiOrder|null $order
 * @property-read \Kanchangzhou\AI\Models\AiUserPackage|null $resourcePackage
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereAiOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereChatHistoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereInputTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereModelUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereOrderType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereOutputTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereTotalTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereUniqueHash($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereUnitCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem whereUserPackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrderItem withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiOrderItem
 */
class AiOrderItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    const PROVIDER_ALI = ChatModel::PROVIDER_ALI;
    const PROVIDER_OPENAI = ChatModel::PROVIDER_OPENAI;
    const PROVIDER_BAIDU = ChatModel::PROVIDER_BAIDU;
    const PROVIDER_KNOWLEDGE = ChatModel::PROVIDER_KNOWLEDGE;

    const PROVIDER_MAP = ChatModel::PROVIDER_MAP;

    public function resourcePackage() {
        return $this->belongsTo(AiUserPackage::class, 'resource_package_id', 'id');
    }

    public function order() {
        return $this->belongsTo(AiOrder::class, 'ai_order_id', 'id');
    }

}
