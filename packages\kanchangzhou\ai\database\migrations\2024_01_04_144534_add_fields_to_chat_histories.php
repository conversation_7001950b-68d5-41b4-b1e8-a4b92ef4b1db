<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->string('module_name')
                  ->after('provider')
                  ->nullable()
                  ->comment('模块名称');
            $table->string('module_key')
                  ->after('module_name')
                  ->nullable()
                  ->comment('模块key');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('module_name');
            $table->dropColumn('module_key');
        });
    }
};
