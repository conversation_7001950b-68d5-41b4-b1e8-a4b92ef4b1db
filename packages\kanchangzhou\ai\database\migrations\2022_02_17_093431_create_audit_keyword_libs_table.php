<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('audit_keyword_libs', function (Blueprint $table) {
            $table->id();
            $table->string('category');
            $table->string('name');
            $table->integer('lib_id');
            $table->string('lib_type');
            $table->unsignedTinyInteger('is_show')
                  ->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('audit_keyword_libs');
    }
};
