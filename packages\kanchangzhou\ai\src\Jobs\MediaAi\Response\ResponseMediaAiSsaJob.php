<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;

class ResponseMediaAiSsaJob extends MediaAiBaseJob
{
// data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"ad85fede7aac4a1aa6cbd8724258b06b","subDataTypes":[{"type":"ssa","source":"索贝","version":null}],"ssa":[{"fileId":"329aca14-8be3-4c0e-8468-5dc8f9f926d8","statusCode":0,"statusInfo":"success","contents":[{"background":{"en_name":"graphics","zh_name":"图文类","confidence":1,"index":25,"is_indoor":"N"},"landMark":{"landMarkConfidence":0,"landMarkName":"无地标"},"saliency":{"label":"graphics","bboxes":[597,174,723,726]}}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {

    }
}
