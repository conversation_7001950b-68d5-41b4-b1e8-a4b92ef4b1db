<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiMusicHistory
 *
 * @property int $id
 * @property int|null $user_id 用户ID
 * @property string|null $task_code 任务码
 * @property string|null $module 模型名称
 * @property string|null $provider 服务提供者
 * @property string|null $action 生成音乐执行的方法
 * @property string|null $prompt 关键词
 * @property string|null $options 参数
 * @property string|null $task_id 任务ID
 * @property string|null $result1 结果1
 * @property string|null $result2 结果2
 * @property string|null $music_url 音乐地址
 * @property string|null $lyric 生成的歌词
 * @property int|null $state 音乐生成进度，0生成中，1成功，2失败
 * @property string|null $errmsg 错误信息
 * @property int|null $custom_mode 自定义模式,默认0为灵感模式;1-自定义模式
 * @property int $finish_time
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereCustomMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereErrmsg($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereFinishTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereLyric($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereModule($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereMusicUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory wherePrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereResult1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereResult2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereTaskCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiMusicHistory withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiMusicHistory
 */
class AiMusicHistory extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [

    ];


}
