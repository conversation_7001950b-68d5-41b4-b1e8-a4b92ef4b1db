<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiTasksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_ai_tasks', function (Blueprint $table) {
            $table->id();
            $table->integer('media_ai_file_id')->comment('媒体文件id');
            $table->string('file_id')->comment('文件id');
            $table->string('module')->comment('模块');
            $table->string('task_id')->comment('任务id');
            $table->string('task_status')->comment('任务状态');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_ai_tasks');
    }
}
