<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('skill_competitions', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->string('name');
            $table->string('title');
            $table->text('content');
            $table->string('background_music', 1000)
                  ->nullable();
            $table->string('status')
                  ->default('waiting');
            $table->text('ai_score_desc')
                  ->nullable();
            $table->string('ai_score', 1000)
                  ->nullable();
            $table->string('ai_media_score', 1000)
                  ->default('[]')
                  ->nullable();
            $table->string('ai_score_status')
                  ->default('waiting');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('skill_competitions');
    }
};
