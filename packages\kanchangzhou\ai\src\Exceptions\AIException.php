<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class AIException extends BaseException
{
    const NETWORK_ERROR = 302001;
    const OVER_LIMIT_DAILY_TOKENS = 302002;
    const OVER_LIMIT_DAILY_TIMES = 302003;
    const NO_PERMISSION = 302004;
    const NOT_SUPPORT_FILES = 302005;

    public static function message($code) {
        $msgArr = [
            static::NETWORK_ERROR => '网络错误',
            static::OVER_LIMIT_DAILY_TOKENS => '超过每日tokens使用限制',
            static::OVER_LIMIT_DAILY_TIMES => '超过每日请求次数限制',
            static::NO_PERMISSION => '无权限',
            static::NOT_SUPPORT_FILES => '不支持文件上传',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
