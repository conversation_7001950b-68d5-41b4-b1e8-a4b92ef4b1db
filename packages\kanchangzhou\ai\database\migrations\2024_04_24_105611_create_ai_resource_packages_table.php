<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('ai_resource_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                  ->comment('资源包名称');
            $table->string('description')
                  ->nullable();
            $table->string('type');
            $table->integer('total_tokens')
                  ->comment('总token数');
            $table->integer('expired_days')
                  ->default(30)
                  ->comment('过期天数');
            $table->unsignedTinyInteger('status');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('ai_resource_packages');
    }
};
