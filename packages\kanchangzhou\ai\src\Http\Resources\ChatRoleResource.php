<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ChatRoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request) {
        return [
            'id' => $this->role_uuid,
            'title' => $this->title,
            'options' => $this->options,
            'examples' => $this->examples,
            'icon' => $this->icon,
            'description' => $this->description,
            'module_key' => $this->module_key,
            'is_published' => $this->is_published,
        ];
    }
}
