<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('chat_used_roles', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->uuid('role_uuid');
            $table->timestamps();
        });

        Schema::table('chat_roles', function (Blueprint $table) {
            $table->string('owner_nickname')
                  ->after('owner_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('chat_used_roles');

        Schema::table('chat_roles', function (Blueprint $table) {
            $table->dropColumn('owner_nickname');
        });
    }
};
