<?php

namespace Kanchangzhou\AI\Services\Chat\Providers;

use Illuminate\Http\Request;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatResponse;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatService;

class BaiduQianfan extends AIChatService
{
    public function handler(): AIChatResponse {
        // TODO: Implement handler() method.
    }

    public function streamHandler() {
        // TODO: Implement streamHandler() method.
    }

    public function uploadFile(Request $request) {
        // TODO: Implement uploadFile() method.
    }

    protected function messageWithFiles($files) {

    }
}
