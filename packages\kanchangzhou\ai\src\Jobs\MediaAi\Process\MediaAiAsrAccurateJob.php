<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Process;

use Illuminate\Support\Str;
use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Jobs\MediaAi\MediaAiQueryTaskJob;
use Kanchangzhou\AI\Models\MediaAiTask;
use Kanchangzhou\AI\Services\MediaAi\AsrAccurate;
use Kanchangzhou\AI\Services\MediaAi\UploadApi;
use Kanchangzhou\Livechannel\Models\Backend\VideoDefinition;

class MediaAiAsrAccurateJob extends MediaAiBaseJob
{
    protected $mediaFile;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($mediaFile) {
        $this->mediaFile = $mediaFile;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $path = $this->mediaFile->media_file_url;
        if ($this->mediaFile->updated_at->diffInDays(now()) >= 7) {

            $path = VideoDefinition::where('video_id', $this->mediaFile->fileaiable_id)
                                   ->where('definition', 'OD')
                                   ->value('aliyun_url');
            if (filter_var($path, FILTER_VALIDATE_URL) && Str::contains($path, 'livehls.cztv.tv')) {
                $uploadApi = new UploadApi();
                $result = $uploadApi->remoteUpload($path);
                $path = $result->json('data.path');
            }

            $this->mediaFile->media_file_url = $path;
            $this->mediaFile->save();
        }


        $files[] = [
            'id' => $this->mediaFile->file_id,
            'path' => $path,
        ];

        $asrAccurate = new AsrAccurate();

        $task = MediaAiTask::where('media_ai_file_id', $this->mediaFile->id)
                           ->where('module', $asrAccurate->getTaskType())
                           ->where('file_id', $this->mediaFile->file_id)
                           ->first();

        if ($task && ($task->task_status == MediaAiTask::STATUS_PROCESSING || $task->task_status == MediaAiTask::STATUS_FINISHED)) {
            MediaAiQueryTaskJob::dispatch($task);

            return;
        }

        $res = $asrAccurate->asrAccurate($files);

        if ($res->successful()) {
            MediaAiTask::create([
                'media_ai_file_id' => $this->mediaFile->id,
                'file_id' => $this->mediaFile->file_id,
                'module' => $asrAccurate->getTaskType(),
                'task_id' => $res->json('data.guid'),
                'task_status' => MediaAiTask::STATUS_PROCESSING,
            ]);
        }
    }
}
