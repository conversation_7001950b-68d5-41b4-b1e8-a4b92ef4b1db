<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\AI\Models\ChatRoleCategory;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ChatRoleCategoryController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('智能场景.分类列表');

        $categories = ChatRoleCategory::when($request->input('is_show'), function ($query, $isShow) {
            $query->where('is_show', $isShow);
        })
                                      ->orderBy('sort')
                                      ->paginate();

        return Respond::respondWithData(JsonResource::collection($categories));
    }

    public function show($id) {
        PermissionHook::can('智能场景.分类详情');

        $category = ChatRoleCategory::where('id', $id)
                                    ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($category));
    }

    public function store(Request $request) {
        PermissionHook::can('智能场景.分类创建');

        $validated = $this->validate($request, [
            'title' => 'required|max:10',
            'is_show' => '',
            'sort' => '',
        ]);

        $category = ChatRoleCategory::create($validated);

        return Respond::respondWithData(JsonResource::make($category));
    }

    public function update(Request $request, $id) {
        PermissionHook::can('智能场景.分类编辑');

        $category = ChatRoleCategory::where('id', $id)
                                    ->firstOrFail();

        $validated = $this->validate($request, [
            'title' => 'required|max:10',
            'is_show' => '',
            'sort' => '',
        ]);

        $category->update($validated);

        return Respond::respondWithData(JsonResource::make($category));
    }

    public function destroy($id) {
        PermissionHook::can('智能场景.分类删除');

        $category = ChatRoleCategory::where('id', $id)
                                    ->firstOrFail();

        $category->delete();

        return Respond::respondWithData();
    }
}
