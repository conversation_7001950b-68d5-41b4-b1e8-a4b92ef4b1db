<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\AuditKeyword
 *
 * @property int $id
 * @property string $keyword
 * @property string|null $hit_count
 * @property int $keyword_id
 * @property int $lib_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\AuditKeywordsLib|null $lib
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword query()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereHitCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereKeyword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereKeywordId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereLibId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeyword whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAuditKeyword
 */
class AuditKeyword extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function lib() {
        return $this->belongsTo(AuditKeywordsLib::class, 'lib_id', 'lib_id');
    }
}
