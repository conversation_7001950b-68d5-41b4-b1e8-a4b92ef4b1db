<?php

namespace Kanchangzhou\Administrator\Http\Middlewares;

use Closure;
use Kanchangzhou\Administrator\Exceptions\NotHasPermissionException;
use Kanchangzhou\Administrator\Traits\HasPermissions;
use Kanchangzhou\Kernel\Exceptions\AuthFailException;

class CheckPermissions
{
    use HasPermissions;

    /**
     * @param $request
     * @param Closure $next
     * @param $permission
     *
     * @return mixed
     * @throws AuthFailException
     * @throws NotHasPermissionException
     */
    public function handle($request, Closure $next, $permission) {
        if (app('auth')->guest()) {
            throw new AuthFailException(AuthFailException::UNAUTHENTICATED,[],401);
        }

        $permissions = is_array($permission) ? $permission : explode('|', $permission);

        foreach ($permissions as $permission) {
            if ($this->can($permission)) {
                return $next($request);
            }
        }
    }
}