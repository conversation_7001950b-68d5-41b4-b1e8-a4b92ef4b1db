<?php
namespace Kanchangzhou\AI\Services\Audio\Providers\Volcengine;
use Volcengine\Common\Configuration;
use Volcengine\Vpc\API\VPCApi;
use Volcengine\Vpc\Model\CreateVpcRequest;
class Auth
{
    private $accessToken;

    public function __construct($accessKeyId, $accessKeySecret) {
        $this->setAccessToken($accessKeyId, $accessKeySecret);
    }

    public function setAccessToken($accessKeyId, $accessKeySecret) {

        $config = Configuration::getDefaultConfiguration()
            ->setAk(env('VOLCENGINE_AK'))
            ->setSk(env('VOLCENGINE_SK'))
            ->setRegion('cn-beijing');

        $apiInstance = new VPCApi(new Client(), $config);

        $body = new CreateVpcRequest();
        $body->setClientToken('token-123456789')
            ->setCidrBlock('192.168.0.0/16')
            ->setDnsServers(['10.0.0.1', '10.1.1.2']);

        try {
            $result = $apiInstance->createVpc($body);
            $this->accessToken = response()->json($result);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function getAccessToken(){
        return $this->accessToken;
    }
}
