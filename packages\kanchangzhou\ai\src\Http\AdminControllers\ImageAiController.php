<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

use Kanchangzhou\AI\Exceptions\AIImageException;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Hooks\SensitiveWordsHook;
use Kanchangzhou\Kernel\Supports\Respond;

class ImageAiController extends BaseController
{

    //百度人工智能，logo删除
    public function logoDelete(Request $request) {

        $this->validate($request, [
            'image' => 'required_without:cont_sign',
            //base64编码后的图片数据（和cont_sign二选一）。要求base64编码后大小不超过4M，最短边至少15px，最长边最大4096px,支持jpg/png/bmp格式. 注意：图片需要base64编码、去掉编码头后再进行urlencode。。
            'cont_sign' => 'required_without:image',
            //图片签名（和image二选一，image优先级更高）
        ]);

        $body = [

        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'image' => $request->input('image'),
                           'cont_sign' => $request->input('cont_sign'),
                           'access_token' => $this->accessToken(),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rest/2.0/realtime_search/v1/logo/delete", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return Respond::respondWithData([
                'image' => $res->json('image'),
                'log_id' => $res->json('log_id'),
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    //百度人工智能，图片无损放大
    public function imageQualityEnhance(Request $request) {

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
        ]);

        $body = [

        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                           'image' => $request->input('image'),
                           'url' => $request->input('url'),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/image_quality_enhance", $body);

        if ($res->successful() && !$res->json('error_code')) {

            return Respond::respondWithData([
                [
                    'image' => $res->json('image'),
                    'log_id' => $res->json('log_id'),
                ],
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    //百度人工智能，图像修复
    public function inpainting(Request $request) {

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
            //'rectangle'=>"required",     //要去除的位置为规则矩形时，给出坐标信息，每个元素包含left, top, width, height，int 类型。如： [{'width': 92, 'top': 25, 'height': 36, 'left': 543}] 注意：上传宽高、位置坐标参数要比图片实际宽高小
        ]);

        $body = [
            'rectangle' => [$request->input('rectangle')],
        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                           'image' => $request->input('image'),
                           'url' => $request->input('url'),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/inpainting", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return Respond::respondWithData([
                [
                    'image' => $res->json('image'),
                    'log_id' => $res->json('log_id'),
                ],
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    //百度人工智能，文档图片去底纹
    public function docRepair(Request $request) {

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
        ]);

        $body = [

        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                           'image' => $request->input('image'),
                           'url' => $request->input('url'),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/doc_repair", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return Respond::respondWithData([
                'image' => $res->json('result'),
                'log_id' => $res->json('log_id'),
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    //百度人工智能，图像去噪
    public function denoise(Request $request) {

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
            //'option' => '',     //可用于调节去噪强度，产生不同效果的去噪图，可根据期望进行效果调试。取值在[0，200]区间内, 默认100
        ]);

        $body = [

        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                           'image' => $request->input('image'),
                           'url' => $request->input('url'),
                           'option' => $request->input('option', 100),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/denoise", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return Respond::respondWithData([
                'image' => $res->json('result'),
                'log_id' => $res->json('log_id'),
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    //百度人工智能，文生图
    public function txt2img(Request $request) {
        $this->validate($request, [
            'text' => 'required|string',
            //输入内容，长度不超过100个字（UTF-8编码）
            //'resolution' =>"required|string",    //图片分辨率，可支持1024*1024、1024*1536、1536*1024 默认1024*1024
            //'style' =>"required|string",    //目前支持风格有：探索无限、古风、二次元、写实风格、浮世绘、low poly 、未来主义、像素风格、概念艺术、赛博朋克、洛丽塔风格、巴洛克风格、超现实主义、水彩画、蒸汽波艺术、油画、卡通画 默认写实风格
            //'num' =>'required|integer',    //图片生成数量，支持1-6张 默认1张
        ]);

        $body = [
            'text' => $request->input('text'),
            'resolution' => $request->input('resolution', '1024*1024'),
            'style' => $request->input('style', '写实风格'),
            'num' => $request->input('n', 1),
        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rpc/2.0/ernievilg/v1/txt2img", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return Respond::respondWithData([
                'task_id' => $res->json('data.taskId'),
                'log_id' => $res->json('log_id'),
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    //百度人工智能，文生查询结果
    public function getImg(Request $request) {
        $this->validate($request, [
            'task_id' => 'required|integer',
            //任务ID
        ]);

        $body = [
            'taskId' => $request->input('task_id'),
        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                       ],
                   ])
                   ->post("https://aip.baidubce.com/rpc/2.0/ernievilg/v1/getImg", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return Respond::respondWithData([
                'images' => $res->json('data.imgUrls'),
                'waiting' => $res->json('data.waiting'),
                'log_id' => $res->json('log_id'),
            ]);
        } else {
            throw (new AIImageException(AIImageException::SERVICE_ERROR))->setErrMsg($res->json('error_msg'));
        }
    }

    protected function accessToken($isForce = false) {
        //百度Access Token的有效期(秒为单位，有效期30天) 系统缓存有效期20天
        $accessToken = cache()->get('baidu_api_access_token');

        if (!$accessToken || $isForce) {
            $res = Http::acceptJson()
                       ->withOptions([
                           'query' => [
                               'grant_type' => 'client_credentials',
                               'client_id' => config('kai.image.providers.baidu.api_key'),
                               'client_secret' => config('kai.image.providers.baidu.secret_key'),
                               //'client_id' => 'HxRa4NVMzC7lwHBVSTNAyCGL',
                               //'client_secret' => 'BF00EpOdGXEQtF4iV3whGwXT0Z4OaZR5',
                           ],
                       ])
                       ->post('https://aip.baidubce.com/oauth/2.0/token');

            if ($res->successful()) {
                $accessToken = $res->json('access_token');
                cache()->put('baidu_api_access_token', $accessToken, 20 * 24 * 60);
            }
        }

        return $accessToken;
    }
}

