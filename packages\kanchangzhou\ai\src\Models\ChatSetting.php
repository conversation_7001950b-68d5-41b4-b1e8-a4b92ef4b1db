<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\ChatSetting
 *
 * @property int $id
 * @property string $title
 * @property string $key
 * @property string $value
 * @property string $value_type string, array
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatSetting whereValueType($value)
 * @mixin \Eloquent
 * @mixin IdeHelperChatSetting
 */
class ChatSetting extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function getValueAttribute() {
        if ($this->value_type == 'array') {
            return json_decode($this->attributes['value'], true);
        }

        return $this->attributes['value'];
    }

    public function setValueAttribute($value) {
        if ($this->value_type == 'array') {
            $this->attributes['value'] = json_encode($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }
}
