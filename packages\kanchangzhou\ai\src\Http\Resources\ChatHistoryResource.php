<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ChatHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request) {
        return [
            'chat_uuid'         => $this->chat_uuid,
            'reasoning_content' => $this->reasoning_content,
            'message'           => $this->message,
            'type'              => $this->type,
            'role'              => $this->role,
            'is_like'           => $this->is_like,
            'role_step'         => $this->role_step,
            'step_input'        => $this->step_input,
            'chat_role_id'      => $this->chat_role_id,
            'chat_role'         => ChatRoleResource::make($this->whenLoaded('chatRole')),
            'module_key'        => $this->chat_model_uuid,
            'module_name'       => $this->module_name,
            'chat_model'        => ChatModelResource::make($this->whenLoaded('chatModel')),
            'fileids'           => $this->fileids,
        ];
    }
}
