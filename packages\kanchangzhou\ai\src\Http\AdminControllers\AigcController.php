<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
/**
 * @deprecated 1.0.0
 * Class AigcController
 * @package Kanchangzhou\AI\Http\AdminControllers
 */
class AigcController extends BaseController
{
    public function wanx(Request $request) {
        $this->validate($request, [
            'prompt' => 'required|string',
        ]);

        $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis';
        $body = [
            'model' => 'wanx-v1',
            'input' => [
                'prompt' => $request->input('prompt'),
            ],
            'parameters' => [
                'style' => $request->input('style', '<auto>'),
                'seed' => rand(1, 1000000),
                'size' => $request->input('size', '1024*1024'),
                'n' => 1,
            ],
        ];


        $res = Http::asJson()
                   ->withHeaders(['X-DashScope-Async' => 'enable'])
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->post($uri, $body);

        return $res->json();
    }

    public function wanxQuestTask(Request $request) {
        $uri = 'https://dashscope.aliyuncs.com/api/v1/tasks/';

        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->get($uri . $request->input('task_id'));

        return $res->json();
    }
}

