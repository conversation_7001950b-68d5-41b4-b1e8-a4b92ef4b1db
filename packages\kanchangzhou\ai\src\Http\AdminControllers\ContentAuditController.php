<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Jobs\VideoModerationCallbackJob;
use Kanchangzhou\AI\Models\AiVideoAudit;
use Kanchangzhou\AI\Models\AiVideoAuditLabel;
use Kanchangzhou\AI\Models\ArticleContentAudit;
use Kanchangzhou\AI\Supports\KeywordLib;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use OSS\Credentials\EnvironmentVariableCredentialsProvider;
use OSS\OssClient;

class ContentAuditController extends BaseController
{
    use AiVideoAuditLabel;

    public function audit(Request $request) {
        $this->validate($request, ['content' => 'required|string|max:10000'], []);

        $res = KeywordLib::scan($request->input('content'));

        return Respond::respondWithData($res);
    }

    public function checkAudits(Request $request) {
        $sourceIds = $request->input('source_ids', []) ?? [];

        if (is_string($sourceIds)) {
            $sourceIds = explode(',', $sourceIds);
        }

        $res = ArticleContentAudit::where('source_type', $request->input('source_type'))
                                  ->whereIn('source_id', $sourceIds)
                                  ->where('type', $request->input('type', ArticleContentAudit::TYPE_CORRECTION))
                                  ->limit(50)
                                  ->get()
                                  ->groupBy('source_id');

        return Respond::respondWithData($res);
    }

    // 利用阿里云oss sdk 直传模式, 获取oss直传的凭证
    public function getOssPolicy(Request $request) {
        $this->validate($request, [
            'name' => 'required',
        ], []);

        $fileUuid = Str::uuid()
                       ->toString();
        $callbackParams = [
            'callbackUrl' => route('ai.oss.upload.callback'),
            //'callbackUrl' => 'https://kcztest.xiao5.cn/api/v1/ai/oss/callback',
            'callbackBody' => 'filename=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}&contentMd5=${contentMd5}&name=' . urlencode($request->input('name')) . '&uid=' . AuthFacade::adminUser()
                                                                                                                                                                                            ->getId() . '&nickname=' . urlencode(AuthFacade::adminUser()
                                                                                                                                                                                                                                           ->getNickname()) . '&uuid=' . $fileUuid,
            'callbackBodyType' => 'application/x-www-form-urlencoded',
        ];

        $dir = date('Ymd') . '/';
        $conditions = [
            [
                'bucket' => config('kai.other.aliyun.oss.bucket'),
            ],
            [
                'starts-with',
                '$key',
                $dir,
            ],
            [
                'eq',
                '$success_action_status',
                '200',
            ],
            [
                'callback' => base64_encode(json_encode($callbackParams)),
            ],
            [
                'content-length-range',
                0,
                // 500M
                1024 * 1024 * 500,
            ],
        ];

        $end = time() + 60;
        $exporation = str_replace('+00:00', '.000Z', gmdate('c', $end));
        $policy = json_encode([
            'expiration' => $exporation,
            'conditions' => $conditions,
        ]);

        $signStr = base64_encode($policy);
        $signature = base64_encode(hash_hmac('sha1', $signStr, config('kai.other.aliyun.oss.access_key_secret'), true));

        return Respond::respondWithData([
            'accessid' => config('kai.other.aliyun.oss.access_key_id'),
            'host' => config('kai.other.aliyun.oss.domain'),
            'policy' => base64_encode($policy),
            'signature' => $signature,
            'expire' => $end,
            'callback' => base64_encode(json_encode($callbackParams)),
            'dir' => $dir,
        ]);

    }

    public function videoModeration(Request $request) {

    }

    public function getVideoModerationResult($videoId) {
        $video = AiVideoAudit::where('id', $videoId)
                             ->firstOrFail();

        if ($video->status != AiVideoAudit::STATUS_PROCESSING) {
            throw (new AIException())->setErrMsg('视频审核已完成或尚未提交审核');
        }

        dispatch(new VideoModerationCallbackJob($video))->onQueue('media_ai');

        return Respond::respondWithData();
    }

    public function videoModerationList(Request $request) {
        $videos = AiVideoAudit::when($request->input('title'), function ($query, $title) {
            $query->where('title', 'like', "%{$title}%");
        })
                              ->orderBy('id', 'desc')
                              ->paginate(15, [
                                  'id',
                                  'video_uuid',
                                  'title',
                                  'nickname',
                                  'status',
                                  'created_at',
                                  'checked_at',
                              ]);

//        $data = $videos->data;
//        $ossClient = new OssClient(config('kai.other.aliyun.oss.access_key_id'), config('kai.other.aliyun.oss.access_key_secret'), config('kai.other.aliyun.oss.endpoint'));
//        $videoUrl = $ossClient->generatePresignedUrl(config('kai.other.aliyun.oss.bucket'), $video->video_path, time() + 30 * 60, 'GET');
//
//        foreach ($data as $k => $video) {
//            $data[$k]->thumbnail =
//        }

        return Respond::respondWithData(JsonResource::collection($videos));
    }

    public function videoModerationShow($videoId) {
        $video = AiVideoAudit::where('id', $videoId)
                             ->firstOrFail();

        $ossClient = new OssClient(config('kai.other.aliyun.oss.access_key_id'), config('kai.other.aliyun.oss.access_key_secret'), config('kai.other.aliyun.oss.endpoint'));
        $videoUrl = $ossClient->generatePresignedUrl(config('kai.other.aliyun.oss.bucket'), $video->video_path, time() + 30 * 60, 'GET');


        $video->url = $videoUrl;

        return Respond::respondWithData(JsonResource::make($video));
    }

}
