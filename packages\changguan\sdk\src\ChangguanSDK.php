<?php

namespace <PERSON>gua<PERSON>\SDK;

use Changguan\SDK\Auth\JWTDecoder;
use Changguan\SDK\Auth\SignatureValidator;
use Changguan\SDK\Crypto\GmSm;
use Changguan\SDK\Push\PushClient;
use Changguan\SDK\OAuth\OAuthClient;
use Changguan\SDK\Http\HttpClient;
use Changguan\SDK\Http\Tools;
use Changguan\SDK\Navigation\RedirectToBuilder;
use Changguan\SDK\Exceptions\SDKException;

/**
 * ChangguanSDK 主类
 *
 * 提供常观平台所有服务的统一接入点，包括：
 * - OAuth2.0认证服务
 * - JWT认证服务
 * - 消息推送
 * - 加密服务
 * - 签名验证
 * - 工具集
 * - 导航构建
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
class ChangguanSDK
{
    /**
     * SDK版本号
     */
    private const VERSION = '1.0.0';

    /**
     * 默认API基础URL
     */
    private const DEFAULT_API_URL = 'https://api.changguan.com';
    private const DEFAULT_OAUTH_URL = 'https://oauth.changguan.com';

    /**
     * 错误码定义
     */
    private const ERROR_CONFIG_MISSING = 4001;    // 配置缺失
    private const ERROR_CONFIG_INVALID = 4002;    // 配置无效
    private const ERROR_SERVICE_INIT = 4003;      // 服务初始化失败
    private const ERROR_SERVICE_UNAVAILABLE = 4004; // 服务不可用

    /**
     * @var array SDK配置信息
     */
    private array $config;

    /**
     * @var HttpClient|null HTTP客户端实例
     */
    private ?HttpClient $httpClient = null;

    /**
     * @var PushClient|null
     */
    private ?PushClient $pushClient = null;

    /**
     * @var OAuthClient|null
     */
    private ?OAuthClient $oauthClient = null;

    /**
     * @var GmSm|null
     */
    private ?GmSm $gmSm = null;

    /**
     * @var JWTDecoder|null
     */
    private ?JWTDecoder $jwtDecoder = null;

    /**
     * @var SignatureValidator|null
     */
    private ?SignatureValidator $signatureValidator = null;

    /**
     * @var Tools|null
     */
    private ?Tools $tools = null;

    /**
     * @var RedirectToBuilder|null
     */
    private ?RedirectToBuilder $redirectBuilder = null;

    /**
     * ChangguanSDK 构造函数
     *
     * @param array $config 配置数组，包含以下键：
     *      - client_id             客户端ID（必填）
     *      - client_access_key     客户端访问密钥（必填）
     *      - client_access_secret  客户端访问密钥secret（必填）
     *      - push_base_url         推送服务基础URL（可选，默认：https://api.changguan.com）
     *      - oauth_base_url        认证服务基础URL（可选，默认：https://oauth.changguan.com）
     *      - api_version           API版本号（可选，默认：v1）
     *      - http_timeout          HTTP请求超时时间（可选，默认：30）
     *      - http_connect_timeout  HTTP连接超时时间（可选，默认：10）
     *
     * @throws SDKException 当配置无效时抛出
     */
    public function __construct(array $config = []) {
        try {
            // 合并默认配置
            $this->config = array_merge([
                'client_id'            => '',
                'client_access_key'    => '',
                'client_access_secret' => '',
                'push_base_url'        => self::DEFAULT_API_URL,
                'oauth_base_url'       => self::DEFAULT_OAUTH_URL,
                'api_version'          => 'v1',
                'http_timeout'         => 30,
                'http_connect_timeout' => 10,
            ], $config);

            $this->validateConfig($this->config);
            $this->initializeServices();
        } catch (SDKException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new SDKException('SDK初始化失败: ' . $e->getMessage(), self::ERROR_SERVICE_INIT);
        }
    }

    /**
     * 获取HTTP客户端实例
     *
     * @param string|null $baseUrl 可选的基础URL，不提供则使用默认URL
     *
     * @return HttpClient
     * @throws SDKException|Exceptions\HttpException 当HTTP客户端未初始化时抛出
     */
    public function http(?string $baseUrl = null): HttpClient {
        if (!$this->httpClient) {
            throw new SDKException('HTTP客户端未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        if ($baseUrl) {
            return $this->httpClient->withBaseUrl($baseUrl);
        }

        return $this->httpClient;
    }

    /**
     * 获取推送客户端实例
     *
     * @return PushClient
     * @throws SDKException 当推送服务未初始化时抛出
     */
    public function push(): PushClient {
        if (!$this->pushClient) {
            throw new SDKException('推送服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->pushClient;
    }

    /**
     * 获取OAuth客户端实例
     *
     * @return OAuthClient
     * @throws SDKException 当OAuth服务未初始化时抛出
     */
    public function oauth(): OAuthClient {
        if (!$this->oauthClient) {
            throw new SDKException('OAuth服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->oauthClient;
    }

    /**
     * 获取国密工具实例
     *
     * @return GmSm
     * @throws SDKException 当加密服务未初始化时抛出
     */
    public function crypto(): GmSm {
        if (!$this->gmSm) {
            throw new SDKException('加密服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->gmSm;
    }

    /**
     * 获取JWT解码器实例
     *
     * @return JWTDecoder
     * @throws SDKException 当JWT服务未初始化时抛出
     */
    public function jwt(): JWTDecoder {
        if (!$this->jwtDecoder) {
            throw new SDKException('JWT服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->jwtDecoder;
    }

    /**
     * 获取签名验证器实例
     *
     * @return SignatureValidator
     * @throws SDKException 当签名服务未初始化时抛出
     */
    public function signature(): SignatureValidator {
        if (!$this->signatureValidator) {
            throw new SDKException('签名服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->signatureValidator;
    }

    /**
     * 获取HTTP工具集实例
     *
     * @return Tools
     * @throws SDKException 当工具服务未初始化时抛出
     */
    public function tools(): Tools {
        if (!$this->tools) {
            throw new SDKException('工具服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->tools;
    }

    /**
     * 获取导航构建器实例
     *
     * @return RedirectToBuilder
     * @throws SDKException 当导航服务未初始化时抛出
     */
    public function redirect(): RedirectToBuilder {
        if (!$this->redirectBuilder) {
            throw new SDKException('导航服务未初始化', self::ERROR_SERVICE_UNAVAILABLE);
        }

        return $this->redirectBuilder;
    }

    /**
     * 获取当前配置信息
     *
     * @return array
     */
    public function getConfig(): array {
        return $this->config;
    }

    /**
     * 获取SDK版本号
     *
     * @return string
     */
    public function getVersion(): string {
        return self::VERSION;
    }

    /**
     * 验证配置的有效性
     *
     * @param array $config 配置数组
     *
     * @throws SDKException 当配置无效时抛出
     */
    private function validateConfig(array $config): void {
        try {
            // 验证必填字段
            $required = ['client_id', 'client_access_key', 'client_access_secret'];
            foreach ($required as $field) {
                if (empty($config[$field])) {
                    throw new SDKException("缺少必填配置项: {$field}", self::ERROR_CONFIG_MISSING);
                }
            }

            // 验证URL格式
            if (!empty($config['push_base_url']) && !filter_var($config['push_base_url'], FILTER_VALIDATE_URL)) {
                throw new SDKException('推送服务URL格式无效', self::ERROR_CONFIG_INVALID);
            }

            if (!empty($config['oauth_base_url']) && !filter_var($config['oauth_base_url'], FILTER_VALIDATE_URL)) {
                throw new SDKException('认证服务URL格式无效', self::ERROR_CONFIG_INVALID);
            }

            // 验证客户端ID格式
            if (!preg_match('/^[a-zA-Z0-9_-]+$/', $config['client_id'])) {
                throw new SDKException('客户端ID格式无效', self::ERROR_CONFIG_INVALID);
            }

        } catch (SDKException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new SDKException('配置验证失败: ' . $e->getMessage(), self::ERROR_CONFIG_INVALID);
        }
    }

    /**
     * 初始化所有服务
     *
     * @throws SDKException 当服务初始化失败时抛出
     */
    private function initializeServices(): void {
        try {
            // HTTP客户端配置
            $httpConfig = [
                'timeout'         => $this->config['http_timeout'],
                'connect_timeout' => $this->config['http_connect_timeout'],
            ];

            // 初始化主HTTP客户端
            $this->httpClient = new HttpClient($this->config['client_id'], $this->config['client_access_secret'], '', $httpConfig);

            // 初始化国密工具
            $this->gmSm = new GmSm();
            $this->gmSm->configure($this->config['client_access_key'], $this->config['client_access_secret']);

            // 初始化推送客户端
            $this->pushClient = new PushClient($this->httpClient->withBaseUrl($this->config['push_base_url']));

            // 初始化OAuth客户端（使用OAuth专用的HTTP客户端）
            $oauthHttpClient = $this->httpClient->withBaseUrl($this->config['oauth_base_url']);
            $this->oauthClient = new OAuthClient($oauthHttpClient);

            // 初始化其他组件
            $this->jwtDecoder = new JWTDecoder($this->gmSm);
            $this->signatureValidator = new SignatureValidator($this->config['client_id'], $this->config['client_access_secret']);
            $this->tools = new Tools();
            $this->redirectBuilder = new RedirectToBuilder();

        } catch (SDKException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new SDKException('服务初始化失败: ' . $e->getMessage(), self::ERROR_SERVICE_INIT);
        }
    }

    /**
     * 重置服务实例
     * 用于在更新配置后重新初始化服务
     *
     * @throws SDKException 当重置失败时抛出
     */
    public function reset(): void {
        try {
            $this->httpClient = null;
            $this->pushClient = null;
            $this->oauthClient = null;
            $this->gmSm = null;
            $this->jwtDecoder = null;
            $this->signatureValidator = null;
            $this->tools = null;
            $this->redirectBuilder = null;

            $this->initializeServices();
        } catch (SDKException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new SDKException('服务重置失败: ' . $e->getMessage(), self::ERROR_SERVICE_INIT);
        }
    }
}
