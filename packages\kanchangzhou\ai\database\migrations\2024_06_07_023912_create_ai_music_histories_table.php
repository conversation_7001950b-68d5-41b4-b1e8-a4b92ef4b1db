<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ai_music_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->nullable()->comment('用户ID');
            $table->string('task_code')->nullable()->comment('任务码');
            $table->string('module')->nullable()->comment('模型名称');
            $table->string('provider')->nullable()->comment('服务提供者');
            $table->string('action')->nullable()->comment('生成音乐执行的方法');
            $table->string('prompt')->nullable()->comment('关键词');
            $table->string('options')->nullable()->comment('参数');
            $table->string('task_id')->nullable()->comment('任务ID');
            $table->string('result1')->nullable()->comment('结果1');
            $table->string('result2')->nullable()->comment('结果2');
            $table->string('music_url')->nullable()->comment('音乐地址');
            $table->string('lyric')->nullable()->comment('生成的歌词');
            $table->integer('state')->nullable()->comment('音乐生成进度，0生成中，1成功，2失败');
            $table->string('errmsg')->nullable()->comment('错误信息');
            $table->integer('custom_mode')->nullable()->comment('自定义模式,默认0为灵感模式;1-自定义模式');
            //$table->string('user_ip')->nullable()->comment('用户IP');
            $table->integer('finish_time');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_image_histories');
    }
};
