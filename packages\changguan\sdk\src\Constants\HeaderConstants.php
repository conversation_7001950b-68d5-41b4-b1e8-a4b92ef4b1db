<?php

namespace Changguan\SDK\Constants;

class HeaderConstants
{

    // 定义标准 header 键名常量
    // 应用ID
    public const APP_ID = 'CG-App-ID';
    // 客户端类型
    public const CLIENT_TYPE = 'CG-Client-Type';
    // 客户端版本
    public const CLIENT_VERSION = 'CG-Client-Version';
    // 客户端构建版本
    public const CLIENT_BUILD = 'CG-Client-Build';
    // 设备ID
    public const DEVICE_ID = 'CG-Device-ID';
    // 设备型号
    public const DEVICE_MODEL = 'CG-Device-Model';
    // 设备操作系统
    public const DEVICE_OS = 'CG-Device-OS';
    // 设备操作系统版本
    public const DEVICE_OS_VERSION = 'CG-Device-OS-Version';
    // 网络类型
    public const NETWORK_TYPE = 'CG-Network-Type';
    // 用户ID
    public const USER_ID = 'CG-User-ID';
    // 请求ID
    public const REQUEST_ID = 'CG-Request-ID';
    // 时间戳
    public const TIMESTAMP = 'CG-Timestamp';
    // 时区
    public const TIMEZONE = 'CG-Timezone';
    // API版本
    public const API_VERSION = 'CG-API-Version';
    // 语言
    public const LANGUAGE = 'CG-Language';
    // 地区
    public const REGION = 'CG-Region';
    // 渠道
    public const CHANNEL = 'CG-Channel';
    // 签名
    public const SIGNATURE = 'CG-Signature';
    // 随机字符串
    public const NONCE = 'CG-Nonce';
    // 客户端ID
    public const CLIENT_ID = 'CG-Client-ID';

    public const OS_IOS = 'ios';
    public const OS_IPADOS = 'ipados';
    public const OS_ANDROID = 'android';
    public const OS_HARMONYOS = 'harmonyos';
    public const OS_WEB = 'web';
    public const OS_PC = 'pc';
    public const OS_WECHAT = 'wechat';
}
