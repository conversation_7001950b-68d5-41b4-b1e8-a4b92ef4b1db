<?php

namespace Kanchangzhou\Advertisement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kanchangzhou\Kernel\Supports\Redirectable;

/**
 * Kanchangzhou\Advertisement\Models\Advertising
 *
 * @property int $id
 * @property string $title 广告标题
 * @property int $advertisers_id 广告商ID号
 * @property int $advertising_position_id 广告位ID号
 * @property int $status 状态
 * @property string $username 添加人
 * @property array $thumb_img 缩略图
 * @property string $start_show_time 开始显示时间
 * @property string $stop_show_time 结束显示时间
 * @property string $bz 备注信息
 * @property int $type 广告类型 0为图片 1为动图 2为视频
 * @property int $style 广告样式 0为全屏显示 1为非全屏显示
 * @property string $redirect_to 跳转地址
 * @property int $power 权重
 * @property int $clickNum 点击量
 * @property int $serve_time 投放时长
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $media_type 媒体资源文件类型 0为图片 1为视频
 * @property int $is_show_title 是否显示标题
 * @property int $display_times 广告媒体显示次数
 * @property-read \Kanchangzhou\Advertisement\Models\Advertiser|null $advertiserInfo
 * @property-read \Kanchangzhou\Advertisement\Models\AdvertisingPosition|null $advertisingPositionInfo
 * @property-read mixed $redirectable
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising query()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereAdvertisersId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereAdvertisingPositionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereBz($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereClickNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereDisplayTimes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereIsShowTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereMediaType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising wherePower($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereRedirectTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereServeTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereStartShowTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereStopShowTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereStyle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereThumbImg($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising whereUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Advertising withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdvertising
 */
class Advertising extends Model
{
    use SoftDeletes;
    //
    protected $table = 'advertising';

    protected $casts = [
        "thumb_img"=>"array"
    ];

    protected $appends = [
        // 其他appends参数
        'redirectable',
//        "thumb_img"
    ];

    public function advertiserInfo(){
        return $this->hasOne("Kanchangzhou\Advertisement\Models\Advertiser", "id", "advertisers_id");
    }

//    public function getThumbImgAttribute(){
//
//    }

    public function advertisingPositionInfo(){
        return $this->hasOne("Kanchangzhou\Advertisement\Models\AdvertisingPosition", "id", "advertising_position_id");
    }

    protected function serializeDate(\DateTimeInterface $date) : string
    {
        return $date->format($this->getDateFormat());
    }

    public function getRedirectableAttribute(){
        return Redirectable::make()->buildRedirectableByString($this->redirect_to)->toArray();
    }
}
