<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use AlibabaCloud\Chatbot\V20171011\Chat;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Supports\SensenovaRequest;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

/**
 * @deprecated 1.0.0
 * Class ChatController
 * @package Kanchangzhou\AI\Http\AdminControllers
 */
class ChatController extends BaseController
{
    protected $chatUuid;

    public function index(Request $request) {
        PermissionHook::can('智能对话.对话');

        $uri = '/llm/chat-completions';

        $this->validate($request, [
            'content' => 'required|max:1900',
        ], []);

        $messages = [];

        if ($request->input('chat_uuid') && Cache::has($request->input('chat_uuid'))) {
            $chatUuid = $request->input('chat_uuid');
        } else {
            $chatUuid = Str::uuid()
                           ->toString();
        }

        if (Cache::has($chatUuid)) {
            $history = Cache::get($chatUuid);
            $totalToken = $history['usage']['total_tokens'];
            foreach ($history['messages'] as $k => $message) {
                if ($totalToken > 1000) {
                    $totalToken = $totalToken - Str::length($message['content']);
                    array_shift($history['messages']);
                    continue;
                }
                $messages[] = $message;
            }
        }

        $messages[] = [
            "role" => "user",
            "content" => $request->input('content'),
        ];

        ChatHistory::create([
            'chat_uuid' => $chatUuid,
            'chat_model_id' => 'nova-ptc-xl-v1',
            'message' => $request->input('content'),
            'role' => 'user',
            'provider' => 'sense',
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
        ]);

        $body = [
            'model' => 'nova-ptc-xl-v1',
            'max_new_tokens' => 2048,
            'temperature' => 1,
            'repetition_penalty' => 1.07,
            'messages' => $messages,
        ];

        $response = SensenovaRequest::postRequest($uri, $body);

        if ($response->successful()) {
            $messages[] = [
                'role' => 'assistant',
                'content' => $response['data']['choices'][0]['message'],
            ];


            ChatHistory::create([
                'chat_uuid' => $chatUuid,
                'chat_model_id' => 'nova-ptc-xl-v1',
                'message' => $response['data']['choices'][0]['message'],
                'role' => 'assistant',
                'provider' => 'sense',
                'user_id' => AuthFacade::adminUser()
                                       ->getId(),
            ]);

            Cache::put($chatUuid, [
                'messages' => $messages,
                'usage' => $response['data']['usage'],
            ], Carbon::now()
                     ->addDay());

            return Respond::respondWithData([
                'chat_uuid' => $chatUuid,
                'data' => $response['data'],
            ]);
        }

        return Respond::respondWithData($response);
    }

    public function chatHistory() {
        PermissionHook::can('智能对话.对话');

        $history = ChatHistory::where('user_id', AuthFacade::adminUser()
                                                           ->getId())
                              ->where('created_at', '>=', Carbon::now()
                                                                ->subMonths(3))
                              ->where('type', ChatHistory::TYPE_CHAT)
                              ->whereNull('chat_role_id')
                              ->groupBy('chat_uuid')
                              ->orderByDesc('id')
                              ->take(10)
                              ->get();

        return Respond::respondWithData(JsonResource::collection($history));
    }

    public function chatHistoryDetail(Request $request, $chatUuid) {
        PermissionHook::can('智能对话.对话');

        $history = ChatHistory::where('chat_uuid', $chatUuid)
                              ->when($request->input('last_id'), function ($query, $lastId) {
                                  $query->where('id', '<', $lastId);
                              })
                              ->where('is_retry', ChatHistory::IS_RETRY_NO)
                              ->orderByDesc('id')
                              ->take(15)
                              ->get()
                              ->sortBy('id');

        return Respond::respondWithData(JsonResource::collection($history));
    }

    public function delHistory($chatUuid) {
        PermissionHook::can('智能对话.对话');

        ChatHistory::where('chat_uuid', $chatUuid)
                   ->delete();

        return Respond::respondWithData();
    }

    public function roleChat(Request $request) {
        PermissionHook::can('智能对话.对话');

        $this->validate($request, [
            'content' => 'required|max:1900',
        ], []);

        $role = null;
        if ($request->input('role_id')) {
            $role = ChatRole::where('id', $request->input('role_id'))
                            ->first();
        }

        if ($request->input('chat_uuid') && Cache::has($request->input('chat_uuid'))) {
            $chatUuid = $request->input('chat_uuid');
        } else {
            // $chatUuid = Str::uuid();

            // 创建会话
            $uri = 'llm/chat/sessions';
            $body = [
                'system_prompt' => [
                    [
                        'role' => 'user',
                        'content' => $role ? $role->system_prompt : '开启会话',
                    ],
                ],
            ];
            $chatSession = SensenovaRequest::postRequest($uri, $body);

            if ($chatSession->failed()) {
                return Respond::respondWithData($chatSession);
            }

            $chatUuid = $chatSession->json('session_id');
        }

        $messages[] = [
            "role" => "user",
            "content" => $request->input('content'),
        ];

        ChatHistory::create([
            'chat_uuid' => $chatUuid,
            'chat_model_id' => 'nova-ptc-xl-v1',
            'message' => $request->input('content'),
            'role' => 'user',
            'provider' => 'sense',
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'chat_role_id' => $role?->role_uuid,
        ]);

        $uri = 'llm/chat-conversations';
        $body = [
            'action' => 'next',
            'content' => $request->input('content'),
            'model' => 'nova-ptc-xl-v1',
            'session_id' => $chatUuid,
            'max_new_tokens' => 2048,
            'temperature' => 1.1,
            'repetition_penalty' => 1.08,
        ];


        $response = SensenovaRequest::postRequest($uri, $body);

        if ($response->successful()) {

            ChatHistory::create([
                'chat_uuid' => $chatUuid,
                'chat_model_id' => 'nova-ptc-xl-v1',
                'message' => $response['data']['message'],
                'role' => 'assistant',
                'provider' => 'sense',
                'user_id' => AuthFacade::adminUser()
                                       ->getId(),
                'chat_role_id' => $role ? $role->id : 0,
            ]);

            Cache::put($chatUuid, true, Carbon::now()
                                              ->addDay());

            return Respond::respondWithData([
                'chat_uuid' => $chatUuid,
                'data' => $response['data'],
            ]);
        }

        return Respond::respondWithData($response);
    }

    public function chatModules() {
        return Respond::respondWithData([
            [
                'module' => 'qianwen',
                'label' => '千问',
            ],
            [
                'module' => 'qianwen-baichuan',
                'label' => '百川',
            ],
            [
                'module' => 'qianwen-glm',
                'label' => 'GLM',
            ],
            //            [
            //                'id' => 'qianwen-llm',
            //                'name' => '千问-LLM',
            //            ],
        ]);
    }

    /**
     * @throws AIException
     * @throws ValidationException
     */
    public function chatNew(Request $request) {
        $this->validate($request, [
            'prompt' => [
                Rule::requiredIf(function () use ($request) {
                    return !$request->input('is_retry');
                }),
                'max:1900',
            ],
            'module_key' => 'required',
            'chat_uuid' => 'required_if:is_retry,1',
            'is_retry' => '',
        ], []);

        switch ($request->input('module_key')) {
            case 'qianwen-baichuan':
                return $this->qianwenBaichuan($request);
                break;
            case 'qianwen-glm':
                return $this->qianwenGLM($request);
                break;
            case 'qianwen-vl':
                return $this->qianwenVL($request);
                break;
            case 'qianwen-llm':
                return $this->qianwenLLM($request);
                break;
            case 'qianwen':
            default:
                return $this->qianwen($request);
                break;
        }
    }

    public function likeThis(Request $request) {
        $this->validate($request, [
            'chat_uuid' => 'required',
            'message_id' => 'required',
        ], []);

        ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                   ->where('id', $request->input('message_id'))
                   ->update([
                       'is_like' => ChatHistory::IS_LIKE_YES,
                   ]);

        return Respond::respondWithData();
    }

    public function dontLikeThis(Request $request) {
        $this->validate($request, [
            'chat_uuid' => 'required',
            'message_id' => 'required',
        ], []);

        ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                   ->where('id', $request->input('message_id'))
                   ->update([
                       'is_like' => ChatHistory::IS_LIKE_NO,
                   ]);

        return Respond::respondWithData();
    }

    public function qianwen(Request $request) {
//        $this->validate($request, [
//            'content' => 'required|max:1900',
//        ], []);


        $body = [
            'model' => 'qwen-max',
            'input' => [
                'messages' => $this->_formatMessagesWithHistory($request),
            ],
            'parameters' => [
                'result_format' => 'message',
                'top_p' => 0.75,
                'top_k' => 63,
                'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                'temperature' => 1,
                'enable_search' => true,
            ],
        ];

        $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->post($uri, $body);

        if ($res->successful()) {
            // 保存历史记录, 用户输入和机器人回复
            if (!$request->input('is_retry')) {
                ChatHistory::create([
                    'chat_uuid' => $this->chatUuid,
                    'chat_model_id' => 'qwen-plus',
                    'message' => $request->input('prompt'),
                    'role' => 'user',
                    'provider' => 'dashscope',
                    'user_id' => AuthFacade::adminUser()
                                           ->getId(),
                    'is_retry' => ChatHistory::IS_RETRY_NO,
                    'input_tokens' => $res->json('usage.input_tokens'),
                ]);
            }

            if ($request->input('is_retry')) {
                ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                           ->where('role', 'assistant')
                           ->where('is_retry', ChatHistory::IS_RETRY_NO)
                           ->orderByDesc('id')
                           ->first()
                           ->update([
                               'is_retry' => ChatHistory::IS_RETRY_YES,
                           ]);
            }

            $reply = ChatHistory::create([
                'chat_uuid' => $this->chatUuid,
                'chat_model_id' => 'qwen-plus',
                'message' => $res->json('output.choices.0.message.content'),
                'role' => 'assistant',
                'provider' => 'dashscope',
                'user_id' => AuthFacade::adminUser()
                                       ->getId(),
                'is_retry' => ChatHistory::IS_RETRY_NO,
                'total_tokens' => $res->json('usage.total_tokens'),
                'output_tokens' => $res->json('usage.output_tokens'),
                'provider_request_id' => $res->json('request_id'),
            ]);


            return Respond::respondWithData([
                'chat_uuid' => $this->chatUuid,
                'message' => $res->json('output.choices.0.message.content'),
                'message_id' => $reply->id,
            ]);
        }

        throw new AIException(AIException::NETWORK_ERROR);
    }

    public function qianwenBaichuan(Request $request) {
//        $this->validate($request, [
//            'content' => 'required|max:1900',
//        ], []);

        $body = [
            'model' => 'baichuan2-13b-chat-v1',
            'input' => [
                'messages' => $this->_formatMessagesWithHistory($request),
            ],
        ];

        $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->post($uri, $body);

        if ($res->successful()) {
            // 保存历史记录, 用户输入和机器人回复
            if (!$request->input('is_retry')) {
                ChatHistory::create([
                    'chat_uuid' => $this->chatUuid,
                    'chat_model_id' => 'baichuan2-13b-chat-v1',
                    'message' => $request->input('prompt'),
                    'role' => 'user',
                    'provider' => 'dashscope',
                    'user_id' => AuthFacade::adminUser()
                                           ->getId(),
                    'is_retry' => ChatHistory::IS_RETRY_NO,
                    'input_tokens' => $res->json('usage.input_tokens'),
                ]);
            }


            if ($request->input('is_retry')) {
                ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                           ->where('role', 'assistant')
                           ->where('is_retry', ChatHistory::IS_RETRY_NO)
                           ->orderByDesc('id')
                           ->first()
                           ->update([
                               'is_retry' => ChatHistory::IS_RETRY_YES,
                           ]);
            }

            $reply = ChatHistory::create([
                'chat_uuid' => $this->chatUuid,
                'chat_model_id' => 'baichuan2-13b-chat-v1',
                'message' => $res->json('output.choices.0.message.content'),
                'role' => 'assistant',
                'provider' => 'dashscope',
                'user_id' => AuthFacade::adminUser()
                                       ->getId(),
                'is_retry' => ChatHistory::IS_RETRY_NO,
                'output_tokens' => $res->json('usage.output_tokens'),
                'provider_request_id' => $res->json('request_id'),
            ]);

            return Respond::respondWithData([
                'chat_uuid' => $this->chatUuid,
                'message' => $res->json('output.choices.0.message.content'),
                'message_id' => $reply->id,
            ]);
        }

        throw new AIException(AIException::NETWORK_ERROR);
    }

    public function qianwenGLM(Request $request) {
//        $this->validate($request, [
//            'content' => 'required|max:1900',
//        ], []);

        $body = [
            'model' => 'chatglm3-6b',
            'input' => [
                'messages' => $this->_formatMessagesWithHistory($request),
            ],
        ];

        $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->post($uri, $body);

        if ($res->successful()) {
            // 保存历史记录, 用户输入和机器人回复
            if (!$request->input('is_retry')) {
                ChatHistory::create([
                    'chat_uuid' => $this->chatUuid,
                    'chat_model_id' => 'chatglm3-6b',
                    'message' => $request->input('prompt'),
                    'role' => 'user',
                    'provider' => 'dashscope',
                    'user_id' => AuthFacade::adminUser()
                                           ->getId(),
                    'is_retry' => ChatHistory::IS_RETRY_NO,
                    'input_tokens' => $res->json('usage.input_tokens'),
                ]);
            }


            if ($request->input('is_retry')) {
                ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                           ->where('role', 'assistant')
                           ->where('is_retry', ChatHistory::IS_RETRY_NO)
                           ->orderByDesc('id')
                           ->first()
                           ->update([
                               'is_retry' => ChatHistory::IS_RETRY_YES,
                           ]);
            }

            $reply = ChatHistory::create([
                'chat_uuid' => $this->chatUuid,
                'chat_model_id' => 'chatglm3-6b',
                'message' => $res->json('output.choices.0.message.content'),
                'role' => 'assistant',
                'provider' => 'dashscope',
                'user_id' => AuthFacade::adminUser()
                                       ->getId(),
                'is_retry' => ChatHistory::IS_RETRY_NO,
                'output_tokens' => $res->json('usage.output_tokens'),
                'provider_request_id' => $res->json('request_id'),
            ]);

            return Respond::respondWithData([
                'chat_uuid' => $this->chatUuid,
                'message' => $res->json('output.choices.0.message.content'),
                'message_id' => $reply->id,
            ]);
        }

        throw new AIException(AIException::NETWORK_ERROR);
    }

    public function qianwenLLM(Request $request) {
//        $this->validate($request, [
//            'content' => 'required|max:1900',
//        ], []);

        $body = [
            'model' => 'llama2-13b-chat-v2',
            'input' => [
                'messages' => $this->_formatMessagesWithHistory($request),
            ],
        ];

        $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->post($uri, $body);

        if ($res->successful()) {
            // 保存历史记录, 用户输入和机器人回复
            if (!$request->input('is_retry')) {
                ChatHistory::create([
                    'chat_uuid' => $this->chatUuid,
                    'chat_model_id' => 'llama2-13b-chat-v2',
                    'message' => $request->input('prompt'),
                    'role' => 'user',
                    'provider' => 'dashscope',
                    'user_id' => AuthFacade::adminUser()
                                           ->getId(),
                    'is_retry' => ChatHistory::IS_RETRY_NO,
                    'input_tokens' => $res->json('usage.input_tokens'),
                ]);
            }

            if ($request->input('is_retry')) {
                ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                           ->where('role', 'assistant')
                           ->where('is_retry', ChatHistory::IS_RETRY_NO)
                           ->orderByDesc('id')
                           ->first()
                           ->update([
                               'is_retry' => ChatHistory::IS_RETRY_YES,
                           ]);
            }

            $reply = ChatHistory::create([
                'chat_uuid' => $this->chatUuid,
                'chat_model_id' => 'llama2-13b-chat-v2',
                'message' => $res->json('output.text'),
                'role' => 'assistant',
                'provider' => 'dashscope',
                'user_id' => AuthFacade::adminUser()
                                       ->getId(),
                'is_retry' => ChatHistory::IS_RETRY_NO,
                'output_tokens' => $res->json('usage.output_tokens'),
                'provider_request_id' => $res->json('request_id'),
            ]);

            return Respond::respondWithData([
                'chat_uuid' => $this->chatUuid,
                'message' => $res->json('output.choices.0.message.content'),
                'message_id' => $reply->id,
            ]);
        }

        throw new AIException(AIException::NETWORK_ERROR);
    }

    public function qianwenVL(Request $request) {
//        $this->validate($request, [
//            'content' => 'required|max:1900',
//        ], []);

        $body = [
            'model' => 'qwen-vl-chat-v1',
            'input' => [
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => [
                            ["image" => $request->input('image')],
                            ["text" => $request->input('prompt')],
                        ],
                    ],
                ],
            ],
        ];

        $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation';
        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->asJson()
                   ->post($uri, $body);

        return $res->json();
    }

    protected function _formatPromptsWithHistory(Request $request) {
        $prompts = [];
        if ($request->input('chat_uuid')) {
            $chatUuid = $request->input('chat_uuid');
        } else {
            $chatUuid = Str::uuid()
                           ->toString();
        }

        $this->chatUuid = $chatUuid;

        $histories = ChatHistory::where('chat_uuid', $chatUuid)
                                ->orderByDesc('id')
                                ->take(10)
                                ->get()
                                ->sortBy('id');

        // 根据历史记录ID顺序排序, 保证历史记录顺序, 生成prompts
        $prompts['history'] = [];
        foreach ($histories as $history) {
            $prompts['history'][] = $history->message;
        }

        $prompts['prompt'] = $request->input('prompt');

        return $prompts;
    }

    protected function _cachePrompts($cacheKey, $prompts) {

    }

    protected function _formatMessagesWithoutHistory(Request $request) {
        $messages = [];
        if ($request->input('chat_uuid')) {
            $chatUuid = $request->input('chat_uuid');
        } else {
            $chatUuid = Str::uuid()->toString();
        }

        $this->chatUuid = $chatUuid;

        $messages[] = [
            'role' => 'user',
            'content' => $request->input('prompt'),
        ];

        return $messages;
    }

    protected function _formatMessagesWithHistory(Request $request) {
        $messages = [];
        if ($request->input('chat_uuid')) {
            $chatUuid = $request->input('chat_uuid');
        } else {
            $chatUuid = Str::uuid()->toString();
        }

        $this->chatUuid = $chatUuid;

        $histories = ChatHistory::where('chat_uuid', $chatUuid)
                                ->where('is_retry', ChatHistory::IS_RETRY_NO)
                                ->orderByDesc('id')
                                ->take(10)
                                ->get()
                                ->sortBy('id');

        if ($request->input('is_retry')) {
            // 根据历史记录ID顺序排序, 保证历史记录顺序, 生成prompts
            foreach ($histories as $history) {
                $messages[] = [
                    'role' => $history->role,
                    'content' => $history->message,
                ];
            }

            array_pop($messages);

        } else {
            // 根据历史记录ID顺序排序, 保证历史记录顺序, 生成prompts
            foreach ($histories as $history) {
                $messages[] = [
                    'role' => $history->role,
                    'content' => $history->message,
                ];
            }

            $messages[] = [
                'role' => 'user',
                'content' => $request->input('prompt'),
            ];
        }

        return $messages;
    }

    protected function _cacheMessages() {

    }
}
