<?php

namespace Kanchangzhou\AI\Services\Image\Contacts;

use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Models\AiImageHistory;
use Kanchangzhou\AI\Models\AiImageUrl;

/**
 * Class AIChatService
 * @package Kanchangzhou\AI\Services\Chat\Contacts
 * @property  string $prompt 用户输入
 * @property  string $moduleName 模块名称
 * @property  string $moduleKey 模块key
 * @property  \Illuminate\Http\Request $request 请求对象
 */
abstract class AIImageService
{
    protected $request;
    protected $historyCount = 10;
    protected $user;

    /**
     * @param AiUser $user
     * @param $request
     */
    public function __construct(AiUser $user, $request = null) {
        $this->user = $user;
        $this->request = $request;
    }

    abstract public function handler($action);

    protected function createHistory($res) {

        $data = [
            'prompt' => $res['prompt'],
            'module' => $res['module'],
            'provider' => $res['provider'],
            'width' => $res['width'] ?? null,
            'height' => $res['height'] ?? null,
            'image_num' => $res['image_num'] ?? 1,
            'style' => $res['style'] ?? null,
            'image_base64' => $res['image_base64'] ?? null,
            'image_url' => $res['image_url'] ?? null,
            'change_degree' => $res['change_degree'] ?? null,
            'progress' => $res['progress'] ?? 0,
            'point' => $res['point'] ?? null,
            'price' => $res['price'] ?? null,
            'status' => $res['status'] ?? 'INIT',
            'task_id' => $res['task_id'],
            'log_id' => $res['log_id'] ?? null,
            'user_id' => $this->user->getUserId(),
            'guard_name' => $this->user->getGuardName(),
            'action' => $res['action'] ?? null,
        ];

        // 更新或创建的数据
        return AiImageHistory::create($data);
    }

    protected function updateHistory(&$res) {

        if ($res['status'] == 'SUCCESS') {
            $history = AiImageHistory::where('task_id', $res['task_id'])
                                     ->first();

            if ($history) {
                $history->update([
                    'log_id' => $res['log_id'],
                    'task_id' => $res['task_id'],
                    'status' => $res['status'],
                    'progress' => $res['progress'],
                    'user_id' => $this->user->getUserId(),
                    'guard_name' => $this->user->getGuardName(),
                ]);

                if ($res['result_list']) {
                    //$history->imageUrl()->createMany($res['images']);
                    foreach ($res['result_list'] as $images) {
                        $data = [];
                        $data['image_orgin'] = $images['image_orgin'];
                        $data['image_local'] = $images['image_local'];
                        $data['history_id'] = $history['id'];
                        $data['task_id'] = $history['task_id'];
                        $data['log_id'] = $history['log_id'];
                        $data['user_id'] = $history['user_id'];
                        $data['guard_name'] = $history['guard_name'];

                        $data['status'] = $images['status'] ?? null;
                        $data['progress'] = $images['progress'] ?? null;
                        $data['error_code'] = $images['error_code'] ?? null;
                        $data['approve_conclusion'] = $images['approve_conclusion'] ?? null;

                        /*
                         * 会生成两条数据
                        AiImageUrl::updateOrCreate(['image_orgin' => $images['image_orgin']], // 如果图片存在，则进行更新
                            $data // 更新的数据
                        );
                        */

                        // Check if the record already exists
                        $existing = AiImageUrl::where('image_local', $images['image_local'])->first();

                        if ($existing) {
                            $existing->update($data);
                        } else {
                            AiImageUrl::create($data);
                        }

                    }
                }
            }
        }

        return $res;
    }

    protected function createHistoryUrl($res) {

        if ($res['status'] == 'SUCCESS') {

            $history_data = [
                'prompt' => $res['prompt'],
                'revised_prompt' => $res['revised_prompt'],
                'module' => $res['module'],
                'provider' => $res['provider'],
                'width' => $res['width'],
                'height' => $res['height'],
                'image_num' => $res['image_num'],
                'style' => $res['style'] ?? null,
                'image_base64' => $res['image_base64'] ?? null,
                'image_url' => $res['image_url'] ?? null,
                'change_degree' => $res['change_degree'] ?? null,
                'progress' => $res['progress'] ?? null,
                'point' => $res['point'] ?? null,
                'price' => $res['price'] ?? null,
                'status' => 'SUCCESS',
                'log_id' => $res['log_id'],
                'task_id' => $res['task_id'],
                'user_id' => $this->user->getUserId(),
                'guard_name' => $this->user->getGuardName(),
            ];

            $history = AiImageHistory::create($history_data);

            if ($res['result_list']) {
                $result_list = [];
                foreach ($res['result_list'] as $key => $images) {
                    $url_data = [
                        'image_orgin' => $images['image_orgin'],
                        'image_base64' => $images['image_base64'] ?? null,
                        'image_local' => $images['image_local'],
                        'status' => $images['status'] ?? null,
                        'progress' => $images['progress'] ?? null,
                        'error_code' => $images['error_code'] ?? null,
                        'approve_conclusion' => $images['approve_conclusion'] ?? null,
                        'history_id' => $history['id'],
                        'task_id' => $history['task_id'],
                        'log_id' => $history['log_id'],
                        'user_id' => $history['user_id'],
                        'guard_name' => $history['guard_name'],
                    ];
                    $result_list[$key] = AiImageUrl::create($url_data);
                }
                $history['result_list'] = $result_list;
            }
        }

        return $history;
    }

    /**
     * 获取历史消息
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Query\Builder[]|\Illuminate\Support\Collection|ChatHistory[]
     */
    public function getHistory() {
        return AIImageHistory::with(['AiImageUrl'])
                             ->where('user_id', $this->user->getUserId())
                             ->where('guard_name', $this->user->getGuardName())
                             ->take($this->historyCount)
                             ->orderByDesc('id')
                             ->get();
    }

    public function getHistoryCount() {
        return $this->historyCount;
    }
}
