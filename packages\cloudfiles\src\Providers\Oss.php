<?php

namespace Rjustice\CloudFiles\Providers;

use AlibabaCloud\Client\Support\Path;
use AlibabaCloud\SDK\OSS\OSS\DeleteMultipleObjectsRequest\body\delete\object;
use League\Flysystem\AdapterInterface;
use League\Flysystem\Util;
use OSS\Core\OssException;
use OSS\OssClient;

class Oss extends AbstractCloudFiles
{
    protected $accessKey;
    protected $secretKey;
    protected $cdnDomain;
    protected $expire;
    protected $endpoint;
    protected $internalEndpoint;
    protected $bucket;
    protected $acl;
    protected $debug;
    protected $ssl;
    protected $signature;

    protected static $resultMap = [
        'Body' => 'raw_contents',
        'Content-Length' => 'size',
        'ContentType' => 'mimetype',
        'Size' => 'size',
        'StorageClass' => 'storage_class',
    ];

    /**
     * @var OssClient
     */
    protected $client;

    protected $options = [];

    public function config($config) {
        $this->accessKey = $config['access_key'];
        $this->secretKey = $config['secret_key'];
        $this->cdnDomain = $config['cdndomain'] ?? '';
        $this->endpoint = $config['endpoint'];
        $this->internalEndpoint = $config['internal_endpoint'] ?? '';
        $this->bucket = $config['bucket'];
        $this->expire = $config['expire'] ?? 3600;
        $this->acl = $config['acl'] ?? 0;
        $this->debug = $config['debug'] ?? false;
        $this->ssl = $config['ssl'] ?? false;
        $this->signature = $config['signature'] ?? OssClient::OSS_SIGNATURE_VERSION_V1;

        $endpoint = $this->internalEndpoint ?: $this->endpoint;

        $this->client = new OssClient([
            'key' => $this->accessKey,
            'secret' => $this->secretKey,
            'endpoint' => $endpoint,
        ]);

        return $this;
    }

    public function multiUpload($path, $file, $options = [
        OssClient::OSS_CHECK_MD5 => true,
        OssClient::OSS_PART_SIZE => 1024 * 100,
    ]) {
        try {
            $result = $this->client->multiuploadFile($this->bucket, ltrim($path, '/'), $file, $options);

            return $result['oss-request-url'];
        } catch (OssException $e) {
            return false;
        }
    }

    public function policy($callbackRoute = '', $dir = '', $expire = 300, $contentLengthRange = 1024 * 1024 * 1000) {
        $callbackParam = [
            'callbackUrl' => $callbackRoute,
            'callbackBody' => 'filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}&etag=${etag}&contentMd5=${contentMd5}',
            'callbackBodyType' => 'application/x-www-form-urlencoded',
        ];

        $callbackString = json_encode($callbackParam);
        $base64CallbackBody = base64_encode($callbackString);
        $now = time();
        $end = $now + $expire;
        $host = $this->cdnDomain;
        $dir = ltrim($dir, '/') . '/';
        $conditions = [
            [
                'bucket' => $this->bucket,
            ],
            [
                'starts-with',
                '$key',
                $dir,
            ],
            [
                'eq',
                '$success_action_status',
                '200',
            ],
            [
                'callback' => $base64CallbackBody,
            ],
            [
                'content-length-range',
                0,
                $contentLengthRange,
            ],
        ];
        $policyArr = [
            'expiration' => str_replace('+00:00', '.000Z', gmdate('c', $end)),
            'conditions' => $conditions,
        ];

        $policy = base64_encode(json_encode($policyArr));
        $signature = base64_encode(hash_hmac('sha1', $policy, $this->secretKey, true));

        $response = [];

        $response['data']['OSSAccessKeyId'] = $this->accessKey;
        $response['data']['Policy'] = $policy;
        $response['data']['Signature'] = $signature;
        $response['data']['Expire'] = $end;
        $response['data']['success_action_status'] = 200;
        $response['data']['host'] = $host;
        $response['data']['key'] = $dir;
        $response['data']['callback'] = $base64CallbackBody;

        $response['expire'] = $expire;
        $response['expire_at'] = date('Y-m-d H:i:s', $end);

        return $response;

    }

    public function verify() {
        $auth = [
            'authorizationBase64' => $_SERVER['HTTP_AUTHORIZATION'],
            'pubKeyUrlBase64' => $_SERVER['HTTP_X_OSS_PUB_KEY_URL'],
            'path' => $_SERVER['REQUEST_URI'],
            'body' => file_get_contents('php://input'),
        ];

        if ($auth['authorizationBase64'] == '' || $auth['pubKeyUrlBase64'] == '') {
            throw new \Exception('authorizationBase64 or pubKeyUrlBase64 is empty');
        }

        $pubKey = file_get_contents(base64_decode($auth['pubKeyUrlBase64']));

        if ($pubKey == '') {
            throw new \Exception('pubKey is empty');
        }

        if (($pos = strpos($auth['path'], '?')) === false) {
            $authStr = urldecode($auth['path']) . "\n" . $auth['body'];
        } else {
            $authStr = urldecode(substr($auth['path'], 0, $pos)) . substr($auth['path'], $pos, strlen($auth['path']) - $pos) . "\n" . $auth['body'];
        }

        if (!openssl_verify($authStr, base64_decode($auth['authorizationBase64']), $pubKey, OPENSSL_ALGO_MD5)) {
            throw new \Exception('verify failed');
        }

        return true;
    }

    public function base64($path, $data) {
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $data, $result)) {
            $contents = base64_decode(substr($data, strpos($data, ',') + 1));

            return $this->write($path, $contents);
        }

        throw new OssException('base64 error');
    }

    public function write($path, $contents, $config = null) {
        try {
            $res = $this->client->putObject($this->bucket, $path, $contents);
        } catch (OssException $exception) {
            throw new OssException($exception->getMessage());
        }

        return true;
    }

    public function writeStream($path, $resource, $config = null) {
        $contents = stream_get_contents($resource);

        return $this->write($path, $contents);
    }

    public function writeFile($path, $filePath, $config = null) {
        try {
            $this->client->uploadFile($this->bucket, $path, $filePath, false);
        } catch (OssException $exception) {
            throw new OssException($exception->getMessage());
        }

        return true;
    }

    public function update($path, $contents, $config = null) {
        return $this->write($path, $contents, $config);
    }

    public function updateStream($path, $resource, $config) {
        $contents = stream_get_contents($resource);

        return $this->writeStream($path, $resource, $config);
    }

    public function rename($path, $newPath) {
        if (!$this->copy($path, $newPath)) {
            return false;
        }

        return $this->delete($path);
    }

    public function copy($path, $newPath) {
        try {
            $this->client->copyObject($this->bucket, $path, $this->bucket, $newPath);
        } catch (OssException $exception) {
            throw new OssException($exception->getMessage());
        }

        return true;
    }

    public function delete($path) {
        if (is_string($path)) {
            try {
                $this->client->deleteObject($this->bucket, $path);
            } catch (OssException $exception) {
                throw new OssException($exception->getMessage());
            }
        } elseif (is_array($path)) {
            try {
                $this->client->deleteObjects($this->bucket, $path);
            } catch (OssException $exception) {
                throw new OssException($exception->getMessage());
            }
        }

        return true;
    }

    public function has($path) {
        return $this->client->doesObjectExist($this->bucket, $path);
    }

    public function deleteDir($dirname) {
        $dirname = rtrim($dirname, '/') . '/';
        $dirObjects = $this->listDirObjects($dirname, true);
        if (count($dirObjects['objects']) > 0) {
            foreach ($dirObjects['objects'] as $object) {
                $objects[] = $object['Key'];
            }

            try {
                $this->client->deleteObjects($this->bucket, $objects);
            } catch (OssException $e) {
                throw new OssException($e->getMessage());
            }
        }

        try {
            $this->client->deleteObject($this->bucket, $dirname);
        } catch (OssException $e) {
            throw new OssException($e->getMessage());
        }

        return true;
    }

    public function createDir($dirname) {
        try {
            $this->client->createObjectDir($this->bucket, $dirname, false);
        } catch (OssException $e) {
            throw new OssException($e->getMessage());
        }

        return true;
    }

    public function readObject($path) {
        $result['Body'] = $this->client->getObject($this->bucket, $path);
        $result = array_merge($result, ['type' => 'file']);

        return $this->normalizeResponse($result, $path);
    }

    public function read($path) {
        $result = $this->readObject($path);

        $result['contents'] = (string)$result['Body'];

        unset($result['Body']);

        return $this->normalizeResponse($result, $path);
    }

    public function readStream($path) {
        $result = $this->readObject($path);
        $result['stream'] = $result['raw_contents'];
        rewind($result['stream']);
        $result['raw_contents']->detachStream();
        unset($result['raw_contents']);

        return $this->normalizeResponse($result, $path);
    }

    public function getMetadata($path) {
        try {
            $objectMeta = $this->client->getObjectMeta($this->bucket, $path);
        } catch (OssException $e) {
            throw new OssException($e->getMessage());
        }

        return $this->normalizeResponse($objectMeta, $path);
    }

    public function getSize($path) {
        $object = $this->getMetadata($path);
        $object['size'] = $object['content-length'];

        return $this->normalizeResponse($object, $path);
    }

    public function getMimeType($path) {
        $object = $this->getMetadata($path);
        $object['mimetype'] = $object['content-type'];

        return $this->normalizeResponse($object, $path);
    }

    public function getTimestamp($path) {
        $object = $this->getMetadata($path);
        $object['timestamp'] = strtotime($object['last-modified']);

        return $this->normalizeResponse($object, $path);
    }

    protected function getBucketAcl() {
        try {
            $res['visibility'] = $this->client->getBucketAcl($this->bucket);
        } catch (OssException $e) {
            throw new OssException($e->getMessage());
        }

        return $res;
    }

    public function getVisibility($path) {
        try {
            $res['visibility'] = $this->client->getObjectAcl($this->bucket, $path);
        } catch (OssException $e) {
            throw new OssException($e->getMessage());
        }

        if ($res['visibility'] == 'default') {
            $res['visibility'] = $this->getBucketAcl();
        }

        switch ($res['visibility']) {
            default:
            case OssClient::OSS_ACL_TYPE_PRIVATE:
                $res['visibility'] = OssClient::OSS_ACL_TYPE_PRIVATE;
                break;

            case OssClient::OSS_ACL_TYPE_PUBLIC_READ:
                $res['visibility'] = OssClient::OSS_ACL_TYPE_PUBLIC_READ;
                break;
            case OssClient::OSS_ACL_TYPE_PUBLIC_READ_WRITE:
                $res['visibility'] = OssClient::OSS_ACL_TYPE_PUBLIC_READ_WRITE;
                break;
        }

        return $res;
    }

    public function setVisibility($path, $visibility) {
        $acl = ($visibility === AdapterInterface::VISIBILITY_PUBLIC) ? OssClient::OSS_ACL_TYPE_PUBLIC_READ : OssClient::OSS_ACL_TYPE_PRIVATE;
        $res = $this->client->putObjectAcl($this->bucket, $path, $acl);
    }

    public function listDirObjects($dirname = '', $recursive = false) {
        $delimiter = '/';
        $nextMarker = '';
        $maxKeys = 1000;

        $options = [
            'dirname' => $delimiter,
            'prefix' => $dirname,
            'max-keys' => $maxKeys,
            'marker' => $nextMarker,
        ];

        try {
            $listObjectInfo = $this->client->listObjects($this->bucket, $options);
        } catch (OssException $e) {
            throw new OssException($e->getMessage());
        }

        $objectList = $listObjectInfo->getObjectList();
        $prefixList = $listObjectInfo->getPrefixList();

        $dir = [
            'objects' => [],
            'prefix' => [],
        ];
        if (!empty($objectList)) {
            foreach ($objectList as $objectInfo) {
                $object['Prefix'] = $dirname;
                $object['Key'] = $objectInfo->getKey();
                $object['LastModified'] = $objectInfo->getLastModified();
                $object['eTag'] = $objectInfo->getETag();
                $object['Size'] = $objectInfo->getSize();
                $object['StorageClass'] = $objectInfo->getStorageClass();
                $object['type'] = $objectInfo->getType();
                $dir['objects'][] = $object;
            }
        }

        if (!empty($prefixList)) {
            foreach ($prefixList as $prefixInfo) {
                $dir['prefix'][] = $prefixInfo->getPrefix();
            }
        }

        if ($recursive) {
            foreach ($dir['prefix'] as $prefix) {
                $next = $this->listDirObjects($prefix, $recursive);
                $dir['objects'] = array_merge($dir['objects'], $next['objects']);
            }
        }

        return $dir;
    }

    public function listContents($dirname) {
        $dir = $this->listDirObjects($dirname, true);

        $contents = $dir['objects'];
        $result = array_map([
            $this,
            'normalizeResponse',
        ], $contents);

        $result = array_filter($result, function ($value) {
            return $value['path'] !== 'false';
        });

        return Util::emulateDirectories($result);
    }

    public function getPath($file) {
        $host = str_replace([
            'https:',
            'http:',
        ], '', rtrim($this->cdnDomain, '/'));

        $path = str_replace([
            'https:',
            'http:',
        ], '', rtrim($file, '/'));

        return ltrim($path, $host);
    }

    public function getUrl($path) {
        $host = rtrim($this->cdnDomain, '/');
        $path = ltrim($path, '/');

        return $host . '/' . $path;
    }

    public function signatureUrl($file, $expire = 3600, $options = []) {
        $path = $this->getPath($file);

        $options = array_merge($this->options, $options);

        $url = $this->client->signUrl($this->bucket, $path, $expire, 'GET', $options);

        // 处理URL, 如果是内网地址, 则替换为外网地址, 如果有cdn域名, 则替换为cdn域名
        if ($this->internalEndpoint) {
            $url = str_replace($this->internalEndpoint, $this->endpoint, $url);
        }

        if ($this->cdnDomain) {
            $url = str_replace($this->endpoint, $this->cdnDomain, $url);
        }

        return $url;
    }
}
