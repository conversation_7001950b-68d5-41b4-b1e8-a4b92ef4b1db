<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\ChatApiKey
 *
 * @property int $id
 * @property string $key API Key
 * @property int $user_id 用户ID
 * @property string $guard_name
 * @property int|null $tokens
 * @property int|null $pics
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey wherePics($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatApiKey whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperChatApiKey
 */
class ChatApiKey extends Model
{
    use HasFactory;
}
