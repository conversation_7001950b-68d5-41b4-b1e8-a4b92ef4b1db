<?php
namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Services\Video\AiVideoManager;
use Kanchangzhou\AI\Models\AiVideoHistory;

class VideoController extends BaseController
{
    public function task(Request $request) {

        $this->validate($request, [

            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'pixverse',
                ]),
            ],

            'prompt' => ['required', 'string'], //画面描述词
            'ratio' => ['string', 'nullable'],   //比例;默认16:9 16:9/9:16/1:1/4:3/3:4
            'style' => ['string', 'nullable'],   //console-docs.apipost.cn/preview/61de93dee58d5cd2/d4e4b152eea413a5传参考图则不需要此参数
            'image_base' => ['string', 'nullable'],   //选填,参考图片链接,可供互联网访问并可下载的链接;不要和asset_id同时传,如果传了asset_id将忽略此参考图
            'motion_strength' => ['string', 'nullable'],  //选填,参考图模式才需要;运动强度;默认0.55;值介于0.01-1.00之间
            'seed' => ['string', 'nullable'], //选填,种子,不传则随机
            'motion_scale' => ['array', 'nullable'], //目前，使用单个摄影机运动会产生更好的结果。请注意，使用相机运动会限制运动强度。如果不想使用摄影机运动功能，请确保所有“摄影机运动”值都设置为0
        ]);

        $response = (new AiVideoManager())->client($request->input('module', 'baidu'))
            ->setRequestConfig($request->all())
            ->task();

        return Respond::respondWithData([
            $response
        ]);
    }

    public function result(Request $request) {
        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'pixverse',
                ]),
            ],
            'task_id' => ['required', 'string'],
        ]);

        $response = (new AiVideoManager())->client($request->input('module', 'baidu'))
            ->result($request->input('task_id'));

        return Respond::respondWithData([
            $response
        ]);
    }

    public function rsync(Request $request) {
        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'pixverse',
                ]),
            ],
            'task_id' => ['required', 'string'],
        ]);

        $response = (new AiVideoManager())->client($request->input('module', 'baidu'))
            ->rsync($request->input('task_id'));

        return Respond::respondWithData([
            $response
        ]);
    }

    public function history(Request $request) {
        $this->validate($request, [
            //模型名称
            'module' => [
                'nullable',
                Rule::in([
                    'baidu',
                    'pixverse',
                ]),
            ],
        ]);

        $query = AIVideoHistory::where('user_id', AuthFacade::adminUser()->getId());
        if($request->input('module')){
            $query->where('module', Str::studly($request->input('module')));
        }
        $history = $query->orderByDesc('id')->paginate();

        return Respond::respondWithData(JsonResource::collection($history));
    }
}
