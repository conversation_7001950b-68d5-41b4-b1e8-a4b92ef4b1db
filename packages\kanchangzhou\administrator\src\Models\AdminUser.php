<?php

namespace Kanchangzhou\Administrator\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Kanchangzhou\Auth\Contracts\UserInterface;
use Kanchangzhou\Kernel\Contracts\Commenter;
use Kanchangzhou\ReviewCenter\Traits\ReviewerTrait;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;


/**
 * Kanchangzhou\Administrator\Models\AdminUser
 *
 * @property int $id
 * @property string $username
 * @property string $password
 * @property int|null $admin_department_id
 * @property string|null $nickname
 * @property string $mobile
 * @property string|null $true_name
 * @property string|null $avatar
 * @property int|null $status
 * @property string|null $uc_openid
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $last_logon_time
 * @property-read \Kanchangzhou\Administrator\Models\AdminDepartment|null $adminDepartment
 * @property-read Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Kanchangzhou\ReviewCenter\Models\ReviewMember|null $reviewMember
 * @property-read Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser permission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser role($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereAdminDepartmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereLastLogonTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereTrueName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereUcOpenid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser whereUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminUser withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdminUser
 */
class AdminUser extends Authenticatable implements JWTSubject, UserInterface, Commenter
{
    use SoftDeletes, HasRoles;
    use ReviewerTrait;

    protected $guarded = [];

    protected $guard_name = 'kadmin';

//    protected $fillable = [
//        'id',
//        'username',
//        'password',
//        'nickname',
//        'mobile',
//        'true_name',
//        'avatar',
//        'status',
//        'updated_at',
//    ];

    protected $hidden = [
        'password',
    ];

    const STATUS_INVALID = 1;
    const STATUS_VALID = 2;

    public function getJWTIdentifier() {
        return $this->getKey();
    }

    public function getJWTCustomClaims() {
        return [
            'role' => 'admin',
            'is_admin' => true,
            'nickname' => $this->nickname,
            'true_name' => $this->true_name,
            'status' => $this->getStatus(),
            'avatar' => $this->avatar,
        ];
    }

    public function targetModels($targetModel) {
        $related = $targetModel;
        if ($targetModel instanceof Model) {
            $related = get_class($targetModel);
        } elseif ($targetModel instanceof Collection) {
            $related = $targetModel->first()
                                   ->getMorphClass();
        }

        return $this->morphedByMany($related, 'target', 'model_to_target');
    }

    public function targetModelsOnly($targetModel) {
        $related = $targetModel;
        if ($targetModel instanceof Model) {
            $related = get_class($targetModel);
        } elseif ($targetModel instanceof Collection) {
            $related = $targetModel->first()
                                   ->getMorphClass();
        }

        return $this->morphedByMany($related, 'target', 'model_to_target_only');
    }

    public function adminDepartment() {
        return $this->belongsTo(AdminDepartment::class);
    }


    public function getId() {
        return $this->getAttribute('id');
    }

    public function getNickname(): ?string {
        return $this->getAttribute('nickname');
    }

    public function getName(): ?string {
        return $this->getAttribute('true_name');
    }

    public function getEmail(): ?string {
        return $this->getAttribute('email');
    }

    public function getAvatar(): ?string {
        return $this->getAttribute('avatar');
    }

    public function getStatus(): ?bool {
        return $this->getAttribute('status') == self::STATUS_VALID;
    }

    public function getMobile(): ?string {
        return $this->getAttribute('mobile');
    }

    // Commenter
    public function getCommenterNickname() {
        return $this->getAttribute('nickname');
    }

    public function getCommenterAvatar() {
        return $this->getAttribute('avatar');
    }

    public function getGuardName(){
        return $this->guard_name;
    }

}
