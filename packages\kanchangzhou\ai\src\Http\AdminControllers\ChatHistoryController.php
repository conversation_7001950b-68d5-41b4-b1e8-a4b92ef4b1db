<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\AI\Http\Resources\ChatHistoryResource;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ChatHistoryController extends BaseController
{
    public function myCreated(Request $request) {
        PermissionHook::can('智能对话.我的创作');

        $history = ChatHistory::with([
            'chatRole',
        ])
                              ->when($request->input('role_id'), function ($query, $roleId) {
                                  $query->where('chat_role_id', $roleId);
                              })
                              ->whereHas('chatRole', function ($query) {
                                  $query->where('type', ChatRole::TYPE_STEP)
                                        ->where('show_history', ChatRole::SHOW_HISTORY_YES);
                              })
                              ->where('type', ChatHistory::TYPE_CHAT)
                              ->where('user_id', AuthFacade::adminUser()
                                                           ->getId())
                              ->where('guard_name', 'kadmin')
                              ->where('created_at', '>=', Carbon::now()
                                                                ->subMonths(3))
                              ->where('is_retry', ChatHistory::IS_RETRY_NO)
                              ->where('role', ChatHistory::ROLE_ASSISTANT)
                              ->groupBy('chat_uuid')
                              ->orderByDesc('id')
                              ->paginate();

        return Respond::respondWithData(ChatHistoryResource::collection($history));
    }

    public function chat(Request $request) {
        PermissionHook::can('智能对话.对话历史');

        $history = ChatHistory::where('user_id', AuthFacade::adminUser()
                                                           ->getId())
                              ->where('guard_name', 'kadmin')
                              ->where('created_at', '>=', Carbon::now()
                                                                ->subMonths(3))
                              ->where('is_retry', ChatHistory::IS_RETRY_NO)
                              ->when($request->input('role_id'), function ($query, $roleId) {
                                  $query->where('chat_role_id', $roleId);
                              }, function ($query) {
                                  $query->whereNull('chat_role_id');
                              })
                              ->where('type', ChatHistory::TYPE_CHAT)
                              ->groupBy('chat_uuid')
                              ->orderByDesc('id')
                              ->take(15)
                              ->paginate();

        return Respond::respondWithData(ChatHistoryResource::collection($history));
    }

    public function show(Request $request, $chatUuid) {
        PermissionHook::can('智能对话.对话历史');

        $history = ChatHistory::with([
            'chatRole',
            'chatModel',
        ])
                              ->where('chat_uuid', $chatUuid)
                              ->when($request->input('last_id'), function ($query, $lastId) {
                                  $query->where('id', '<', $lastId);
                              })
                              ->where('user_id', AuthFacade::adminUser()
                                                           ->getId())
                              ->where('guard_name', 'kadmin')
                              ->where('is_retry', ChatHistory::IS_RETRY_NO)
                              ->orderByDesc('id')
                              ->take(16)
                              ->get()
                              ->sortBy('id');

        return Respond::respondWithData(ChatHistoryResource::collection($history));
    }

    public function destroy($chatUuid) {
        PermissionHook::can('智能对话.对话历史');

        ChatHistory::where('chat_uuid', $chatUuid)
                   ->where('user_id', AuthFacade::adminUser()
                                                ->getId())
                   ->where('guard_name', 'kadmin')
                   ->delete();

        return Respond::respondWithData();
    }

    public function likeThis(Request $request) {
        PermissionHook::can('智能对话.对话历史');

        $this->validate($request, [
            'chat_uuid' => 'required',
            'message_id' => 'required',
        ], []);

        ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                   ->where('id', $request->input('message_id'))
                   ->where('user_id', AuthFacade::adminUser()
                                                ->getId())
                   ->where('guard_name', 'kadmin')
                   ->update([
                       'is_like' => ChatHistory::IS_LIKE_YES,
                   ]);

        return Respond::respondWithData();
    }

    public function dontLikeThis(Request $request) {
        PermissionHook::can('智能对话.对话历史');

        $this->validate($request, [
            'chat_uuid' => 'required',
            'message_id' => 'required',
        ], []);

        ChatHistory::where('chat_uuid', $request->input('chat_uuid'))
                   ->where('id', $request->input('message_id'))
                   ->where('user_id', AuthFacade::adminUser()
                                                ->getId())
                   ->where('guard_name', 'kadmin')
                   ->update([
                       'is_like' => ChatHistory::IS_LIKE_NO,
                   ]);

        return Respond::respondWithData();
    }

}
