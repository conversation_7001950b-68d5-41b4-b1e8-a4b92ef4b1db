<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Azure\Config;

use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsConfigInterface;

class StreamConfig implements AiTtsConfigInterface
{
    private $format;

    public function __construct($config = []) {
        $config += [
            'format' => 'mp3',
        ];

        $this->format = $config['format'];
    }

    public function toArray() {
        // TODO: Implement toArray() method.
        return [
            'format' => $this->format,
        ];
    }

    public function toJson() {
        // TODO: Implement toJson() method.
        return json_encode($this->toArray());
    }
}
