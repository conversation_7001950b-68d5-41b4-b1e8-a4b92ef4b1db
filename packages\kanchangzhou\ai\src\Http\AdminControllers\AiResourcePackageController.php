<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\AI\Models\AiModelPrice;
use Kanchangzhou\AI\Models\AiOrder;
use Kanchangzhou\AI\Models\AiOrderItem;
use Kanchangzhou\AI\Models\AiResourcePackage;
use Kanchangzhou\AI\Models\AiUserPackage;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\AI\Exceptions\AiOrderException;

class AiResourcePackageController extends BaseController
{
    public function index(Request $request) {
        $packages = AiResourcePackage::where('status', AiResourcePackage::STATUS_VALID)
                                     ->when($request->input('type'), function ($query, $type) {
                                         $query->where('type', $type);
                                     })
                                     ->when($request->input('provider'), function ($query, $provider) {
                                         $query->where('provider', $provider);
                                     })
                                     ->when($request->input('model_id'), function ($query, $model_id) {
                                         $query->where('model_id', $model_id);
                                     })
                                     ->when($request->input('is_general'), function ($query, $is_general) {
                                         $query->where('is_general', $is_general);
                                     })
                                     ->orderBy('id', 'desc')
                                     ->paginate(10);

        return Respond::respondWithData(JsonResource::collection($packages));
    }

    public function show($packageId) {
        $package = AiResourcePackage::find($packageId);

        return Respond::respondWithData($package);
    }

    public function formOptions() {
        $providers = ChatModel::PROVIDER_MAP;
        $modelTypes = ChatModel::MODEL_TYPE_MAP;
        $status = AiResourcePackage::STATUS_MAP;
        $isGeneral = AiResourcePackage::IS_GENERAL_MAP;

        return Respond::respondWithData(compact('providers', 'modelTypes', 'status', 'isGeneral'));
    }

    public function store(Request $request) {
        $this->validate($request, [
            'name' => 'required',
            'description' => '',
            'type' => 'required',
            'chat_model_id' => 'required_if:is_general,' . AiResourcePackage::IS_GENERAL_NO,
            'is_general' => 'required',
            'total_tokens' => 'required',
            'expired_days' => 'required',
            'status' => 'required',
        ], []);

        $chatModel = ChatModel::where('id', $request->input('chat_model_id'))
                              ->first();

        $package = AiResourcePackage::create([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'type' => $request->input('type'),
            'provider' => $chatModel?->provider,
            'model_id' => $chatModel?->model_id,
            'model_type' => $chatModel?->model_type,
            'is_general' => $request->input('is_general'),
            'total_tokens' => $request->input('total_tokens'),
            'expired_days' => $request->input('expired_days'),
            'status' => $request->input('status'),
        ]);

        return Respond::respondWithData($package);
    }

    public function update(Request $request, $packageId) {
        $package = AiResourcePackage::find($packageId);

        $this->validate($request, [
            'name' => 'required',
            'description' => '',
            'total_tokens' => 'required',
            'expired_days' => 'required',
            'status' => 'required',
        ], []);

        $package->update([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'total_tokens' => $request->input('total_tokens'),
            'expired_days' => $request->input('expired_days'),
            'status' => $request->input('status'),
        ]);

        return Respond::respondWithData($package);
    }

    public function destroy($packageId) {
        $package = AiResourcePackage::find($packageId);

        $package->delete();

        return Respond::respondWithData();
    }

    public function getFreeResourcePackage() {
        $package = AiResourcePackage::find(1);

        // 检查用户是否已经购买过免费套餐,并且套餐未过期
        $userPackage = AiUserPackage::where('user_id', AuthFacade::adminUser()
                                                                 ->getId())
                                    ->where('guard_name', 'kadmin')
                                    ->where('resource_package_id', $package->id)
                                    ->where('expired_at', '>', Carbon::now())
                                    ->where('used_tokens', '<', $package->total_tokens)
                                    ->first();

        if ($userPackage) {
            throw new AiOrderException(AiOrderException::FREE_REPEAT_BUY);
        }

        // 创建订单
        $order = AiOrder::create([
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'guard_name' => 'kadmin',
            'order_no' => 'AI' . date('YmdHis') . rand(1000, 9999),
            'total' => 0,
            'status' => AiOrder::STATUS_PAID,
            'order_type' => AiOrder::ORDER_TYPE_CHAT_PACKAGE,
            'payment_no' => 'P' . date('YmdHis') . rand(1000, 9999),
            'payment_method' => AiOrder::PAYMENT_METHOD_SYSTEM,
            'paid_at' => Carbon::now(),
        ]);

        $order->items()
              ->create([
                  'model_id' => 0,
                  'model_uuid' => '',
                  'provider' => '',
                  'input_tokens' => 0,
                  'output_tokens' => 0,
                  'total_tokens' => $package->total_tokens,
                  'price' => 0,
                  'unit_count' => 0,
                  'total' => 0,
                  'order_type' => $package->type,
                  'status' => AiOrder::STATUS_PAID,
                  'user_package_id' => 0,
                  'chat_history_id' => 0,
              ]);


        // 创建用户套餐
        AiUserPackage::create([
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'guard_name' => 'kadmin',
            'total_tokens' => $package->total_tokens,
            'used_tokens' => 0,
            'package_type' => $package->type,
            'provider' => $package->provider,
            'model_type' => $package->model_type,
            'model_id' => $package->model_id,
            'is_general' => $package->is_general,
            'order_id' => $order->id,
            'expired_at' => Carbon::now()
                                  ->addDays($package->expired_days)
                                  ->endOfDay(),
            'resource_package_id' => $package->id,
        ]);

        return Respond::respondWithData();
    }

    public function myPackages() {
        $packages = AiUserPackage::where('user_id', AuthFacade::adminUser()
                                                              ->getId())->where('guard_name', 'kadmin')
                                 ->paginate(10);

        return Respond::respondWithData(JsonResource::collection($packages));
    }
}
