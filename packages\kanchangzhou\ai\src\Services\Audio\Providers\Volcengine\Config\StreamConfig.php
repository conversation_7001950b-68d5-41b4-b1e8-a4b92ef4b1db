<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Config;

use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsConfigInterface;

class StreamConfig implements AiTtsConfigInterface
{
    private $speechRate;

    private $pitchRate;

    private $volume;

    private $voice;

    private $format;

    public function __construct($config = []) {
        $config += [
            'voice' => 'xiaoyun',
            'format' => 'mp3',
            'speech_rate' => 0,
            'pitch_rate' => 0,
            'volume' => 50,
        ];

        $this->voice= $config['voice'];
        $this->format = $config['format'];
        $this->speechRate = $config['speech_rate'];
        $this->pitchRate = $config['pitch_rate'];
        $this->volume = $config['volume'];
    }

    public function toArray() {
        // TODO: Implement toArray() method.
        return [
            'voice' => $this->voice,
            'format' => $this->format,
            'speech_rate' => $this->speechRate,
            'pitch_rate' => $this->pitchRate,
            'volume' => $this->volume,
        ];
    }

    public function toJson() {
        // TODO: Implement toJson() method.
        return json_encode($this->toArray());
    }
}
