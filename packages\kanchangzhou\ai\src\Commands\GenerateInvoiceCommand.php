<?php

namespace Kanchangzhou\AI\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Kanchangzhou\AI\Models\AiModelPrice;
use Kanchangzhou\AI\Models\AiOrder;
use Kanchangzhou\AI\Models\AiOrderItem;
use Kanchangzhou\AI\Models\ChatHistory;

class GenerateInvoiceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:invoice {--all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $isAll = $this->option('all');

        ChatHistory::withTrashed()
                   ->with('chatModel')
                   ->selectRaw('SUM(input_tokens) as input_tokens, SUM(output_tokens) as output_tokens, DATE_FORMAT(created_at, "%Y-%m-%d %H:%s") as t')
                   ->addSelect([
                       'user_id',
                       'guard_name',
                       'chat_model_uuid',
                       'chat_model_id',
                   ])
                   ->when($isAll, function ($query) {
                       $query->where('created_at', '>=', '2024-03-28 00:00:00');
                   }, function ($query) {
                       $query->whereBetween('created_at', [
                           Carbon::now()
                                 ->subMinute()
                                 ->startOfMinute(),
                           Carbon::now()
                                 ->subMinute()
                                 ->endOfMinute(),
                       ]);
                   })
                   ->where('type', ChatHistory::TYPE_CHAT)
                   ->groupBy('user_id', 'guard_name', 'chat_model_uuid', 't')
                   ->chunk(100, function ($histories) {
                       foreach ($histories as $history) {
                           // 检查是否已经进入结算
                           if (AiOrderItem::where('unique_hash', hash('md5', $history->user_id . $history->guard_name . $history->chat_model_uuid . $history->t))
                                          ->exists()) {
                               continue;
                           }

                           // 生成AiOrder和AiOrderItem,并且从用户套餐中扣除, 单个订单中如果套餐只够扣除部分,则默认扣除全部并且该订单状态为已支付
                           $modelPrice = AiModelPrice::where('provider', $history->chatModel->provider)
                                                     ->where('model_type', $history->chatModel->model_type)
                                                     ->where('model_id', $history->chatModel->model_id)
                                                     ->first();

                           $order = AiOrder::create([
                               'user_id' => $history->user_id,
                               'guard_name' => $history->guard_name,
                               'order_no' => 'AI' . date('YmdHis') . rand(1000, 9999),
                               'total' => $modelPrice->price * number_format(($history->input_tokens + $history->output_tokens) / $modelPrice->unit_count, 3),
                               'status' => AiOrder::STATUS_PENDING,
                               'order_type' => AiOrder::ORDER_TYPE_CHAT,
                           ]);

                           $order->items()
                                 ->create([
                                     'model_id' => $history->chatModel->model_id,
                                     'model_uuid' => $history->chatModel->model_uuid,
                                     'provider' => $history->chatModel->provider,
                                     'input_tokens' => $history->input_tokens,
                                     'output_tokens' => $history->output_tokens,
                                     'total_tokens' => $history->input_tokens + $history->output_tokens,
                                     'price' => $modelPrice->price,
                                     'unit_count' => $modelPrice->unit_count,
                                     'total' => $modelPrice->price * number_format(($history->input_tokens + $history->output_tokens) / $modelPrice->unit_count, 3),
                                     'order_type' => AiOrder::ORDER_TYPE_CHAT,
                                     'status' => AiOrder::STATUS_PENDING,
                                     'user_package_id' => 0,
                                     'chat_history_id' => 0,
                                     'unique_hash' => hash('md5', $history->user_id . $history->chat_model_uuid . $history->t),
                                 ]);
                       }
                   });
    }
}
