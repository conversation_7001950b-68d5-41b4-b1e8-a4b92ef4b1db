<?php

namespace Kanchangzhou\AI\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\SkillCompetition;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\Auth\Facades\AuthFacade;

class SkillCompetitionAiScoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $project;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(SkillCompetition $project) {
        $this->project = $project;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        if ($this->project->ai_score_status == SkillCompetition::AI_SCORE_STATUS_FINISHED) {
            return;
        }
        // 根据分镜头的内容, 判断图像音频视频个数, 进行评分
        $image = 0;
        $audio = 0;
        $video = 0;
        $music = 0;

        $prompt = "主题:{$this->project->title}\n";

        if (!empty($this->project->background_music)) {
            $music++;
        }

        $images = [];
        $audios = [];
        $videos = [];

        foreach ($this->project->content as $i => $item) {
            if (!empty($item['hua_mian']) && !in_array($item['hua_mian'], $images)) {
                $image++;
            }

            if (!empty($item['yin_pin']) && !in_array($item['yin_pin'], $audios)) {
                $audio++;
            }

            if (!empty($item['shi_pin']) && !in_array($item['shi_pin'], $videos)) {
                $video++;
            }

            $prompt .= "分镜头{$i}, 景别:{$item['jing_bie']}, 场景预期:{$item['chang_jing']}, 内容:{$item['nei_rong']}, 文案:{$item['wen_an']}, 备注:{$item['bei_zhu']}\n";
        }

        $this->project->ai_media_score = [
            'image' => min($image, 10),
            'audio' => min($audio * 2, 10),
            'music' => min($music * 10, 10),
            'video' => min($video * 2, 5),
        ];

        $role = config('kai.other.skill_competition.score_role');
        $model = config('kai.other.skill_competition.score_model');

        $chatModel = ChatModel::where('model_uuid', $model)
                              ->first();

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatModel->provider];

        $aiUser = new AiUser($this->project->user_id, 'kadmin');

        $service = new $serviceProvider([
            'prompt' => $prompt,
            'role_id' => $role,
        ], $chatModel, $aiUser);

        $res = $service->handle();

        $message = $res->message;

        // 正则匹配出message中的分数, 格式为 {{45}}
        preg_match_all('/\{\{(\d+)\}\}/', $message, $matches);
        $this->project->ai_score = $matches[1][0];
        $this->project->ai_score_desc = $message;
        $this->project->ai_score_status = SkillCompetition::AI_SCORE_STATUS_FINISHED;
        $this->project->save();
    }
}
