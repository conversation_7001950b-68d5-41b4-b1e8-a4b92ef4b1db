<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMediaAttrInfoToAdvertisingPositionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('advertising_position', function (Blueprint $table) {
            $table->integer("image_num")->comment("图片数量");
            $table->integer("image_width")->comment("图片宽度");
            $table->integer("image_height")->comment("图片高度");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('advertising_position', function (Blueprint $table) {
            //
        });
    }
}
