<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

#use Kanchangzhou\Livechannel\Commands\AddRecordLog;
#use Kanchangzhou\Livechannel\Commands\RunRecordLog;


class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
#	AddRecordLog::class,
#	RunRecordLog::class,

    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     *
     * @return void
     */
    protected function schedule(Schedule $schedule) {
        // $schedule->command('inspire')->hourly();
//        $schedule->command('livechannel:add-record-log')->everyMinute();
//        $schedule->command('livechannel:run-record-log')->everyMinute();
//        $schedule->command('homepage:recommend_expired')->everyMinute();
//        $schedule->command('livechannel:run-video-upload')->everyMinute();
//        $schedule->command('live:live-subscribe-push')->everyMinute();
//        $schedule->command('counter:article')->everyFiveMinutes();
//        $schedule->command('m2o:sync_video')->everyFiveMinutes();
//        $schedule->command('statistic:archive')->dailyAt('01:00');
//        $schedule->command('kcz:siteData')->dailyAt('01:00');

        $schedule->command('thirdSource:cz 48')->everyFiveMinutes();
        $schedule->command('thirdSource:cz 54')->everyFiveMinutes();
        $schedule->command('thirdSource:cz 55')->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands() {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
