<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->string('model_type')
                  ->nullable()
                  ->after('model_id')
                  ->default(\Kanchangzhou\AI\Models\ChatModel::MODEL_TYPE_CHAT);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->dropColumn('model_type');
        });
    }
};
