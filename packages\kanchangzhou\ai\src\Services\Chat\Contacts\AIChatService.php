<?php

namespace Kanchangzhou\AI\Services\Chat\Contacts;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\Auth\Facades\AuthFacade;

// 写phpdoc注释, 用于IDE提示

/**
 * Class AIChatService
 * @package Kanchangzhou\AI\Services\Chat\Contacts
 * @property  string $chatUuid 会话ID
 * @property  string $inputPrompt 用户输入
 * @property  string $moduleName 模块名称
 * @property  string $moduleKey 模块key
 * @property  string $modelKey 模型key
 * @property  bool $isStream 是否流式对话
 * @property  int $dialogRound 历史记录条数
 * @property  string $roleUserKey 用户角色key
 * @property  string $roleAssistantKey 机器人角色key
 * @property  string $roleSystemKey 系统角色key
 */
abstract class AIChatService
{
    protected $chatUuid;
    protected $prompt;
    protected $isRetry = false;
    protected $moduleName;
    protected $moduleKey;
    protected $serviceProvider;
    protected $modelId;
    protected $roleId;
    protected $role;
    protected $roleStep = 'step_1';
    protected $roleStepInput = [];
    protected $isStream = false;
    protected $dialogRound = 5;
    protected $roleUserKey = 'user';
    protected $roleAssistantKey = 'assistant';
    protected $roleSystemKey = 'system';
    protected $hasSystemRole = false;
    protected $inputTokens = 0;

    protected $fileids;

    protected $chatModel;

    protected $user;

    public function __construct($chatParams, ?ChatModel $chatModel, AiUser $user = null) {
        $this->prompt = $chatParams['prompt'] ?? '';
        $this->chatUuid = $chatParams['chat_uuid'] ?? Str::uuid()
                                                         ->toString();
        $this->isRetry = $chatParams['is_retry'] ?? false;
        $this->roleId = $chatParams['role_id'] ?? null;
        $this->roleStep = $chatParams['role_step'] ?? 'step_1';
        $this->roleStepInput = $chatParams['role_step_input'] ?? [];
        $this->chatModel = $chatModel;
        $this->fileids = $chatParams['fileids'] ?? [];
        $this->user = $user;
    }


    abstract public function handler(): AIChatResponse;

    /**
     * 流式处理器, 用于处理流式对话, 暂时返回空AIChatResponse
     * @return AIChatResponse
     */
    abstract public function streamHandler();

    abstract public function uploadFile(Request $request);

    public function handle() {
        $res = $this->handler();

        $reply = $this->saveHistory($res);

        $res->offsetSet('message_id', $reply->id);

        return $res;
    }

    public function streamHandle() {
        $res = $this->streamHandler();
        //        return;
        if (!($res['is_error'] ?? false)) {
            $reply = $this->saveHistory($res);
            $res->offsetSet('message_id', $reply->id);
        }

        //        $res->offsetSet('message_id', $reply->id);
        $res->offsetUnset('message');

        $res->offsetSet('module_key', $this->chatModel?->model_uuid);
        $res->offsetSet('module_name', $this->chatModel?->title);

        echo "id:" . (microtime(true) * 10000) . PHP_EOL;
        echo "event:stop" . PHP_EOL;
        echo "data: " . $res->toJson() . PHP_EOL . PHP_EOL;
        flush();
    }

    protected function saveHistory(&$res) {
        // 存历史记录
        $this->chatUuid = ChatHistory::where('chat_uuid', $this->chatUuid)
                                     ->where('user_id', $this->user->getUserId())
                                     ->where('guard_name', $this->user->getGuardName())
                                     ->where('is_retry', ChatHistory::IS_RETRY_NO)
                                     ->exists() ? $this->chatUuid : Str::uuid()
                                                                       ->toString();

        $res->offsetSet('chat_uuid', $this->chatUuid);

        $role = $this->getRole();

        if (!$this->isRetry) {
            ChatHistory::create([
                'chat_uuid'       => $this->chatUuid,
                'chat_model_id'   => $this->chatModel?->model_id,
                'chat_model_uuid' => $this->chatModel?->model_uuid,
                'message'         => $this->prompt,
                'fileids'         => $this->fileids,
                'role'            => $this->roleUserKey,
                'provider'        => $this->serviceProvider,
                'module_name'     => $this->chatModel?->title,
                'module_key'      => $this->chatModel?->provider,
                'user_id'         => $this->user->getUserId(),
                'guard_name'      => $this->user->getGuardName(),
                'chat_role_id'    => $role?->role_uuid,
                'is_retry'        => ChatHistory::IS_RETRY_NO,
                'input_tokens'    => $res->input_tokens ?? $this->inputTokens,
                'role_step'       => $role ? $this->roleStep : '',
                'step_input'      => $role ? $this->roleStepInput : [],
            ]);

            ChatLimit::setProvider($this->serviceProvider)
                     ->setModule($this->chatModel?->provider, $this->chatModel?->title)
                     ->setUserId($this->user->getUserId())
                     ->chatIncrement($res->input_tokens ?? $this->inputTokens);
        }

        if ($this->isRetry) {
            ChatHistory::where('chat_uuid', $this->chatUuid)
                       ->where('role', $this->roleAssistantKey)
                       ->where('is_retry', ChatHistory::IS_RETRY_NO)
                       ->orderByDesc('id')
                       ->first()
                       ->update([
                           'is_retry' => ChatHistory::IS_RETRY_YES,
                       ]);
        }

        ChatLimit::setProvider($this->serviceProvider)
                 ->setModule($this->chatModel?->provider, $this->chatModel?->title)
                 ->setUserId($this->user->getUserId())
                 ->chatIncrement($res->output_tokens ?? Str::length($res->message));

        return ChatHistory::create([
            'chat_uuid'           => $this->chatUuid,
            'chat_model_id'       => $this->chatModel?->model_id,
            'chat_model_uuid'     => $this->chatModel?->model_uuid,
            'reasoning_content'   => $res->reasoning_content,
            'message'             => $res->message,
            'role'                => $this->roleAssistantKey,
            'provider'            => ChatModel::PROVIDER_MAP[$this->chatModel?->provider],
            'module_name'         => $this->chatModel?->title,
            'module_key'          => $this->chatModel?->provider,
            'user_id'             => $this->user->getUserId(),
            'guard_name'          => $this->user->getGuardName(),
            'is_retry'            => ChatHistory::IS_RETRY_NO,
            'chat_role_id'        => $role?->role_uuid,
            'output_tokens'       => $res->output_tokens ?? Str::length($res->message),
            'provider_request_id' => $res->source_id,
        ]);
    }

    public function getRoleSystemPrompt() {
        return ($this->getRole()?->system_prompt ?? '你是由中吴网技术部研发智能大模型叫"小吴同学"');
    }

    public function getRoleStepPrompt(ChatHistory $historyMessage = null) {
        $role = $historyMessage ? $historyMessage->chatRole : $this->getRole();
        $inputPrompt = $historyMessage ? $historyMessage->message : $this->prompt;
        $roleStep = $historyMessage ? $historyMessage->role_step : $this->roleStep;

        if (!$role || $role->type == ChatRole::TYPE_NORMAL) {
            return $inputPrompt;
        }

        if ($role->type == ChatRole::TYPE_STEP) {
            $stepPrompt = $role->step_prompt[$roleStep] ?? '';
            $fields = $role->options[$roleStep]['fields'] ?? [];
            $stepInput = $historyMessage ? $historyMessage->step_input : $this->roleStepInput;

            if (!$stepInput) {
                return $inputPrompt;
            }

            foreach ($fields as $field) {
                $fieldInput = $stepInput[$field['field']] ?? '';

                $fieldValue = match ($field['type']) {
                    'checkbox', 'cascader', 'inputTag' => implode(',', $fieldInput),
                    'select' => ($field['props']['multiple'] ?? false) ? implode(',', $fieldInput) : $fieldInput,
                    'datepicker', 'timePicker' => Str::contains(($field['props']['type'] ?? ''), 'range') ? implode(' ~ ', $fieldInput) : $fieldInput,
                    default => $fieldInput ?? '',
                };
                if ($role->step_prompt[$roleStep] ?? '') {
                    $stepPrompt = str_replace('{' . $field['title'] . '}', $fieldValue, $stepPrompt);
                } else {
                    $stepPrompt .= $field['title'] . ':' . $fieldValue . "\n";
                }
            }

            return $stepPrompt;
        }

        return $inputPrompt;
    }

    /**
     * 获取历史消息
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Query\Builder[]|\Illuminate\Support\Collection|ChatHistory[]
     */
    public function getHistoryMessages($count = 10) {
        return ChatHistory::with(['chatRole'])
                          ->where('chat_uuid', $this->chatUuid)
                          ->where('user_id', $this->user->getUserId())
                          ->where('guard_name', $this->user->getGuardName())
                          ->where('is_retry', ChatHistory::IS_RETRY_NO)
                          ->take($count * 2)
                          ->orderByDesc('id')
                          ->get();
    }

    /**
     * 获取历史消息, 生成prompts
     * @return array
     */
    public function messageWithHistory() {
        $messages = [];

        if ($this->hasSystemRole) {
            $messages[] = [
                'role'    => $this->roleSystemKey,
                'content' => $this->getRoleSystemPrompt(),
            ];

            $this->inputTokens += Str::length($this->getRoleSystemPrompt());
        }
        $role = $this->getRole();

        $this->dialogRound = $role?->context_count ?? $this->chatModel?->context_round ?? 5;

        if ($this->isRetry) {
            if ($role?->enable_context == ChatRole::ENABLE_CONTEXT_NO || $this->chatModel?->enable_context == ChatModel::ENABLE_CONTEXT_NO) {
                $this->dialogRound = 1;
            }

            $historyMessages = $this->getHistoryMessages($this->dialogRound)
                                    ->sortBy('id');

            // 根据历史记录ID顺序排序, 保证历史记录顺序, 生成prompts
            foreach ($historyMessages as $historyMessage) {
                if ($this->chatModel?->can_files == ChatModel::CAN_FILES_YES && $historyMessage->fileids) {
                    // 判断类是否有messageWithFiles方法
                    if (method_exists($this, 'messageWithFiles')) {
                        $messages[] = $this->messageWithFiles($historyMessage->fileids);
                    }
                }
                $messages[] = [
                    'role'    => $historyMessage->role,
                    'content' => $this->getRoleStepPrompt($historyMessage),
                ];

                $this->inputTokens += Str::length($this->getRoleStepPrompt($historyMessage));
            }

            $popItem = array_pop($messages);
            $this->inputTokens -= Str::length($popItem['content']);

        } else {

            if ($role?->enable_context == ChatRole::ENABLE_CONTEXT_NO || $this->chatModel?->enable_context == ChatModel::ENABLE_CONTEXT_NO) {
                $this->dialogRound = 1;
            }
            $historyMessages = $this->getHistoryMessages($this->dialogRound)
                                    ->sortBy('id');


            // 根据历史记录ID顺序排序, 保证历史记录顺序, 生成prompts
            foreach ($historyMessages as $historyMessage) {
                if ($this->chatModel?->can_files == ChatModel::CAN_FILES_YES && $historyMessage->fileids) {
                    // 判断类是否有messageWithFiles方法
                    if (method_exists($this, 'messageWithFiles')) {
                        $messages[] = $this->messageWithFiles($historyMessage->fileids);
                    }
                }
                $messages[] = [
                    'role'    => $historyMessage->role,
                    'content' => $this->getRoleStepPrompt($historyMessage),
                ];

                $this->inputTokens += Str::length($this->getRoleStepPrompt($historyMessage));
            }

            if ($this->chatModel?->can_files == ChatModel::CAN_FILES_YES && $this->fileids) {
                $this->fileids = is_string($this->fileids) ? explode(',', $this->fileids) : $this->fileids;

                // 判断类是否有messageWithFiles方法
                if (method_exists($this, 'messageWithFiles')) {
                    $messages[] = $this->messageWithFiles($this->fileids);
                }
            }

            $messages[] = [
                'role'    => $this->roleUserKey,
                'content' => $this->getRoleStepPrompt(),
            ];

            $this->inputTokens += Str::length($this->getRoleStepPrompt());
        }

        return $messages;
    }

    /**
     * 获取历史消息, 生成prompts
     * @return array
     */
    public function messageWithoutHistory() {
        $messages[] = [
            'role'    => $this->roleUserKey,
            'content' => $this->getRoleStepPrompt(),
        ];

        $this->inputTokens += Str::length($this->getRoleSystemPrompt());

        return $messages;
    }

    /**
     * 获取历史消息, 生成prompts
     * @return array
     */
    public function promptWithHistory() {
        $prompts = [];

        $historyMessages = $this->getHistoryMessages()
                                ->sortBy('id');

        $prompts['history'] = [];
        foreach ($historyMessages as $historyMessage) {
            $prompts['history'][] = $this->getRoleStepPrompt($historyMessage);

            $this->inputTokens += Str::length($this->getRoleSystemPrompt($historyMessage));
        }

        $prompts['prompt'] = $this->getRoleStepPrompt();

        $this->inputTokens += Str::length($this->getRoleSystemPrompt());

        return $prompts;
    }

    /**
     * 获取历史消息, 生成prompts
     * @return array
     */
    public function promptWithoutHistory() {
        $prompts = [];

        $prpmpts['history'] = [];
        $prompts['prompt'] = $this->getRoleStepPrompt();

        $this->inputTokens += Str::length($this->getRoleSystemPrompt());

        return $prompts;
    }

    protected function getRole() {
        if (!$this->role) {
            $this->role = ChatRole::where('role_uuid', $this->roleId)
                                  ->first();
        }

        return $this->role;
    }

    public function setChatUuid($chatUuid) {
        $this->chatUuid = $chatUuid;

        return $this;
    }

    public function setPrompt($prompt) {
        $this->prompt = $prompt;

        return $this;
    }

    public function setIsRetry(bool $isRetry) {
        $this->isRetry = $isRetry;

        return $this;
    }

    public function setRoleStep($roleStep) {
        $this->roleStep = $roleStep;

        return $this;
    }

    public function setRoleStepInput($roleStepInput) {
        $this->roleStepInput = $roleStepInput;

        return $this;
    }

    public function setRoleid($roleId) {
        $this->roleId = $roleId;

        return $this;
    }

    public function setChatModel($chatModel) {
        $this->chatModel = $chatModel;

        return $this;
    }

    public function getUser(): ?AiUser {
        return $this->user;
    }

    public function setUser(?AiUser $user) {
        $this->user = $user;

        return $this;
    }

    public function getChatUuid() {
        return $this->chatUuid;
    }

    public function getPrompt() {
        return $this->prompt;
    }

    public function getModuleName() {
        return $this->moduleName;
    }

    public function getModuleKey() {
        return $this->moduleKey;
    }

    public function getModelId() {
        return $this->modelId;
    }

    public function getDialogRound() {
        return $this->dialogRound;
    }

    public function getRoleUserKey() {
        return $this->roleUserKey;
    }

    public function getRoleAssistantKey() {
        return $this->roleAssistantKey;
    }

    public function getRoleSystemKey() {
        return $this->roleSystemKey;
    }
}
