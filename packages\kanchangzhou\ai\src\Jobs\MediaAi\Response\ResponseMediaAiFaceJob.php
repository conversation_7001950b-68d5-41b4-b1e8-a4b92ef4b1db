<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Models\MediaAiFace;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiFaceJob extends MediaAiBaseJob
{
// data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"3028e0c38b3c48539e6befd78f88cecd","subDataTypes":[{"type":"face","source":"索贝","version":null}],"face":[{"fileId":"47f99a48-ad3c-497c-b443-63d2d0439163","statusCode":0,"statusInfo":"success","contents":[{"name":"unnamed","isKnow":false,"tag":"未命名人物","emotion":{"kind":"Neutral","confidence":0.7159254550933838},"angle":-4.81103515625,"personId":"6c9da81c632c40dfad30e581a3d8ab6b","confidence":0.7168035507202148,"clusterId":"6c9da81c632c40dfad30e581a3d8ab6b","quality":0.5448896884918213,"cluster_id":"6c9da81c632c40dfad30e581a3d8ab6b","begin":30000000,"end":30000000,"keyframe":{"offset":30000000,"location":{"left":1024,"top":219,"width":173,"height":223}}},{"name":"汤博奇","isKnow":true,"tag":"","emotion":{"kind":"Neutral","confidence":0.9831019043922424},"angle":-3.619476318359375,"personId":"cc6cb125906c4526a4ff3ff3eb898dd9","confidence":0.9068098664283752,"clusterId":"cc6cb125906c4526a4ff3ff3eb898dd9","quality":0.6973496079444885,"cluster_id":"cc6cb125906c4526a4ff3ff3eb898dd9","begin":18130000000,"end":18170000000,"keyframe":{"offset":18130000000,"location":{"left":764,"top":176,"width":188,"height":254}}}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $taskType = $this->data['data']['subDataTypes'][0]['type'];
        $guid = $this->data['data']['guid'];
        $faces = $this->data['data'][$taskType][0]['contents'];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();
        if (!$task) {
            return;
        }

        MediaAiFace::where('media_ai_file_id', $task->media_ai_file_id)
                   ->delete();

        foreach (array_chunk($faces, 20) as $chunk) {
            $tmp = array_map(function ($face) use ($task) {
                return [
                    'media_ai_file_id' => $task->media_ai_file_id,
                    'name' => $face['name'],
                    'tag' => $face['tag'],
                    'is_known' => $face['isKnow'] ? MediaAiFace::IS_KNOWN_YES : MediaAiFace::IS_KNOWN_NO,
                    'person_id' => $face['personId'],
                    'cluster_id' => $face['cluster_id'] ?? ($face['clusterId'] ?? ''),
                    'kind' => $face['emotion']['kind'] ?? '',
                    'kind_confidence' => $face['emotion']['confidence'] ?? '',
                    'angle' => $face['angle'] ?? '',
                    'keyframe' => json_encode($face['keyframe']),
                    'offset_time' => $face['begin'],
                    'begin' => $face['begin'],
                    'end' => $face['end'],
                ];
            }, $chunk);

            MediaAiFace::insert($tmp);
        }
//
//        foreach ($faces as $face) {
////            if ($face['isKnow']) {
//                MediaAiFace::updateOrCreate([
//                    'media_ai_file_id' => $task->media_ai_file_id,
//                    'person_id' => $face['personId'],
//                    'offset_time' => $face['begin'],
//                ], [
//                    'media_ai_file_id' => $task->media_ai_file_id,
//                    'name' => $face['name'],
//                    'tag' => $face['tag'],
//                    'is_known' => MediaAiFace::IS_KNOWN_YES,
//                    'person_id' => $face['personId'],
//                    'cluster_id' => $face['cluster_id'] ?? ($face['clusterId'] ?? ''),
//                    'kind' => $face['emotion']['kind'] ?? '',
//                    'kind_confidence' => $face['emotion']['confidence'] ?? '',
//                    'angle' => $face['angle'] ?? '',
//                    'keyframe' => $face['keyframe'],
//                    'offset_time' => $face['begin'],
//                    'begin' => $face['begin'],
//                    'end' => $face['end'],
//                ]);
////            }
//        }

        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
