<?php
namespace Kanchangzhou\Advertisement;

use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use Kanchangzhou\Advertisement\Http\Middleware\FilterParam;
use Kanchangzhou\Kernel\Events\HomeItemDeleteEvent;
use Kanchangzhou\Advertisement\Listeners\AdPositionHomeItemDeleteListener;

class AdvertisementProvider extends ServiceProvider
{
    public function boot(){
        $this->loadRoutesFrom(__DIR__."/../routes/kadvertisement.php");
    }

    public function register()
    {
        $this->registerRouteMiddleware();
        $this->registerMigrates();
//        $this->registerEventListener();
        $this->registerPublishables();
        $this->registerConfig();
    }

    private function registerPublishables()
    {
        $arrPublishable = [
            'migrations' => [
                __DIR__."/../database/migrations" => database_path('migrations'),
            ],
            'config' => [
                __DIR__."/../config/kadvertisement.php" => config_path('kadvertisement.php'),
            ],
        ];

        foreach ($arrPublishable as $group => $paths) {
            $this->publishes($paths, $group);
        }

    }

    private function registerConfig(){
        $this->mergeConfigFrom(__DIR__ . '/../config/kadvertisement.php', 'kadvertisement');
    }

    private function registerMigrates() {
        if ($this->app->runningInConsole()) {
            $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        }
    }

    private function registerRouteMiddleware() {
        app('router')->aliasMiddleware('adfilter', FilterParam::class);
    }

//    private function registerEventListener() {
//        $this->booting(function () {
//            Event::listen(HomeItemDeleteEvent::class, AdPositionHomeItemDeleteListener::class);
//        });
//
//    }

}
