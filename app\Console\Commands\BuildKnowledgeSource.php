<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Kanchangzhou\Article\Models\Article;

class BuildKnowledgeSource extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:build-knowledge-source';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle() {
        Article::without('likeCounter')
               ->select([
                   'id',
                   'title',
                   'published_at',
               ])
               ->selectRaw('REGEXP_REPLACE ( REGEXP_REPLACE ( content, "<[^>]+>", "" ), "(\n)+", "\n" ) AS content')
               ->where('status', Article::STATUS_FINAL)
               ->where('type', Article::TYPE_NORMAL)
               ->whereRaw("MATCH(title,content) against(? IN NATURAL LANGUAGE MODE)", ['新能源 新能源之都'])
               ->where('title', 'not like', '（学习测试）%')
               ->where('title', 'not like', '最新风险等级提醒%')
               ->whereRaw("MATCH(title,content) against(? IN NATURAL LANGUAGE MODE) > 30", ['新能源 新能源之都'])
               ->orderByRaw("MATCH(title,content) against (? IN NATURAL LANGUAGE MODE) desc,id desc", ['新能源 新能源之都'])
               ->chunk(100, function ($articles) {
                   foreach ($articles as $article) {
                       $content = preg_replace([
                           '/&nbsp;|&ensp;|&thinsp;|&emsp;|&zwj;| /',
                           "/&ldquo;/",
                           "/&rdquo;/",
                           "/(\n)+/",
                           "/&hellip;/",
                           "/&mdash;/",
                           "/&permil;/",
                           "/&middot;|&bull;/",
                           "/&lsquo;/",
                           "/&rsquo;/",
                           "/&rsquo;/",
                           "/&rarr;/",
                           "/&larr;/",
                           "/&times;/",
                           "/&gt;/",
                           "/&lt;/",
                           "/&divide;/",
                           "/&plusmn;/",
                           "/&amp;/",
                           "/&uarr;/",
                           "/&darr;/",
                           "/&deg;/",
                           "/&infin;/",
                           "/&le;/",
                       ], [
                           ' ',
                           "“",
                           "”",
                           "\n",
                           "…",
                           "—",
                           "‰",
                           "·",
                           "‘",
                           "’",
                           "’",
                           "→",
                           "←",
                           "x",
                           ">",
                           "<",
                           "÷",
                           "±",
                           "&",
                           "↑",
                           "↓",
                           "°",
                           "∞",
                           "≤",
                       ], $article->content);

                       Storage::put('knowledge-source/' . $article->title . '.txt', $article->title . "\n" . "发布日期:{$article->published_at},\n" . $content);
                   }
               });
    }
}
