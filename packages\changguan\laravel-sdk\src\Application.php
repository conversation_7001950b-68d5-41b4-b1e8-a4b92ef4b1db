<?php

namespace Changguan\LaravelSDK;

use Changguan\SDK\ChangguanSDK;
use Changguan\LaravelSDK\Supports\Config;

class Application
{
    /**
     * SDK 实例
     *
     * @var ChangguanSDK
     */
    protected $sdk;

    /**
     * 当前项目名称
     *
     * @var string
     */
    protected $currentProject;

    /**
     * 配置实例
     *
     * @var Config
     */
    protected $config;

    /**
     * 创建新的常观应用实例
     *
     * @param Config $config
     */
    public function __construct(Config $config) {
        $this->config = $config;
        $this->currentProject = $config->getDefaultProject();
        $this->sdk = $this->createSdkInstance();
    }

    /**
     * 创建新的 SDK 实例
     *
     * @return ChangguanSDK
     */
    protected function createSdkInstance(): ChangguanSDK {
        return new ChangguanSDK($this->getCurrentConfig());
    }

    /**
     * 获取当前项目的配置
     *
     * @return array
     */
    protected function getCurrentConfig(): array {
        return $this->config->getProjectConfig($this->currentProject);
    }

    /**
     * 切换到不同的项目
     *
     * @param string $project
     *
     * @return $this
     * @throws \InvalidArgumentException
     */
    public function project(string $project): self {
        if (!$this->config->hasProject($project)) {
            throw new \InvalidArgumentException("项目 [$project] 未配置。");
        }

        if ($this->currentProject !== $project) {
            $this->currentProject = $project;
            $this->sdk = $this->createSdkInstance();
        }

        return $this;
    }

    /**
     * 获取推送客户端实例
     *
     * @return \Changguan\SDK\Push\PushClient
     */
    public function push() {
        return $this->sdk->push();
    }

    /**
     * 获取加密服务实例
     *
     * @return \Changguan\SDK\Crypto\GmSm
     */
    public function crypto() {
        return $this->sdk->crypto();
    }

    /**
     * 获取 JWT 解码器实例
     *
     * @return \Changguan\SDK\Auth\JWTDecoder
     */
    public function jwt() {
        return $this->sdk->jwt();
    }

    /**
     * 获取签名验证器实例
     *
     * @return \Changguan\SDK\Auth\SignatureValidator
     */
    public function signature() {
        return $this->sdk->signature();
    }

    /**
     * 获取工具集实例
     *
     * @return \Changguan\SDK\Http\Tools
     */
    public function tools() {
        return $this->sdk->tools();
    }

    /**
     * 获取跳转链接构建器实例
     *
     * @return \Changguan\SDK\Navigation\RedirectToBuilder
     */
    public function redirect() {
        return $this->sdk->redirect();
    }

    /**
     * 获取 HTTP 客户端实例
     * @return \Changguan\SDK\Http\HttpClient
     * @throws \Changguan\SDK\Exceptions\HttpException
     * @throws \Changguan\SDK\Exceptions\SDKException
     */
    public function httpClient(){
        return $this->sdk->http();
    }

    /**
     * 获取 OAuth 客户端实例
     * @return \Changguan\SDK\OAuth\OAuthClient
     * @throws \Changguan\SDK\Exceptions\SDKException
     */
    public function oauth() {
        return $this->sdk->oauth();
    }

    /**
     * 获取当前项目名称
     *
     * @return string
     */
    public function getCurrentProject(): string {
        return $this->currentProject;
    }

    /**
     * 获取 SDK 版本
     *
     * @return string
     */
    public function version(): string {
        return $this->sdk->getVersion();
    }

    /**
     * 获取底层 SDK 实例
     *
     * @return ChangguanSDK
     */
    public function getSdk(): ChangguanSDK {
        return $this->sdk;
    }
}
