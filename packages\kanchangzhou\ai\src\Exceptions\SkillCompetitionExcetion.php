<?php

namespace Kanchangzhou\AI\Exceptions;

class SkillCompetitionExcetion extends AIException
{
    const HAS_BEEN_FINISHED = 305001;
    const NOT_FINISHED = 305002;

    public static function message($code) {
        $msgArr = [
            self::HAS_BEEN_FINISHED => '已经提交,无法再次修改',
            self::NOT_FINISHED => '尚未提交作品',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
