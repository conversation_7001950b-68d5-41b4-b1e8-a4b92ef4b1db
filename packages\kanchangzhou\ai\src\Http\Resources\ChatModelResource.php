<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ChatModelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'model_uuid'=>$this->model_uuid,
            'provider'=>$this->provider,
            'title'=>$this->title,
            'can_files'=>$this->can_files,
            'model_type'=>$this->model_type,
        ];
    }
}
