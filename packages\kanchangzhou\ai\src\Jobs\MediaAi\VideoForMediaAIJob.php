<?php

namespace Kanchangzhou\AI\Jobs\MediaAi;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiAsrAccurateJob;
use Kanchangzhou\AI\Jobs\MediaAi\Process\MediaAiFaceJob;
use Kanchangzhou\AI\Models\MediaAiFile;
use Kanchangzhou\AI\Services\MediaAi\UploadApi;
use Kanchangzhou\Livechannel\Models\Backend\Video;
use Kanchangzhou\Livechannel\Models\Backend\VideoDefinition;

class VideoForMediaAIJob extends MediaAiBaseJob
{
    protected $videoId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($videoId) {
        $this->videoId = $videoId;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $video = Video::where('status', 1)
                      ->where('aliyun_status', 4)
                      ->find($this->videoId);

        if (!$video) {
            return;
        }

        $mediaAiFile = MediaAiFile::where('fileaiable_id', $video->id)
                                  ->where('fileaiable_type', Video::class)
                                  ->first();

        if (!$mediaAiFile) {

            $mediaAiFile = MediaAiFile::create([
                'fileaiable_id' => $video->id,
                'fileaiable_type' => Video::class,
                'file_id' => Str::uuid(),
                'media_type' => MediaAiFile::MEDIA_TYPE_VIDEO,
                'nlp_text' => '',
            ]);

            $path = VideoDefinition::where('video_id', $video->id)
                                   ->where('definition', 'OD')
                                   ->value('aliyun_url');
            if (filter_var($path, FILTER_VALIDATE_URL) && Str::contains($path, 'livehls.cztv.tv')) {
                $uploadApi = new UploadApi();
                $result = $uploadApi->remoteUpload($path);
                $path = $result->json('data.path');
            }

            $mediaAiFile->media_file_url = $path;
            $mediaAiFile->save();
        } elseif ($mediaAiFile->updated_at->diffInDays(now()) >= 7) {

            $path = VideoDefinition::where('video_id', $video->id)
                                   ->where('definition', 'OD')
                                   ->value('aliyun_url');
            if (filter_var($path, FILTER_VALIDATE_URL) && Str::contains($path, 'livehls.cztv.tv')) {
                $uploadApi = new UploadApi();
                $result = $uploadApi->remoteUpload($path);
                $path = $result->json('data.path');
            }

            $mediaAiFile->media_file_url = $path;
            $mediaAiFile->save();
        }

        dispatch(new MediaAiAsrAccurateJob($mediaAiFile));
        dispatch(new MediaAiFaceJob($mediaAiFile));
    }
}
