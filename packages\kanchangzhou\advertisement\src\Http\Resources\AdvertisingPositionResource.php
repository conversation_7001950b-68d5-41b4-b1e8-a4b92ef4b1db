<?php

namespace Kanchangzhou\Advertisement\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AdvertisingPositionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [

            "id"=>$this->id,
            "position_name"=>$this->position_name,
            "key"=>$this->key,
            "position_type_id"=>$this->position_type_id,
            "type"=>$this->type,
            "ad_type"=>$this->ad_type,
            "ad_tag"=>$this->ad_tag,
            "has_pushed"=>$this->has_pushed
        ];
    }
}
