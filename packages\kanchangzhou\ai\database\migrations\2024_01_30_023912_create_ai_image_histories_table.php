<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ai_image_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->string('prompt')->nullable()->comment('关键词');
            $table->string('module')->nullable()->comment('模型名称');
            $table->string('provider')->nullable()->comment('服务提供者');
            $table->string('style')->nullable()->comment('图片风格');
            $table->double('width')->nullable()->comment('图片宽度');
            $table->double('height')->nullable()->comment('图片高度');
            $table->integer('image_num')->nullable()->comment('图片数量');
            $table->text('image_base64')->nullable()->comment('参考图base64');
            $table->string('image_url')->nullable()->comment('参考图完整URL');
            $table->string('change_degree')->nullable()->comment('参考图影响因子');
            $table->integer('progress')->nullable()->comment('图片生成总进度，进度包含2种，0为未处理完，1为处理完成');
            $table->string('status')->nullable()->comment('有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态');
            $table->string('log_id')->nullable()->comment('日志ID');
            $table->string('task_id')->nullable()->index('task_id')->comment('任务ID');
            $table->integer('user_id')->nullable()->comment('用户ID');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_image_histories');
    }
};
