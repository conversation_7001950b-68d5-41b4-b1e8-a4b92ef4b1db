<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAiVideoAuditsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('ai_video_audits', function (Blueprint $table) {
            $table->id();
            $table->uuid('video_uuid');
            $table->string('title');
            $table->string('video_path', 1000)
                  ->nullable();
            $table->string('etag', 32);
            $table->string('nickname')
                  ->nullable();
            $table->integer('user_id');
            $table->string('status', 32);
            $table->string('task_id')
                  ->nullable();
            $table->string('request_id')
                  ->nullable();
            $table->dateTime('checked_at')
                  ->nullable();
            $table->json('frame_results')
                  ->nullable();
            $table->json('voice_results')
                  ->nullable();
            $table->json('ai_results')
                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('ai_video_audits');
    }
}
