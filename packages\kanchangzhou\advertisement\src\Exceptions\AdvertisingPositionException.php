<?php
namespace Kanchangzhou\Advertisement\Exceptions;

class AdvertisingPositionException extends BaseException{
    public static function message($code) {
        $msgArr = [
            self::HAS_EXISTS => '广告位已存在',
            self::NOT_EXISTS => '广告位不存在',
            self::OPERATION_SUCCESS => '操作成功',
            self::OPERATION_FAIL => '操作失败',
            self::VALIDATE_FAIL=>"数据验证错误",
            self::CAN_NOT_OPERATION=>"广告位下有广告无法删除广告位",
        ];
        return key_exists($code, $msgArr) ? $msgArr[$code] : parent::message($code);
    }
}
