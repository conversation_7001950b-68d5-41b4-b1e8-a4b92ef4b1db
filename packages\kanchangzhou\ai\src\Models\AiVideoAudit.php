<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\AiVideoAudit
 *
 * @property int $id
 * @property string $video_uuid
 * @property string $title
 * @property string $video_path
 * @property string $etag
 * @property string|null $nickname
 * @property int $user_id
 * @property string $status
 * @property string|null $task_id
 * @property string|null $request_id
 * @property \Illuminate\Support\Carbon|null $checked_at
 * @property array|null $frame_results
 * @property array|null $voice_results
 * @property array|null $ai_results
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $status_str
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereAiResults($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereCheckedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereEtag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereFrameResults($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereVideoPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereVideoUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoAudit whereVoiceResults($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAiVideoAudit
 */
class AiVideoAudit extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'status_str',
    ];

    protected $casts = [
        'checked_at' => 'datetime',
        'frame_results' => 'json',
        'voice_results' => 'json',
        'ai_results' => 'json',
    ];

    const STATUS_UPLOADING = 'uploading';
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAIL = 'fail';

    const STATUS_MAP = [
        self::STATUS_UPLOADING => '上传中',
        self::STATUS_PENDING => '待审核',
        self::STATUS_PROCESSING => '审核中',
        self::STATUS_SUCCESS => '已审核',
        self::STATUS_FAIL => '审核失败',
    ];

    public function getStatusStrAttribute() {
        return self::STATUS_MAP[$this->status] ?? '';
    }

}
