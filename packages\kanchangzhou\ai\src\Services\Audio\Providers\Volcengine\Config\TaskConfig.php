<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Config;

use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsConfigInterface;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Services\AiUser;


class TaskConfig implements AiTtsConfigInterface
{
    private $appid;
    private $reqid;
    private $text;
    private $format;
    private $voice_type;
    private $voice;
    private $language;
    private $sample_rate;
    private $volume;
    private $speed;
    private $pitch;
    private $enable_subtitle;
    private $sentence_interval;
    private $style;
    private $callback_url;

    public function __construct($config = []) {
        $this->appid = config('kai.tts.providers.volcengine.appid');
        $this->reqid = \Str::uuid()->toString();
        $this->text = $config['text'];
        $this->format = $config['format'] ?? 'mp3';
        $this->voice_type = $config['voice_type'] ?? 'BV701_streaming';
        $this->callback_url = url(config('kai.tts.providers.volcengine.call_back_url'));

        $this->voice = $config['voice'] ?? null;
        $this->language = $config['language'] ?? null;
        $this->sample_rate = $config['sample_rate'] ?? null;
        $this->volume = $config['volume'] ?? null;
        $this->speed = $config['speed'] ?? null;
        $this->pitch = $config['pitch'] ?? null;
        $this->enable_subtitle = $config['enable_subtitle'] ?? null;
        $this->sentence_interval = $config['sentence_interval'] ?? null;
        $this->style = $config['style'] ?? null;
    }

    public function toArray() {
        $array = [
            'appid' => $this->appid,
            'reqid' => $this->reqid,
            'text' => $this->text,
            'format' => $this->format,
            'voice_type' => $this->voice_type,
            'voice' => $this->voice,
            'language' => $this->language,
            'sample_rate' => $this->sample_rate,
            'volume' => $this->volume,
            'speed' => $this->speed,
            'pitch' => $this->pitch,
            'enable_subtitle' => $this->enable_subtitle,
            'sentence_interval' => $this->sentence_interval,
            'style' => $this->style,
            'callback_url' => $this->callback_url,
        ];

        foreach($array as $k=>&$v){
            if($v == null){
                unset($array[$k]);
            }
        }

        return $array;
    }

    public function toJson() {
        return json_encode($this->toArray());
    }
}
