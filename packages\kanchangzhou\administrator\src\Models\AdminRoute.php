<?php

namespace Kanchangzhou\Administrator\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Kanchangzhou\Administrator\Traits\ModelTree;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;

/**
 * Kanchangzhou\Administrator\Models\AdminRoute
 *
 * @property int $id
 * @property string $title
 * @property string|null $route_str
 * @property int|null $parent_id
 * @property int|null $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $platform 平台
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AdminRoute> $children
 * @property-read int|null $children_count
 * @property-read AdminRoute|null $parent
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Role> $roles
 * @property-read int|null $roles_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute permission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute role($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute wherePlatform($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereRouteStr($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminRoute whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAdminRoute
 */
class AdminRoute extends Model
{
    use HasFactory, HasRoles;

    use ModelTree {
        ModelTree::boot as treeBoot;
    }

    protected $guarded = [];

    public $guard_name = 'kadmin';

}
