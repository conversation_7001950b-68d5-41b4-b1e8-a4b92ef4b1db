<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Services\AiUser;

/**
 * Kanchangzhou\AI\Models\AiVideoHistory
 *
 * @property int $id
 * @property string|null $prompt 关键词
 * @property string|null $module 模型名称
 * @property string|null $provider 服务提供者
 * @property string|null $video_origin 原始图片路径
 * @property string|null $video_local 本地视频路径
 * @property float|null $width 视频宽度
 * @property float|null $height 视频高度
 * @property string|null $image_base 参考图完整URL
 * @property string|null $style 视频风格
 * @property string|null $ratio 比例
 * @property string|null $motion_strength 运动强度
 * @property string|null $motion_scale 运动模式 json格式
 * @property string|null $asset_id 角色ID pixverse专用
 * @property string|null $seed 选填,种子,不传则随机
 * @property string|null $price 视频价格
 * @property string|null $point 视频点数
 * @property string|null $failed_reason 失败原因
 * @property int|null $progress 图片生成总进度，进度包含2种，0为未处理完，1为处理完成
 * @property string|null $status 有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
 * @property string|null $task_id 任务ID
 * @property int|null $user_id 用户ID
 * @property string $guard_name
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereAssetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereFailedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereImageBase($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereModule($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereMotionScale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereMotionStrength($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereProgress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory wherePrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereRatio($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereSeed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereStyle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereVideoLocal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereVideoOrigin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory whereWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiVideoHistory withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiVideoHistory
 */
class AiVideoHistory extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [

    ];

    protected $casts = [

    ];

    protected $fillable = [
        'prompt',
        'module',
        'provider',
        'progress',
        'status',
        'ratio',
        'style',
        'image_base',
        'motion_strength',
        'asset_id',
        'motion_scale',
        'seed',
        'task_id',
        'user_id',
        'guard_name'
    ];

    public static function createFromRequest($task_id, array $request)
    {
        $aiUser = new AiUser(AuthFacade::adminUser()
            ->getId(), 'kadmin');

        return self::create([
            'task_id' => $task_id,

            'prompt' => $request['prompt'],
            'module' => $request['module'],
            'provider' => $request['provider'],
            'progress' => $request['progress'] ?? 0,
            'status' => $request['status']  ?? 'INIT',

            'ratio' => $request['ratio'] ?? null,
            'style' => $request['style'] ?? null,
            'image_base' => $request['image_base'] ?? null,
            'motion_strength' => $request['motion_strength'] ?? null,
            'motion_scale' => $request['motion_scale'] ?? null,
            'asset_id' => $request['asset_id'] ?? null,
            'seed' => $request['seed'] ?? null,

            'user_id' => $aiUser->getUserId(),
            'guard_name' => $aiUser->getGuardName(),
        ]);
    }

    public static function updateByTaskId($taskId, array $updateData)
    {
        return self::where('task_id', $taskId)->update($updateData);
    }
}
