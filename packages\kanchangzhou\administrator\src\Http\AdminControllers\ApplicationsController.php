<?php


namespace Kanchangzhou\Administrator\Http\AdminControllers;


use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Models\AdminApplication;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ApplicationsController extends BaseController
{
    public function index() {
        PermissionHook::can('应用管理.列表');

        $apiKeys = AdminApplication::with('adminUser')
                                   ->latest()
                                   ->paginate(20);

        return Respond::respondWithData(JsonResource::collection($apiKeys));
    }

    public function store(Request $request) {
        PermissionHook::can('应用管理.新增');

        try {
            $this->validate($request, [
                'title' => 'required',
                'admin_user_id' => 'required|exists:' . AdminUser::class . ',id',
            ], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        AdminApplication::create([
            'title' => $request->input('title'),
            'admin_user_id' => $request->input('admin_user_id'),
            'app_key' => Str::random(16),
            'app_secret' => Str::random(32),
        ]);

        return Respond::respondWithData();
    }

    public function destroy($id) {
        PermissionHook::can('应用管理.删除');

        $apiKey = AdminApplication::where('id', $id)
                                  ->firstOrFail();

        $apiKey->delete();

        return Respond::respondWithData();
    }
}
