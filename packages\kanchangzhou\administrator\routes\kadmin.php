<?php

use Kanchangzhou\Administrator\Http\Controllers\AuthController;
use Kanchangzhou\Administrator\Http\AdminControllers\{AdminUserController,
    DepartmentController,
    MyController,
    PermissionController,
    RoleController,
    ModelPermissionController,
    ModelPermissionOnlyController,
    AdminRouteController
};
use Kanchangzhou\Administrator\Http\Controllers\NotifyController;

Route::prefix('api/v1/admin')
     ->as('kadmin.')
     ->group(function () {
         // 无需登录

         Route::middleware(config('kadmin.route.middleware.no_auth'))
              ->group(function () {
                  Route::post('login/{type?}', [
                      AuthController::class,
                      'login',
                  ])
                       ->name('token.create');

                  Route::post('token/refresh', [
                      AuthController::class,
                      'refresh',
                  ])
                       ->name('token.refresh');

                  Route::post('oauth/login', [AuthController::class, 'oauth'])
                       ->name('oauth.login');

                  Route::post('notify/changguan', [NotifyController::class, 'changguanNotify'])
                       ->name('notify.changguan');
              });

         // 需要登录
         Route::middleware(config('kadmin.route.middleware.admin'))
              ->group(function () {
                  Route::post('logout', [
                      AuthController::class,
                      'logout',
                  ])
                       ->name('token.logout');

                  //                  Route::get('permissions', [
                  //                      AuthController::class,
                  //                      'permissions',
                  //                  ]);
              });


         // 后台应用接口
         Route::middleware(config('kadmin.route.middleware.admin'))
              ->group(function () {
                  Route::post('adminusers/{id}/restore', [
                      AdminUserController::class,
                      'restore',
                  ]);

                  Route::resource('adminusers', AdminUserController::class)
                       ->only([
                           'index',
                           'show',
                           'store',
                           'update',
                           'destroy',
                       ]);

                  Route::resource('admin-departments', DepartmentController::class)
                       ->only([
                           'index',
                           'show',
                           'store',
                           'update',
                           'destroy',
                       ]);

                  Route::get('my', [
                      MyController::class,
                      'show',
                  ]);

                  Route::post('my/edit', [
                      MyController::class,
                      'update',
                  ]);

                  Route::post('my/has-permission', [
                      AdminUserController::class,
                      'hasPermission',
                  ]);

                  Route::resource('permissions', PermissionController::class)
                       ->only([
                           'index',
                           'show',
                           'store',
                           'update',
                           'destroy',
                       ]);

                  Route::resource('roles', RoleController::class)
                       ->only([
                           'index',
                           'show',
                           'store',
                           'update',
                           'destroy',
                       ]);

                  Route::get('model-permission/form-options', [
                      ModelPermissionController::class,
                      'formOptions',
                  ]);

                  Route::post('model-permission/admin-user/{adminId}/{targetAlias}/check', [
                      ModelPermissionController::class,
                      'checkChosen',
                  ]);

                  Route::get('model-permission/admin-user/{adminId}/{targetAlias}', [
                      ModelPermissionController::class,
                      'index',
                  ]);

                  Route::post('model-permission/admin-user/{adminId}/{targetAlias}', [
                      ModelPermissionController::class,
                      'update',
                  ]);
                  Route::delete('model-permission/admin-user/{adminId}/{targetAlias}', [
                      ModelPermissionController::class,
                      'destroy',
                  ]);


                  Route::get('model-permission-only/form-options', [
                      ModelPermissionOnlyController::class,
                      'formOptions',
                  ]);

                  Route::post('model-permission-only/admin-user/{adminId}/{targetAlias}/check', [
                      ModelPermissionOnlyController::class,
                      'checkChosen',
                  ]);

                  Route::get('model-permission-only/admin-user/{adminId}/{targetAlias}', [
                      ModelPermissionOnlyController::class,
                      'index',
                  ]);

                  Route::post('model-permission-only/admin-user/{adminId}/{targetAlias}', [
                      ModelPermissionOnlyController::class,
                      'update',
                  ]);
                  Route::delete('model-permission-only/admin-user/{adminId}/{targetAlias}', [
                      ModelPermissionOnlyController::class,
                      'destroy',
                  ]);

                  Route::resource('admin-routes', AdminRouteController::class);

                  Route::resource('applications', \Kanchangzhou\Administrator\Http\AdminControllers\ApplicationsController::class)
                       ->only([
                           'index',
                           'store',
                           'destroy',
                       ]);
              });
     });
