<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('ai_resource_packages', function (Blueprint $table) {
            $table->string('provider')
                  ->nullable()
                  ->comment('AI服务提供商')
                  ->after('type');
            $table->string('model_type')
                  ->nullable()
                  ->comment('模型类型')
                  ->after('provider');
            $table->string('model_id')
                  ->nullable()
                  ->comment('模型ID')
                  ->after('model_type');
            // 是否通用
            $table->boolean('is_general')
                  ->default(1)
                  ->comment('是否通用')
                  ->after('model_id');
        });

        Schema::table('ai_user_packages', function (Blueprint $table) {
            $table->string('provider')
                  ->nullable()
                  ->comment('AI服务提供商')
                  ->after('package_type');
            $table->string('model_type')
                  ->nullable()
                  ->comment('模型类型')
                  ->after('provider');
            $table->string('model_id')
                  ->nullable()
                  ->comment('模型ID')
                  ->after('model_type');
            // 是否通用
            $table->boolean('is_general')
                  ->default(1)
                  ->comment('是否通用')
                  ->after('model_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('ai_resource_packages', function (Blueprint $table) {
            $table->dropColumn('provider');
            $table->dropColumn('model_type');
            $table->dropColumn('model_id');
            $table->dropColumn('is_general');
        });

        Schema::table('ai_user_packages', function (Blueprint $table) {
            $table->dropColumn('provider');
            $table->dropColumn('model_type');
            $table->dropColumn('model_id');
            $table->dropColumn('is_general');
        });
    }
};
