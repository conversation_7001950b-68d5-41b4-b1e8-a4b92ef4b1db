<?php

namespace Kanchangzhou\AI\Services\Chat\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatService;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatResponse;
use Orhanerday\OpenAi\OpenAi;

class OpenAiBase extends AIChatService
{
    protected $serviceProvider = 'OpenAI';
    protected $hasSystemRole = true;

    public function handler(): AIChatResponse {

        $openAi = new OpenAi(config('kai.chat.providers.openai.api_key'));
        $openAi->setBaseURL(config('kai.chat.providers.openai.base_url'));

        $response = $openAi->chat([
            'model' => $this->chatModel?->model_id,
            'messages' => $this->messageWithHistory(),
            'temperature' => $this->chatModel?->temperature ?? 0.8,
            "max_tokens" => 2000,
            "presence_penalty" => $this->chatModel?->penalty ?? 1,
            "stream" => false,
        ]);
        $res = json_decode($response);

        if (isset($res->error)) {
            throw new \Exception($res->error->message);
        } else {
            if (isset($res->choices[0]->message->content)) {
                return new AIChatResponse([
                    'message' => $res->choices[0]->message->content,
                    'chat_uuid' => $this->chatUuid,
                    'total_tokens' => $res->usage->total_tokens,
                    'input_tokens' => $res->usage->prompt_tokens,
                    'output_tokens' => $res->usage->completion_tokens,
                    'source_id' => $res->id,
                ]);
            }
        }

        throw new AIException(AIException::NETWORK_ERROR, [], 400, '请求失败(' . __('ai::ai.' . $res->json('message')) . ')');

    }

    public function streamHandler() {

        $openAi = new OpenAi(config('kai.chat.providers.openai.api_key'));
        $openAi->setBaseURL($this->chatModel?->api_gateway ?? config('kai.chat.providers.openai.base_url'));

        $opts = [
            'model' => $this->chatModel?->model_id,
            'messages' => $this->messageWithHistory(),
            'temperature' => $this->chatModel?->temperature ?? 0.8,
            "max_tokens" => 2000,
            "presence_penalty" => $this->chatModel?->penalty ?? 1,
            "stream" => true,
            "stream_options" => ["include_usage" => true],
        ];

//        header('Content-type: text/event-stream');
//        header('Cache-Control: no-cache');
        $returnResponse = [
            'message' => '',
            'chat_uuid' => $this->chatUuid,
            'total_tokens' => 0,
            'input_tokens' => 0,
            'output_tokens' => 0,
            'source_id' => '',
        ];

        $openAi->chat($opts, function ($curl_info, $data) use (&$returnResponse) {
            $buffer = $data;
            $buffer = str_replace('data: {', '{', $buffer);
            $buffer = str_replace('data: [', '[', $buffer);

            $buffer = str_replace("}\n\n{", '}[br]{', $buffer);
            $buffer = str_replace("}\n\n[", '}[br][', $buffer);

            $lines = explode('[br]', $buffer);

            $line_c = count($lines);

            $message = '';
            $finishReason = '';

            foreach ($lines as $line) {
                if (trim($line) != '[DONE]') {
                    $lineData = json_decode(trim($line), TRUE);

                    if (isset($lineData['error'])) {
                        echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                        echo "event:error" . PHP_EOL;
                        echo "data:" . json_encode([
                                'time' => microtime(true),
                                'message' => __('ai::ai.' . $lineData['error']['message']),
                                'code' => $lineData['error']['code'],
                            ]) . PHP_EOL . PHP_EOL;
                        flush();

                        $returnResponse['message'] = $lineData['error']['message'];
                        $returnResponse['is_error'] = true;

                        return strlen($data);
                    }

                    if (!is_array($lineData) || !isset($lineData['choices']) || !isset($lineData['choices'][0]) || !isset($lineData['choices'][0]['delta']) || !isset($lineData['choices'][0]['delta']['content'])) {
//                        $finishReason = $lineData['choices'][0]['finish_reason'];
                        $returnResponse['total_tokens'] = $lineData['usage']['total_tokens'] ?? 0;
                        $returnResponse['input_tokens'] = $lineData['usage']['prompt_tokens'] ?? 0;
                        $returnResponse['output_tokens'] = $lineData['usage']['completion_tokens'] ?? 0;
                        continue;
                    }
                    $message .= $lineData['choices'][0]['delta']['content'];
                    $finishReason = $lineData['choices'][0]['finish_reason'] ?? '';
                    $returnResponse['source_id'] = $lineData['id'];
                }
            }

            echo "id:" . (microtime(true) * 10000) . PHP_EOL;
            echo "event:message" . PHP_EOL;
            echo "data: " . json_encode([
                    'time' => microtime(true),
                    'message' => $message,
                    'finish_reason' => $finishReason,
                ]) . PHP_EOL . PHP_EOL;
            flush();

            $returnResponse['message'] .= $message;
//            $returnResponse['total_tokens'] = $lineData['usage']['total_tokens'] ?? 0;
//            $returnResponse['input_tokens'] = $lineData['usage']['prompt_tokens'] ?? 0;
//            $returnResponse['output_tokens'] = $lineData['usage']['completion_tokens'] ?? 0;
//            $returnResponse['source_id'] = $lineData['id'] ?? '';

            return strlen($data);
        });

        return new AIChatResponse($returnResponse);
    }

    public function uploadFile(Request $request) {
        // TODO: Implement uploadFile() method.
    }
}
