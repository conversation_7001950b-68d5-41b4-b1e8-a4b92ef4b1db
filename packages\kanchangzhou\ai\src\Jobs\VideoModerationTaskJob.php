<?php

namespace Kanchangzhou\AI\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Models\AiVideoAudit;
use Kanchangzhou\AI\Supports\GreenContent;

class VideoModerationTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $video;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($video) {
        $this->video = $video;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        // 调用视频审核job
        $task = GreenContent::plusVideoModerationTask([
            'ossBucketName' => config('kai.other.aliyun.oss.bucket'),
            'ossObjectName' => $this->video->video_path,
            'ossRegionId' => config('kai.other.aliyun.oss.region'),
            'callback' => route('ai.moderation.callback'),
            'seed' => Str::replace('-', '', $this->video->video_uuid),
            'dataId' => $this->video->video_uuid,
        ]);

        $this->video->task_id = $task['TaskId'];
        $this->video->checked_at = Carbon::now();
        $this->video->status = AiVideoAudit::STATUS_PROCESSING;
        $this->video->save();

        return true;
    }
}
