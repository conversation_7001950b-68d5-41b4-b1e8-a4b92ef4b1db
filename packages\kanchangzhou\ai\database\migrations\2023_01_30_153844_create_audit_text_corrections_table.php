<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('audit_text_corrections', function (Blueprint $table) {
            $table->id();
            $table->string('word');
            $table->string('replace')
                  ->nullable();
            $table->string('type')
                  ->default(\Kanchangzhou\AI\Models\AuditTextCorrection::TYPE_WHITE);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('audit_text_corrections');
    }
};
