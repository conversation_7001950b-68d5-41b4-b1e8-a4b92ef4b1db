<?php


namespace Kanchangzhou\Administrator\Exceptions;


use Kanchangzhou\Kernel\Exceptions\BaseException;
use Spatie\Permission\Exceptions\GuardDoesNotMatch;
use <PERSON><PERSON>\Permission\Exceptions\PermissionAlreadyExists;
use Spatie\Permission\Exceptions\PermissionDoesNotExist;
use Spa<PERSON>\Permission\Exceptions\RoleAlreadyExists;
use Spatie\Permission\Exceptions\RoleDoesNotExist;
use Throwable;

class PermissionFailException extends BaseException
{

    const ROLE_DOES_NOT_EXIST = 20320;
    const ROLE_ALREADY_EXISTS = 20321;
    const PERMISSION_DOES_NOT_EXIST = 20322;
    const PERMISSION_ALREADY_EXISTS = 20323;
    const GUARD_DOES_NOT_MATCH = 20324;
    const SUPER_ADMIN_CANT_DEL = 20325;

    public function __construct($errCode = 10000, $errData = [], Throwable $previous = null) {
        $this->errCode = $errCode;
        $this->errData = $errData;

        if ($previous instanceof PermissionDoesNotExist) {
            $this->errCode = static::PERMISSION_DOES_NOT_EXIST;
        } elseif ($previous instanceof PermissionAlreadyExists) {
            $this->errCode = static::PERMISSION_ALREADY_EXISTS;
        } elseif ($previous instanceof RoleDoesNotExist) {
            $this->errCode = static::ROLE_DOES_NOT_EXIST;
        } elseif ($previous instanceof RoleAlreadyExists) {
            $this->errCode = static::ROLE_ALREADY_EXISTS;
        } elseif ($previous instanceof GuardDoesNotMatch) {
            $this->errCode = static::GUARD_DOES_NOT_MATCH;
        } elseif ($previous instanceof BaseException) {
            $this->errCode = $previous->getErrCode();
            $this->errData = $previous->getErrData();
        }

        parent::__construct($this->errCode, $this->errData);
    }

    public static function message($code) {
        $msgArr = [
            static::PERMISSION_DOES_NOT_EXIST => '权限不存在',
            static::PERMISSION_ALREADY_EXISTS => '权限已存在',
            static::ROLE_DOES_NOT_EXIST => '角色不存在',
            static::ROLE_ALREADY_EXISTS => '角色已存在',
            static::GUARD_DOES_NOT_MATCH => '角色不存在',
            static::SUPER_ADMIN_CANT_DEL => '超级管理员角色无法被删除',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : parent::message($code);
    }
}