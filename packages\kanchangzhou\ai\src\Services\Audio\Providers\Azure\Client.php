<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Azure;

use GuzzleHttp\Client as guzzleClient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Exceptions\AiAudioException;
use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsClient;
use Kanchangzhou\AI\Services\Audio\Handlers\AiAudioWsHandler;
use Kanchangzhou\AI\Services\Audio\Providers\Azure\Auth;
use Kanchangzhou\AI\Services\Audio\Providers\Azure\Config\StreamConfig;
use Kanchangzhou\AI\Services\Audio\Providers\Azure\Config\TaskConfig;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Models\AiAudioHistory;

class Client extends AiTtsClient
{
    private $auth;
    private $accessKeySecret;
    private $host = 'eastus.tts.speech.microsoft.com';
    //private $host = 'eastus.customvoice.api.speech.microsoft.com';
    private $path_tts = '/cognitiveservices/v1';
    private $path_list = '/cognitiveservices/voices/list';

    public function __construct($appKey, $accessKeyId, $accessKeySecret) {
        parent::__construct();
        $this->auth = (new Auth($accessKeyId, $accessKeySecret))->getAccessToken();
        $this->accessKeySecret = $accessKeySecret;
    }

    public function textToSpeechStream(string $text) {

    }

    public function textToSpeech(string $text) {
        try {
            $guzzleClient = new guzzleClient();
            $headers = [
                'X-Microsoft-OutputFormat' => 'riff-24khz-16bit-mono-pcm',
                'Content-Type' => 'application/ssml+xml',
                'Host' => $this->host,
                'Content-Length' => strlen($text),
                'Authorization' => 'Bearer ' . $this->auth,
                'User-Agent' => 'tts',
            ];

            $response = $guzzleClient->post("https://{$this->host}{$this->path_tts}", [
                'headers' => $headers,
                'body' => $text,
            ]);

            // 检查响应状态，如果是200则将响应体保存为音频文件
            if ($response->getStatusCode() == 200) {

                $task_id = \Str::uuid();
                $path = 'ai-audios'.'/'.date('Ymd').'/'.$task_id;
                if (!\Storage::disk('oss')->exists($path)) {
                    \Storage::disk('oss')->put($path, $response->getBody()->getContents());
                }
                $audio_local = \Storage::disk('oss')->url($path);

                //保存记录
                AiAudioHistory::createFromRequest(null,[
                    'task_id' => $task_id,
                    'prompt' => $text,
                    'module' => 'azure',
                    'provider' => __FUNCTION__,
                    'progress' => 100,
                    'audio_local' => $audio_local,
                    'status' => 'SUCCESS',
                ]);

                return [
                    'message' => 'Speech synthesis completed.',
                    'file' => $audio_local
                ];
            } else {
                //throw new Exception('Request failed with status: ' . $response->getStatusCode());
                throw new AiAudioException(AiAudioException::FAILED_REQUEST);
            }
        } catch (RequestException $e) {
            //return response()->json(['error' => $e->getMessage()], 500);
            throw new AiAudioException(AiAudioException::FAILED_REQUEST);
        }
    }

    public function getVoicesList($request) {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->auth,
            'User-Agent' => 'voices list',
        ])->get("https://{$this->host}{$this->path_list}");

        if ($response->successful()) {

            $list = $response->json();

            //筛选中文信息
            foreach($list as $k=>$v){
                if($request['language']){
                    if(strpos($v['Locale'], $request['language']) === false){
                        unset($list[$k]);
                    }
                }
                if($request['gender']){
                    if($v['Gender'] !== $request['gender']){
                        unset($list[$k]);
                    }
                }
            }
            return $list;
        }

        throw new AiAudioException(AiAudioException::FAILED_REQUEST);
    }

    public function createTask(string $text) {
        try {
            $guzzleClient = new guzzleClient();
            $headers = [
                'X-Microsoft-OutputFormat' => 'riff-24khz-16bit-mono-pcm',
                'Content-Type' => 'application/ssml+xml',
                'Host' => $this->host,
                'Content-Length' => strlen($text),
                'Authorization' => 'Bearer ' . $this->auth,
                'User-Agent' => 'tts',
            ];

            $response = $guzzleClient->post("https://{$this->host}{$this->path_tts}", [
                'headers' => $headers,
                'body' => $text,
            ]);

            // 检查响应状态，如果是200则将响应体保存为音频文件
            if ($response->getStatusCode() == 200) {

                $task_id = \Str::uuid();
                $path = 'ai-audios'.'/'.date('Ymd').'/'.$task_id;
                if (!\Storage::disk('oss')->exists($path)) {
                    \Storage::disk('oss')->put($path, $response->getBody()->getContents());
                }
                $audio_local = \Storage::disk('oss')->url($path);

                //保存记录
                $res = AiAudioHistory::createFromRequest(null,[
                    'task_id' => $task_id,
                    'prompt' => $text,
                    'module' => 'azure',
                    'provider' => __FUNCTION__,
                    'progress' => 100,
                    'audio_local' => $audio_local,
                    'status' => 'SUCCESS',
                ]);

                return $res;
            } else {
                throw new AiAudioException(AiAudioException::FAILED_REQUEST);
            }
        } catch (RequestException $e) {
            throw new AiAudioException(AiAudioException::FAILED_REQUEST,[],400,$e->getMessage());
        }
    }
    public function fetchTaskResult($taskId) {
        $info = AiAudioHistory::where('task_id', $taskId)
            ->firstOrFail();

        //失败
        if($info['status'] == 'FAILED'){
            throw new AiAudioException(AiAudioException::FAILED_REQUEST,[],400,$info['failed_reason']);
        }

        return $info;
    }
    public function rsync(string $taskId) {

    }
}
