<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiModelPrice
 *
 * @property int $id
 * @property string $provider
 * @property string $model_id
 * @property string $model_type
 * @property string $price 价格
 * @property int $unit_count 单位数量
 * @property int $is_show 是否显示 1:显示 2:不显示
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $is_show_str
 * @property-read mixed $model_type_str
 * @property-read mixed $provider_str
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereIsShow($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereUnitCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiModelPrice withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiModelPrice
 */
class AiModelPrice extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [
        'provider_str',
        'is_show_str',
        'model_type_str'
    ];

    const IS_SHOW_NO = 1;
    const IS_SHOW_YES = 2;

    const IS_SHOW_MAP = [
        self::IS_SHOW_NO => '不显示',
        self::IS_SHOW_YES => '显示',
    ];

    const MODEL_TYPE_CHAT = ChatModel::MODEL_TYPE_CHAT;
    const MODEL_TYPE_IMAGE = ChatModel::MODEL_TYPE_IMAGE;
    const MODEL_TYPE_VIDEO = ChatModel::MODEL_TYPE_VIDEO;
    const MODEL_TYPE_AUDIO = ChatModel::MODEL_TYPE_AUDIO;

    const MODEL_TYPE_MAP = ChatModel::MODEL_TYPE_MAP;


    const PROVIDER_ALI = ChatModel::PROVIDER_ALI;
    const PROVIDER_OPENAI = ChatModel::PROVIDER_OPENAI;
    const PROVIDER_BAIDU = ChatModel::PROVIDER_BAIDU;
    const PROVIDER_KNOWLEDGE = ChatModel::PROVIDER_KNOWLEDGE;

    const PROVIDER_MAP = ChatModel::PROVIDER_MAP;

    public function getProviderStrAttribute() {
        return self::PROVIDER_MAP[$this->provider] ?? '';
    }

    public function getIsShowStrAttribute() {
        return self::IS_SHOW_MAP[$this->is_show] ?? '';
    }

    public function getModelTypeStrAttribute() {
        return self::MODEL_TYPE_MAP[$this->model_type] ?? '';
    }
}
