<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('chat_settings', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('key');
            $table->text('value');
            $table->string('value_type')
                  ->default('string')
                  ->comment('string, array');
            $table->timestamps();
        });


        // 填入默认配置值, 以及默认配置值的说明
        // 1. 每日限制字数 , 2. 总限制字数 , 3. 每日限制次数 , 4. 总限制次数
        $data = [
            [
                'title' => '对话每日限制字数',
                'key' => 'chat_daily_limit_tokens',
                'value' => 10000,
                'value_type' => 'string',
            ],
            [
                'title' => '对话每日限制次数',
                'key' => 'chat_daily_limit_times',
                'value' => 1000,
                'value_type' => 'string',
            ],
            // 图像限制条数和次数
            [
                'title' => '图片每日限制张数',
                'key' => 'image_daily_limit_tokens',
                'value' => 10000,
                'value_type' => 'string',
            ],
            [
                'title' => '图片每日限制次数',
                'key' => 'image_daily_limit_times',
                'value' => 1000,
                'value_type' => 'string',
            ],
            // 视频限制
            [
                'title' => '视频每日限制条数',
                'key' => 'video_daily_limit_tokens',
                'value' => 10000,
                'value_type' => 'string',
            ],
            [
                'title' => '视频每日限制次数',
                'key' => 'video_daily_limit_times',
                'value' => 1000,
                'value_type' => 'string',
            ],
            // 音频限制
            [
                'title' => '音频每日限制条数',
                'key' => 'audio_daily_limit_tokens',
                'value' => 1000,
                'value_type' => 'string',
            ],
            [
                'title' => '音频每日限制次数',
                'key' => 'audio_daily_limit_times',
                'value' => 1000,
                'value_type' => 'string',
            ],
        ];

        DB::table('chat_settings')->insert($data);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('chat_settings');
    }
};
