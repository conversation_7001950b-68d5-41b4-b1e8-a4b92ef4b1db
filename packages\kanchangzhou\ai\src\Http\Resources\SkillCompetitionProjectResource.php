<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\AI\Models\SkillCompetition;
use Kanchangzhou\Kernel\Hooks\PermissionHook;

class SkillCompetitionProjectResource extends JsonResource
{
    public function toArray($request) {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'title' => $this->title,
            'content' => $this->content,
            'background_music' => $this->background_music,
            'status' => $this->status,
            $this->mergeWhen(PermissionHook::can('技能大赛.评委|技能大赛.管理', '', false), [
                'ai_score_desc' => $this->ai_score_desc,
                'ai_score' => $this->ai_score,
                'ai_score_status' => $this->ai_score_status,
                'ai_media_score' => $this->ai_media_score,
                'scores' => $this->scores,
            ]),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'can_score' => PermissionHook::can('技能大赛.评委', '', false) && $this->status == SkillCompetition::STATUS_FINISHED,
        ];
    }
}
