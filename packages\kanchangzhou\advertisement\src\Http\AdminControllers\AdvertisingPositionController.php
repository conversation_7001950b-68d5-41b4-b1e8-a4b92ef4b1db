<?php

namespace Kanchangzhou\Advertisement\Http\AdminControllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingPositionException;
use Kanchangzhou\Advertisement\Http\Controllers\Controller;
use Kanchangzhou\Advertisement\Http\Resources\AdvertisingPositionResource;
use Kanchangzhou\Advertisement\Models\Advertising;
use Kanchangzhou\Advertisement\Models\AdvertisingPosition;
use Kanchangzhou\Advertisement\Http\Requests\StoreEditAdvertisingPositionPost;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Kernel\Supports\Response;

class AdvertisingPositionController extends Controller
{

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(StoreEditAdvertisingPositionPost $request) {
        PermissionHook::can("广告位.新建");
        $validated = $request->validated();

        $position_name = $validated["position_name"];
        $position_type_id = $validated["position_type_id"];
        $type = $validated["type"];
        $ad_type = $validated["ad_type"];
        $ad_tag = $validated["ad_tag"];

        $hasAdvPosition = AdvertisingPosition::where("position_name", $position_name)
                                             ->where("position_type_id", $position_type_id)
                                             ->first();
        if ($hasAdvPosition) {

            throw new AdvertisingPositionException(AdvertisingPositionException::HAS_EXISTS);
        }
        $AdvertisingPosition = new AdvertisingPosition();
        $AdvertisingPosition->position_name = $position_name;
        $AdvertisingPosition->key = $this->get6RandomStr(6);
        $AdvertisingPosition->position_type_id = $position_type_id;
        $AdvertisingPosition->type = $type;
        $AdvertisingPosition->ad_type = $ad_type;
        $AdvertisingPosition->has_pushed = 0;
        $AdvertisingPosition->image_num = 0;
        $AdvertisingPosition->image_width = 0;
        $AdvertisingPosition->image_height = 0;
        $AdvertisingPosition->ad_tag = $ad_tag;

        $saveRes = $AdvertisingPosition->save();
        if ($saveRes) {
            return Respond::respondWithData("operation success", AdvertisingPositionException::OPERATION_SUCCESS);
        } else {

            throw new AdvertisingPositionException(AdvertisingPositionException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, $id) {
        PermissionHook::can("广告位.删除", $id);
        $advertisingPosition = AdvertisingPosition::find($id);
        if (!$advertisingPosition) {
            throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
        }

        $advertisingInfo = Advertising::where("advertising_position_id", $id)
                                      ->first();//如果广告位下有广告，就不能删除广告位
        if ($advertisingInfo) {
            throw new AdvertisingPositionException(AdvertisingPositionException::CAN_NOT_OPERATION);
        }

        $deleteRes = $advertisingPosition->delete();
        if ($deleteRes) {
            \Kanchangzhou\Kernel\Hooks\HomePageHook::deleteAllHomeItem($advertisingPosition);

            return Respond::respondWithData("operation success", AdvertisingPositionException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingPositionException(AdvertisingPositionException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(StoreEditAdvertisingPositionPost $request, $id) {
        PermissionHook::can("广告位.编辑", $id);
        $validated = $request->validated();

        $advertisingPosition = AdvertisingPosition::find($id);
        if (!$advertisingPosition) {
            throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
        }

        $position_name = $validated["position_name"];
        $position_type_id = $validated["position_type_id"];
        $type = $validated["type"];
        $ad_type = $validated["ad_type"];
        $ad_tag = $validated["ad_tag"];

        $hasAdvPosition = AdvertisingPosition::where("position_name", $position_name)
                                             ->where("position_type_id", $position_type_id)
                                             ->first();
        if ($hasAdvPosition) {
            if ($hasAdvPosition->id != $id) {
                throw new AdvertisingPositionException(AdvertisingPositionException::HAS_EXISTS);
            }
        }

        $advertisingPosition->position_name = $position_name;
        $advertisingPosition->position_type_id = $position_type_id;
        $advertisingPosition->type = $type;
        $advertisingPosition->ad_type = $ad_type;
        $advertisingPosition->has_pushed = 0;
        $advertisingPosition->image_num = 0;
        $advertisingPosition->image_width = 0;
        $advertisingPosition->image_height = 0;
        $advertisingPosition->ad_tag = $ad_tag;

        $editRes = $advertisingPosition->save();

        if ($editRes) {

            \Kanchangzhou\Kernel\Hooks\HomePageHook::syncHomeItem($advertisingPosition);

            return Respond::respondWithData("operation success", AdvertisingPositionException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingPositionException(AdvertisingPositionException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $pagesize
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request) {
        PermissionHook::can("广告位.列表");
        $listRes = AdvertisingPosition::orderBy("id", "desc")
                                      ->paginate(10);

        return Respond::respondWithData(AdvertisingPositionResource::collection($listRes), AdvertisingPositionException::OPERATION_SUCCESS);
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail(Request $request, $id) {
        PermissionHook::can("广告位.详情", $id);
        $advertisingPosition = AdvertisingPosition::find($id);
        if (!$advertisingPosition) {
            throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
        }

        return Respond::respondWithData(AdvertisingPositionResource::make($advertisingPosition), AdvertisingPositionException::OPERATION_SUCCESS);
    }


    /**
     * @param Request $request
     */
//    public function adPosition(Request $request){
//        return Respond::respondWithData(AdvertisingPositionResource::collection(AdvertisingPosition::where("position_type_id", 1)->get()), AdvertisingPositionException::OPERATION_SUCCESS);
//    }


    /**
     * 推送操作.
     *
     */
    public function pushToHome(Request $request, $id) {
        PermissionHook::can("广告位.推至信息流", $id);
        // 可以通过request传递id或者其他key, 也可以通过自动载入Model.
        // 获取你的推送数据模型, 并判断是否可以推送到首页. 比如状态是否正常等
        $advertisingPositionModel = AdvertisingPosition::find($id);
        try {
            $this->validate($request, [
                'home_column_id' => 'required',
            ], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }
        // 从前台获取数据 home_column_id 和 home_category_id, 这部分前端已做, 按固定参数获取, 前端可以使用统一组件
        // \Kanchangzhou\Auth\Facades\AuthFacade::adminUser(); 获取管理员, 若无需要更新到最新版本

        $pushRes = \Kanchangzhou\Kernel\Hooks\HomePageHook::pushToHome(AuthFacade::adminUser(), $advertisingPositionModel, $request->input('home_column_id'), $request->input('home_category_id'));

        $has_push = $advertisingPositionModel->has_pushed;
        if ($has_push) {
            $has_push[] = $pushRes;
        } else {
            $has_push = [$pushRes];
        }
        $advertisingPositionModel->has_pushed = $has_push;
        $advertisingPositionModel->save();

        return Respond::respondWithData("operation success", AdvertisingPositionException::OPERATION_SUCCESS);
    }

}
