<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Aliyun\Config;

use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsConfigInterface;

class TaskConfig implements AiTtsConfigInterface
{
    private $speechRate;
    private $pitchRate;
    private $volume;
    private $voice;
    private $format;
    private $enableNotify = false;
    private $notifyUrl;

    public function __construct($config = []) {
        $config += [
            'voice' => 'xiaoyun',
            'format' => 'mp3',
            'speech_rate' => 0,
            'pitch_rate' => 0,
            'volume' => 50,
        ];

        $this->voice = $config['voice'];
        $this->format = $config['format'];
        $this->speechRate = $config['speech_rate'];
        $this->pitchRate = $config['pitch_rate'];
        $this->volume = $config['volume'];

        if (!empty($config['notify_url'])) {
            $this->enableNotify = true;
            $this->notifyUrl = $config['notify_url'];
        }
    }

    public function toArray() {
        return [
            'voice' => $this->voice,
            'format' => $this->format,
            'speech_rate' => $this->speechRate,
            'pitch_rate' => $this->pitchRate,
            'volume' => $this->volume,
            'enable_notify' => $this->enableNotify,
            'notify_url' => $this->notifyUrl,
        ];
    }

    public function toJson() {
        return json_encode($this->toArray());
    }
}
