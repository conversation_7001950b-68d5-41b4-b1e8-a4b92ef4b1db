<?php

namespace Changgua<PERSON>\LaravelSDK;

use Changguan\LaravelSDK\Supports\Config;
use Illuminate\Support\ServiceProvider;

class ChangguanServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 合并配置文件
        $this->mergeConfigFrom(
            __DIR__.'/../config/changguan.php', 'changguan'
        );

        // 注册配置类
        $this->app->singleton('changguan.config', function ($app) {
            return new Config($app['config']['changguan']);
        });

        // 配置类别名
        $this->app->alias('changguan.config', Config::class);

        // 注册主应用类
        $this->app->singleton('changguan', function ($app) {
            return new Application($app['changguan.config']);
        });

        // 注册别名
        $this->app->alias('changguan', Application::class);
    }

    /**
     * 引导服务
     *
     * @return void
     */
    public function boot()
    {
        // 发布配置文件
        $this->publishes([
            __DIR__.'/../config/changguan.php' => config_path('changguan.php'),
        ], 'changguan-config');
    }

    /**
     * 获取服务提供者提供的服务
     *
     * @return array
     */
    public function provides()
    {
        return ['changguan', 'changguan.config', Application::class, Config::class];
    }
}
