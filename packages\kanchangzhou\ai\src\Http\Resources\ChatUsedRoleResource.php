<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ChatUsedRoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request) {
        return [
            'id' => $this->role->role_uuid,
            'title' => $this->role->title,
            'options' => $this->role->options,
            'examples' => $this->role->examples,
            'icon' => $this->role->icon,
            'description' => $this->role->description,
            'module_key' => $this->role->module_key,
            'is_published' => $this->role->is_published,
        ];
    }
}
