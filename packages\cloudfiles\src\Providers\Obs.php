<?php

namespace Rjustice\CloudFiles\Providers;

use Obs\ObsClient;
use Obs\ObsException;

class Obs extends AbstractCloudFiles
{
    protected $accessKey;
    protected $secretKey;
    protected $bucket;
    protected $endpoint;
    protected $internalEndpoint;
    protected $cdnDomain;
    protected $ssl;
    protected $isCname;
    protected $acl;
    protected $debug;
    protected $signature;

    /**
     * @var ObsClient
     */
    protected $client;

    public function config($config) {
        $this->accessKey = $config['access_key'];
        $this->secretKey = $config['secret_key'];
        $this->bucket = $config['bucket'];
        $this->endpoint = $config['endpoint'];
        $this->cdnDomain = $config['cdnDomain'];
        $this->ssl = $config['ssl'];
        $this->isCname = $config['isCname'];
        $this->acl = $config['acl'];
        $this->debug = $config['debug'];
        $this->signature = $config['signature'] ?? ObsClient::SigantureObs;

        $this->client = new ObsClient([
            'key' => $this->accessKey,
            'secret' => $this->secretKey,
            'endpoint' => $this->endpoint,
        ]);

        return $this;
    }

    public function getPath($file) {
        // TODO: Implement getPath() method.
    }

    public function signatureUrl($file) {
        // TODO: Implement signatureUrl() method.
    }

    public function getUrl($path) {
        // TODO: Implement getUrl() method.
    }

    public function policy($callbaclRoute = '', $dir = '', $expire = 300) {
        try {
            $res = $this->client->createPostSignature([
                'Bucket' => $this->bucket,
            ]);
        } catch (ObsException $e) {
            return false;
        }

        $response = [];

        $response['data']['AccessKeyId'] = $this->accessKey;
        $response['data']['policy'] = $res['Policy'];
        $response['data']['signature'] = $res['Signature'];
        $response['data']['Key'] = $dir;
        $response['data']['success_action_status'] = 200;

        $response['expire'] = $expire;
        $response['expire_at'] = date('Y-m-d H:i:s', time() + $expire);

        return $response;
    }

    public function verify() {
        // TODO: Implement verify() method.
    }

    public function write($path, $contents, $config) {
        // TODO: Implement write() method.
    }

    public function writeStream($path, $resource, $config) {
        // TODO: Implement writeStream() method.
    }

    public function update($path, $contents, $config) {
        // TODO: Implement update() method.
    }

    public function updateStream($path, $resource, $config) {
        // TODO: Implement updateStream() method.
    }

    public function rename($path, $newPath) {
        // TODO: Implement rename() method.
    }

    public function copy($path, $newPath) {
        // TODO: Implement copy() method.
    }

    public function delete($path) {
        // TODO: Implement delete() method.
    }

    public function deleteDir($dirname) {
        // TODO: Implement deleteDir() method.
    }

    public function createDir($dirname) {
        // TODO: Implement createDir() method.
    }

    public function setVisibility($path, $visibility) {
        // TODO: Implement setVisibility() method.
    }

    public function getVisibility($path) {
        // TODO: Implement getVisibility() method.
    }

    public function has($path) {
        // TODO: Implement has() method.
    }

    public function read($path) {
        // TODO: Implement read() method.
    }

    public function readStream($path) {
        // TODO: Implement readStream() method.
    }

    public function listContents($dirname) {
        // TODO: Implement listContents() method.
    }
}
