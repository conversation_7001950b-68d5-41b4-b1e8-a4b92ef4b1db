<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Models\MediaAiFile;
use Kanchangzhou\AI\Models\MediaAiKeyword;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiNlpKeywordsJob extends MediaAiBaseJob
{

    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $taskType = $this->data['data']['subDataTypes'][0]['type'];
        $guid = $this->data['data']['guid'];
        $keywords = $this->data['data']['nlp/keywords'][0]['contents'];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();
        if (!$task) {
            return;
        }
        MediaAiKeyword::where('media_ai_file_id', $task->media_ai_file_id)
                      ->delete();

        foreach (array_chunk($keywords, 10) as $chunk) {
            $tmp = array_map(function ($keyword) use ($task) {
                return [
                    'media_ai_file_id' => $task->media_ai_file_id,
                    'keyword' => $keyword['keyword'],
                    'probability' => $keyword['probability'],
                ];
            }, $chunk);

            MediaAiKeyword::insert($tmp);
        }

        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
