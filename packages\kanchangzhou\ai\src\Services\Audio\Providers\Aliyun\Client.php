<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Aliyun;

use Illuminate\Http\File;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsClient;
use Kanchangzhou\AI\Services\Audio\Handlers\AiAudioWsHandler;
use Kanchangzhou\AI\Services\Audio\Providers\Aliyun\Config\StreamConfig;
use Kanchangzhou\AI\Services\Audio\Providers\Aliyun\Config\TaskConfig;

class Client extends AiTtsClient
{
    private $auth;
    public $appKey;

    const TTS_TASK_URL = 'https://nls-gateway.cn-shanghai.aliyuncs.com/rest/v1/tts/async';
    const TTS_STREAM_REQUEST_URL = 'https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts';
    const TTS_WSS_REQUEST_URL = 'wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1';

    const ASR_TASK_URL = 'https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/asr';

    public function __construct($appKey, $accessKeyId, $accessKeySecret) {
        parent::__construct();
        $this->auth = new Auth($accessKeyId, $accessKeySecret);
        $this->appKey = $appKey;
    }


    public function textToSpeechStream(string $text) {
        $body = (new StreamConfig($this->getRequestConfig()))->toArray();

        $body += [
            'appkey' => $this->appKey,
            'token' => $this->auth->getAccessToken(),
            'text' => $text,
        ];

        return Http::asJson()
                   ->post(self::TTS_STREAM_REQUEST_URL, $body);
    }

    public function createTask(string $text) {
        $config = (new TaskConfig($this->getRequestConfig()))->toArray();

        $config += [
            'text' => $text,
        ];

        $body = [
            'payload' => [
                'tts_request' => $config,
            ],
            'header' => [
                'appkey' => $this->appKey,
                'token' => $this->auth->getAccessToken(),
            ],
        ];

        $res = Http::asJson()
                   ->post(self::TTS_TASK_URL, $body);

        return $res->json();
    }

    public function fetchTaskResult($taskId) {
        $options = [
            'appkey' => $this->appKey,
            'task_id' => $taskId,
            'token' => $this->auth->getAccessToken(),
        ];

        $res = Http::asJson()
                   ->get(self::TTS_TASK_URL, $options);

        return $res->json();
    }

    public function asrToText($audioFileContent) {
        $res = Http::attach('audio', $audioFileContent)
                   ->withHeaders([

                       'X-NLS-Token' => $this->auth->getAccessToken(),
                       'Content-type' => 'application/octet-stream',
                   ])
                   ->post(self::ASR_TASK_URL . '?appkey=' . $this->appKey);

        return $res;
    }
}
