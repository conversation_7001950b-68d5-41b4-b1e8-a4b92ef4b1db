<?php

namespace <PERSON><PERSON><PERSON>\SDK\Http;

use Changguan\SDK\Constants\HeaderConstants;

class Tools
{
    private $headers = [];
    private $serverVars = [];

    public function __construct(array $headers = [], array $serverVars = []) {
        $this->headers = $headers;
        $this->serverVars = $serverVars;
    }

    /**
     * 获取客户端真实IP地址
     *
     * @return string
     */
    public function getClientIp(): string {
        $ipAddresses = [];

        if ($xForwardedFor = $this->getHeader('X-Forwarded-For')) {
            $ipAddresses = array_map('trim', explode(',', $xForwardedFor));
        }

        if (empty($ipAddresses)) {
            if ($xRealIp = $this->getHeader('X-Real-IP')) {
                $ipAddresses[] = $xRealIp;
            } elseif (!empty($this->serverVars['REMOTE_ADDR'])) {
                $ipAddresses[] = $this->serverVars['REMOTE_ADDR'];
            }
        }

        return $ipAddresses[0] ?? '0.0.0.0';
    }

    /**
     * 获取设备ID
     *
     * @return string|null
     */
    public function getDeviceId(): ?string {
        return $this->getHeader(HeaderConstants::DEVICE_ID);
    }

    /**
     * 获取操作系统
     *
     * @return string|null
     */
    public function getOS(): ?string {
        return $this->getHeader(HeaderConstants::DEVICE_OS);
    }

    /**
     * 获取操作系统版本
     *
     * @return string|null
     */
    public function getOSVersion(): ?string {
        return $this->getHeader(HeaderConstants::DEVICE_OS_VERSION);
    }

    /**
     * 获取平台信息 (ios/android/web)
     *
     * @return string|null
     */
    public function getPlatform(): ?string {
        return $this->getHeader(HeaderConstants::CLIENT_TYPE);
    }

    /**
     * 获取APP版本号
     *
     * @return string|null
     */
    public function getAppVersion(): ?string {
        return $this->getHeader(HeaderConstants::CLIENT_VERSION);
    }

    /**
     * 获取构建版本号
     *
     * @return string|null
     */
    public function getBuildVersion(): ?string {
        return $this->getHeader(HeaderConstants::CLIENT_BUILD);
    }

    /**
     * 获取网络类型
     *
     * @return string|null
     */
    public function getNetworkType(): ?string {
        return $this->getHeader(HeaderConstants::NETWORK_TYPE);
    }

    /**
     * 获取设备型号
     *
     * @return string|null
     */
    public function getDeviceModel(): ?string {
        return $this->getHeader(HeaderConstants::DEVICE_MODEL);
    }

    /**
     * 获取用户ID
     *
     * @return string|null
     */
    public function getUserId(): ?string {
        return $this->getHeader(HeaderConstants::USER_ID);
    }

    /**
     * 获取请求ID
     *
     * @return string
     */
    public function getRequestId(): string {
        return $this->getHeader(HeaderConstants::REQUEST_ID);
    }

    /**
     * 获取时区信息
     *
     * @return string|null
     */
    public function getTimezone(): ?string {
        return $this->getHeader(HeaderConstants::TIMEZONE);
    }

    /**
     * 获取语言设置
     *
     * @return string|null
     */
    public function getLanguage(): ?string {
        return $this->getHeader(HeaderConstants::LANGUAGE);
    }

    /**
     * 获取地区设置
     *
     * @return string|null
     */
    public function getRegion(): ?string {
        return $this->getHeader(HeaderConstants::REGION);
    }

    /**
     * 获取渠道信息
     *
     * @return string|null
     */
    public function getChannel(): ?string {
        return $this->getHeader(HeaderConstants::CHANNEL);
    }

    /**
     * 获取时间戳
     *
     * @return string
     */
    public function getTimestamp(): string {
        return $this->getHeader(HeaderConstants::TIMESTAMP);
    }

    /**
     * 获取API版本号
     *
     * @return string
     */
    public function getApiVersion(): string {
        return $this->getHeader(HeaderConstants::API_VERSION, '');
    }

    /**
     * 获取App ID
     *
     * @return string
     */
    public function getAppId(): string {
        return $this->getHeader(HeaderConstants::APP_ID, '');
    }

    /**
     * 获取随机字符串
     *
     * @return string
     */
    public function getNonce(): string {
        return $this->getHeader(HeaderConstants::NONCE);
    }

    /**
     * 获取签名
     *
     * @return string
     */
    public function getSignature(): string {
        return $this->getHeader(HeaderConstants::SIGNATURE);
    }

    /**
     * 获取客户端ID
     *
     * @return string
     */
    public function getClientId(): string {
        return $this->getHeader(HeaderConstants::CLIENT_ID);
    }

    /**
     * 对比APP版本号
     */
    public function compareAppVersion(string $version): bool {
        $cleanVersion = ltrim($version, 'v');
        $currentVersion = ltrim($this->getAppVersion(), 'v');

        return version_compare($currentVersion, $cleanVersion, '>=');
    }

    /**
     * 获取所有设备相关信息
     *
     * @return array
     */
    public function getDeviceInfo(): array {
        return [
            'ip'            => $this->getClientIp(),
            'device_id'     => $this->getDeviceId(),
            'device_model'  => $this->getDeviceModel(),
            'os'            => $this->getOS(),
            'os_version'    => $this->getOSVersion(),
            'platform'      => $this->getPlatform(),
            'app_version'   => $this->getAppVersion(),
            'build_version' => $this->getBuildVersion(),
            'network_type'  => $this->getNetworkType(),
            'user_agent'    => $this->getUserAgent(),
            'language'      => $this->getLanguage(),
            'region'        => $this->getRegion(),
            'timezone'      => $this->getTimezone(),
            'channel'       => $this->getChannel(),
            'request_id'    => $this->getRequestId(),
        ];
    }

    /**
     * 获取指定header参数
     *
     * @param string $key
     * @param mixed $default
     *
     * @return mixed
     */
    public function getHeader(string $key, $default = null) {
        // 将输入的 key 标准化为小写
        $lowerKey = strtolower($key);
        
        // 检查实例变量中是否有值（不区分大小写）
        foreach ($this->headers as $headerKey => $value) {
            if (strtolower($headerKey) === $lowerKey) {
                return is_array($value) ? $value[0] : $value;
            }
        }

        // 检查服务器变量
        $serverKey = 'HTTP_' . strtoupper(str_replace('-', '_', $key));
        
        // 检查 serverVars（不区分大小写）
        foreach ($this->serverVars as $serverVarKey => $value) {
            if (strtolower($serverVarKey) === strtolower($serverKey)) {
                return is_array($value) ? $value[0] : $value;
            }
        }

        // 如果实例变量中没有，尝试从全局变量获取
        if (empty($this->headers) && empty($this->serverVars)) {
            // 尝试从全局 getallheaders() 获取
            if (function_exists('getallheaders')) {
                $allHeaders = getallheaders();
                foreach ($allHeaders as $headerKey => $value) {
                    if (strtolower($headerKey) === $lowerKey) {
                        return is_array($value) ? $value[0] : $value;
                    }
                }
            }
            
            // 尝试从 $_SERVER 获取标准化的 HTTP 头
            if (isset($_SERVER[$serverKey])) {
                return is_array($_SERVER[$serverKey]) ? $_SERVER[$serverKey][0] : $_SERVER[$serverKey];
            }
        }

        return $default;
    }

    /**
     * 获取User-Agent
     *
     * @return string|null
     */
    public function getUserAgent(): ?string {
        return $this->getHeader('User-Agent');
    }

    /**
     * 检查是否是移动设备
     *
     * @return bool
     */
    public function isMobile(): bool {
        $userAgent = $this->getUserAgent();

        return (bool)preg_match('/(android|iphone|harmonyos|ipad|mobile)/i', $userAgent);
    }

    /**
     * 检查是否是 iOS 设备
     *
     * @return bool
     */
    public function isIOS(): bool {
        return strtolower($this->getOS() ?? '') === HeaderConstants::OS_IOS || $this->isIpadOS();
    }

    /**
     * 检查是否是 iPadOS 设备
     *
     * @return bool
     */
    public function isIpadOS(): bool {
        return strtolower($this->getOS() ?? '') === HeaderConstants::OS_IPADOS;
    }

    /**
     * 检查是否是 Android 设备
     *
     * @return bool
     */
    public function isAndroid(): bool {
        return strtolower($this->getOS() ?? '') === HeaderConstants::OS_ANDROID;
    }

    /**
     * 检查是否是 HarmonyOS 设备
     *
     * @return bool
     */
    public function isHarmonyOS(): bool {
        return strtolower($this->getOS() ?? '') === HeaderConstants::OS_HARMONYOS;
    }

    /**
     * 检查是否是 Web 平台
     *
     * @return bool
     */
    public function isWeb(): bool {
        return strtolower($this->getPlatform() ?? '') === HeaderConstants::OS_WEB || $this->isPC();
    }

    /**
     * 检查是否是 PC 平台
     * @return bool
     */
    public function isPC(): bool {
        return strtolower($this->getPlatform() ?? '') === HeaderConstants::OS_PC;
    }

    /**
     * 检查是否是 wechat 平台
     */
    public function isWechat(): bool {
        return strtolower($this->getPlatform() ?? '') === HeaderConstants::OS_WECHAT;
    }

    /**
     * 获取设备类型
     *
     * @return string
     */
    public function getDeviceType(): string {
        if ($this->isIOS()) {
            return HeaderConstants::OS_IOS;
        }

        //        if ($this->isIpadOS()) {
        //            return HeaderConstants::OS_IPADOS;
        //        }

        if ($this->isAndroid()) {
            return HeaderConstants::OS_ANDROID;
        }

        if ($this->isHarmonyOS()) {
            return HeaderConstants::OS_HARMONYOS;
        }

        if ($this->isWeb()) {
            return HeaderConstants::OS_WEB;
        }

        //        if ($this->isPC()) {
        //            return HeaderConstants::OS_PC;
        //        }

        if ($this->isWechat()) {
            return HeaderConstants::OS_WECHAT;
        }

        return 'unknown';
    }


    /**
     * @param $fieldName
     * @param $dateTime
     *
     * @return array
     */
    /*public function mergeDateTimeFormat($fieldName, $dateTime) {
        if (!$dateTime instanceof \DateTime) {
            if (!strtotime($dateTime)) {
                $dateTime = Carbon::createFromTimestamp($dateTime);
            } else {
                $dateTime = Carbon::create($dateTime);
            }
        }

        if ($dateTime->timestamp) {
            return [
                $fieldName . '_timestamp' => $dateTime->timestamp,
                $fieldName . '_gmt'       => $dateTime->toISOString(),
                $fieldName . '_human'     => $dateTime->diffForHumans(),
                $fieldName                => $dateTime->toDateTimeString(),
            ];
        } else {
            return [
                $fieldName . '_timestamp' => 0,
                $fieldName . '_gmt'       => '',
                $fieldName . '_human'     => '',
                $fieldName                => '',
            ];
        }
    }*/

    /**
     * 设置 headers 和 serverVars
     *
     * @param array $headers
     * @param array $serverVars
     * @return $this
     */
    public function setHeadersAndServerVars(array $headers, array $serverVars = []): self {
        $this->headers = $headers;
        $this->serverVars = $serverVars;
        return $this;
    }
}
