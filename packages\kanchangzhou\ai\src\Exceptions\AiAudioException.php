<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class AiAudioException extends BaseException
{
    const INVALID_CONFIG = 304001;
    const MISS_PARAM = 304002;
    const MISS_INTERFACE = 304003;
    // 内容不合规
    const HAS_SENSITIVE = 304004;

    const FAILED_REQUEST = 304005;

    const FAILED_ASR = 304006;
    const FAILED_CALLBACK = 304007;

    const TASK_FAILED = 304008;

    const TASK_SUCCESS = 304009;

    public static function message($code) {
        $msgArr = [
            static::INVALID_CONFIG => '无效的配置',
            static::MISS_PARAM => '缺少参数',
            static::MISS_INTERFACE => '缺少服务提供者',
            static::HAS_SENSITIVE => '根据相关法律法规和政策，无法为您提供服务',
            static::FAILED_REQUEST => '接口请求错误',
            static::FAILED_ASR => '语音识别失败',
            static::FAILED_CALLBACK => '服务器请求失败，请稍后再试',
            static::TASK_FAILED => '合成失败，请稍后再试',
            static::TASK_SUCCESS => '合成已结束',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
