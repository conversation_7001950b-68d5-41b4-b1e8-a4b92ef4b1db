<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kanchangzhou\AI\Services\Chat\Providers\AliBailianBase;
use Kanchangzhou\AI\Services\Chat\Providers\AliBase;
use Kanchangzhou\AI\Services\Chat\Providers\AliLongBase;
use Kanchangzhou\AI\Services\Chat\Providers\BaiduBase;
use Kanchangzhou\AI\Services\Chat\Providers\BaiduKnowledgeBase;
use Kanchangzhou\AI\Services\Chat\Providers\BaiduQianfan;
use Kanchangzhou\AI\Services\Chat\Providers\KnowledgeOne;
use Kanchangzhou\AI\Services\Chat\Providers\OpenAiBase;

/**
 * Kanchangzhou\AI\Models\ChatModel
 *
 * @property int $id
 * @property string $model_uuid
 * @property string $provider
 * @property string $title
 * @property string|null $icon 图标
 * @property string $model_id
 * @property string|null $model_type
 * @property string|null $can_files 是否支持文件上传
 * @property float|null $temperature 温度
 * @property float|null $penalty top_p
 * @property int|null $max_tokens 最大token数
 * @property int|null $enable_context 是否启用上下文
 * @property int|null $context_round 上下文轮数
 * @property int|null $chat_used 是否启用聊天
 * @property int $status 状态 1:正常 2:禁用
 * @property int $is_default 是否默认模型
 * @property int|null $sort 排序
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $api_gateway api网关
 * @property-read mixed $chat_used_str
 * @property-read mixed $enable_context_str
 * @property-read mixed $provider_str
 * @property-read mixed $status_str
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereApiGateway($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereCanFiles($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereChatUsed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereContextRound($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereEnableContext($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereMaxTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereModelUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel wherePenalty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereTemperature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatModel withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperChatModel
 */
class ChatModel extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [
        'enable_context_str',
        'chat_used_str',
        'provider_str',
        'status_str',
    ];

    const ENABLE_CONTEXT_NO = 1;
    const ENABLE_CONTEXT_YES = 2;

    const ENABLE_CONTEXT_MAP = [
        self::ENABLE_CONTEXT_NO  => '否',
        self::ENABLE_CONTEXT_YES => '是',
    ];

    const CHAT_USED_NO = 1;
    const CHAT_USED_YES = 2;

    const CHAT_USED_MAP = [
        self::CHAT_USED_NO  => '否',
        self::CHAT_USED_YES => '是',
    ];

    const PROVIDER_ALI = 'aliyun';
    const PROVIDER_OPENAI = 'openai';
    const PROVIDER_BAIDU = 'baidu';
    const PROVIDER_KNOWLEDGE = 'knowledge';
    const PROVIDER_BAIDU_QIANFAN = 'baidu_qianfan';
    const PROVIDER_ALI_BAILIAN = 'ali_bailian';
    const PROVIDER_ALI_LONG = 'aliyun_long';

    const PROVIDER_MAP = [
        self::PROVIDER_ALI           => '阿里云',
        self::PROVIDER_OPENAI        => 'OpenAI',
        self::PROVIDER_BAIDU         => '百度云',
        self::PROVIDER_KNOWLEDGE     => '百度知识库',
        self::PROVIDER_BAIDU_QIANFAN => '百度千帆',
        self::PROVIDER_ALI_BAILIAN   => '阿里百炼',
        self::PROVIDER_ALI_LONG      => '阿里云长文本',
    ];

    const STATUS_INVALID = 1;
    const STATUS_VALID = 2;

    const STATUS_MAP = [
        self::STATUS_INVALID => '无效',
        self::STATUS_VALID   => '有效',
    ];

    const TEMPERATURE_RANGE = [
        self::PROVIDER_ALI           => [
            'min' => 0.1,
            'max' => 2.0,
        ],
        self::PROVIDER_OPENAI        => [
            'min' => 0.1,
            'max' => 2.0,
        ],
        self::PROVIDER_BAIDU         => [
            'min' => 0.1,
            'max' => 1.0,
        ],
        self::PROVIDER_BAIDU_QIANFAN => [
            'min' => 0.1,
            'max' => 1.0,
        ],
        self::PROVIDER_KNOWLEDGE     => [
            'min' => 0.1,
            'max' => 1.0,
        ],
        self::PROVIDER_ALI_LONG      => [
            'min' => 0.1,
            'max' => 2.0,
        ],
        self::PROVIDER_ALI_BAILIAN   => [
            'min' => 0.1,
            'max' => 2.0,
        ],
    ];

    const PENALTY_RANGE = [
        self::PROVIDER_ALI           => [
            'min' => 1,
            'max' => 2,
        ],
        self::PROVIDER_ALI_LONG      => [
            'min' => 1,
            'max' => 2,
        ],
        self::PROVIDER_ALI_BAILIAN   => [
            'min' => 1,
            'max' => 2,
        ],
        self::PROVIDER_OPENAI        => [
            'min' => 1,
            'max' => 2,
        ],
        self::PROVIDER_BAIDU         => [
            'min' => 1,
            'max' => 2,
        ],
        self::PROVIDER_BAIDU_QIANFAN => [
            'min' => 1,
            'max' => 2,
        ],
        self::PROVIDER_KNOWLEDGE     => [
            'min' => 1,
            'max' => 2,
        ],
    ];

    const IS_DEFAULT_NO = 1;
    const IS_DEFAULT_YES = 2;

    const IS_DEFAULT_MAP = [
        self::IS_DEFAULT_NO  => '否',
        self::IS_DEFAULT_YES => '是',
    ];

    const CAN_FILES_NO = 1;
    const CAN_FILES_YES = 2;

    const CAN_FILES_MAP = [
        self::CAN_FILES_NO  => '否',
        self::CAN_FILES_YES => '是',
    ];

    const SERVICE_PROVIDERS = [
        self::PROVIDER_ALI           => AliBase::class,
        self::PROVIDER_ALI_BAILIAN   => AliBailianBase::class,
        self::PROVIDER_OPENAI        => OpenAiBase::class,
        self::PROVIDER_BAIDU         => BaiduBase::class,
        self::PROVIDER_KNOWLEDGE     => BaiduKnowledgeBase::class,
        self::PROVIDER_ALI_LONG      => AliLongBase::class,
        self::PROVIDER_BAIDU_QIANFAN => BaiduQianfan::class,
    ];

    const MODEL_TYPE_CHAT = 'chat';
    const MODEL_TYPE_IMAGE = 'image';
    const MODEL_TYPE_VIDEO = 'video';
    const MODEL_TYPE_AUDIO = 'audio';

    const MODEL_TYPE_MAP = [
        self::MODEL_TYPE_CHAT  => '聊天',
        self::MODEL_TYPE_IMAGE => '图片',
        self::MODEL_TYPE_VIDEO => '视频',
        self::MODEL_TYPE_AUDIO => '音频',
    ];

    public function getStatusStrAttribute() {
        return self::STATUS_MAP[$this->status] ?? '';
    }

    public function getProviderStrAttribute() {
        return self::PROVIDER_MAP[$this->provider] ?? '';
    }

    public function getEnableContextStrAttribute() {
        return self::ENABLE_CONTEXT_MAP[$this->enable_context] ?? '';
    }

    public function getChatUsedStrAttribute() {
        return self::CHAT_USED_MAP[$this->chat_used] ?? '';
    }
}
