<?php

namespace Changgua<PERSON>\SDK\Exceptions;

/**
 * HTTP请求异常类
 * 用于处理HTTP请求过程中的异常
 */
class HttpException extends \Exception
{
    /**
     * 请求执行失败
     */
    public const ERROR_REQUEST_FAILED = 5001;

    /**
     * 响应解析失败
     */
    public const ERROR_RESPONSE_PARSE = 5002;

    /**
     * API返回错误
     */
    public const ERROR_API_ERROR = 5003;

    /**
     * 网络错误
     */
    public const ERROR_NETWORK = 5004;

    /**
     * 配置错误
     */
    public const ERROR_CONFIG = 5005;
}
