<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiOrder
 *
 * @property int $id
 * @property string $order_no 订单号
 * @property int $user_id
 * @property string $guard_name
 * @property string $total 总价
 * @property int $status 状态 1:待支付 2:已支付 3:已取消
 * @property string $order_type 订单类型
 * @property string|null $payment_no
 * @property string|null $payment_method 支付方式
 * @property \Illuminate\Support\Carbon|null $paid_at 支付时间
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property \Illuminate\Support\Carbon|null $closed_at
 * @property \Illuminate\Support\Carbon|null $refunded_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $order_type_str
 * @property-read mixed $payment_method_str
 * @property-read mixed $status_str
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\AiOrderItem> $items
 * @property-read int|null $items_count
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereClosedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereOrderNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereOrderType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder wherePaymentNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereRefundedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiOrder withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiOrder
 */
class AiOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'paid_at' => 'datetime',
        'canceled_at' => 'datetime',
        'closed_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    protected $appends = [
        'status_str',
        'payment_method_str',
        'order_type_str',
    ];

    const PAYMENT_METHOD_PACKAGE = 'package';
    const PAYMENT_METHOD_WECHAT = 'wechat';
    const PAYMENT_METHOD_ALIPAY = 'alipay';
    const PAYMENT_METHOD_SYSTEM = 'system';

    const PAYMENT_METHOD_MAP = [
        self::PAYMENT_METHOD_PACKAGE => '资源包',
        self::PAYMENT_METHOD_WECHAT => '微信',
        self::PAYMENT_METHOD_ALIPAY => '支付宝',
        self::PAYMENT_METHOD_SYSTEM => '系统赠送',
    ];

    const STATUS_PENDING = 1;
    const STATUS_PAID = 2;
    const STATUS_CANCELLED = 3;

    const STATUS_MAP = [
        self::STATUS_PENDING => '待支付',
        self::STATUS_PAID => '已支付',
        self::STATUS_CANCELLED => '已取消',
    ];

    const ORDER_TYPE_CHAT = ChatModel::MODEL_TYPE_CHAT;
    const ORDER_TYPE_IMAGE = ChatModel::MODEL_TYPE_IMAGE;
    const ORDER_TYPE_VIDEO = ChatModel::MODEL_TYPE_VIDEO;
    const ORDER_TYPE_AUDIO = ChatModel::MODEL_TYPE_AUDIO;
    const ORDER_TYPE_CHAT_PACKAGE = ChatModel::MODEL_TYPE_CHAT . '_package';
    const ORDER_TYPE_IMAGE_PACKAGE = ChatModel::MODEL_TYPE_IMAGE . '_package';
    const ORDER_TYPE_VIDEO_PACKAGE = ChatModel::MODEL_TYPE_VIDEO . '_package';
    const ORDER_TYPE_AUDIO_PACKAGE = ChatModel::MODEL_TYPE_AUDIO . '_package';

    const ORDER_TYPE_MAP = [
        self::ORDER_TYPE_CHAT => '对话',
        self::ORDER_TYPE_IMAGE => '文生图',
        self::ORDER_TYPE_VIDEO => '文生视频',
        self::ORDER_TYPE_AUDIO => '文生音频',
        self::ORDER_TYPE_CHAT_PACKAGE => '对话资源包',
        self::ORDER_TYPE_IMAGE_PACKAGE => '文生图资源包',
        self::ORDER_TYPE_VIDEO_PACKAGE => '文生视频资源包',
        self::ORDER_TYPE_AUDIO_PACKAGE => '文生音频资源包',
    ];

    public function items() {
        return $this->hasMany(AiOrderItem::class);
    }

    public function getStatusStrAttribute() {
        return self::STATUS_MAP[$this->status] ?? '';
    }

    public function getPaymentMethodStrAttribute() {
        return self::PAYMENT_METHOD_MAP[$this->payment_method] ?? '';
    }

    public function getOrderTypeStrAttribute() {
        return self::ORDER_TYPE_MAP[$this->order_type] ?? '';
    }
}
