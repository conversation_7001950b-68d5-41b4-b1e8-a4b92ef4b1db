<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Azure\Config;

use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsConfigInterface;

class TaskConfig implements AiTtsConfigInterface
{
    private $format;
    private $prompt;

    public function __construct($config = []) {

        $config += [
            'format' => 'mp3',
        ];
        $this->format = $config['format'];
        $this->prompt = $config['text'];
    }

    public function toArray() {
        return [
            'format' => $this->format,
            'prompt' => $this->prompt,
        ];
    }

    public function toJson() {
        return json_encode($this->toArray());
    }
}
