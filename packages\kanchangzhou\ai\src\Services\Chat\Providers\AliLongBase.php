<?php

namespace Kanchangzhou\AI\Services\Chat\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatResponse;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatService;

class AliLongBase extends AIChatService
{
    protected $serviceProvider = '阿里云';
    protected $uri = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
    protected $hasSystemRole = true;

    protected $fileUri = 'https://dashscope.aliyuncs.com/compatible-mode/v1/files';

    public function handler(): AIChatResponse {
        // TODO: Implement handler() method.
    }

    public function streamHandler() {
        $body = [
            'model' => $this->chatModel?->model_id ?? 'qwen-max',
            //            'input' => [
            'messages' => $this->messageWithHistory(),
            //            ],
            'stream' => true,
            "stream_options" => ["include_usage" => true],
            'parameters' => [
                'result_format' => 'message',
                'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                'temperature' => $this->chatModel?->temperature ?? 0.85,
                'repetition_penalty' => $this->chatModel?->penalty ?? 1.0,
                'enable_search' => true,
                'incremental_output' => true,
            ],
        ];

//        info('qweb-long-body', $body);

        $returnResponse = [
            'message' => '',
            'chat_uuid' => $this->chatUuid,
            'total_tokens' => 0,
            'input_tokens' => 0,
            'output_tokens' => 0,
            'source_id' => '',
        ];

        $response = Http::withHeaders([
            'X-DashScope-SSE' => 'enable',
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'application/json',
            'Accept' => 'text/event-stream',
        ])
                        ->withToken(config('kai.chat.providers.aliyun.api_key'))
                        ->withOptions([
                            'curl' => [
                                CURLOPT_WRITEFUNCTION => function ($curl, $data) use (&$returnResponse) {
                                    $buffer = $data;
                                    $buffer = str_replace('data: {', '{', $buffer);
                                    $buffer = str_replace('data: [', '[', $buffer);

                                    $buffer = str_replace("}\n\n{", '}[br]{', $buffer);
                                    $buffer = str_replace("}\n\n[", '}[br][', $buffer);

                                    $lines = explode('[br]', $buffer);

                                    $line_c = count($lines);

                                    $message = '';
                                    $finishReason = '';

                                    foreach ($lines as $line) {
                                        if (trim($line) != '[DONE]') {
                                            $lineData = json_decode(trim($line), TRUE);

                                            if (isset($lineData['error'])) {
                                                echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                                echo "event:error" . PHP_EOL;
                                                echo "data:" . json_encode([
                                                        'time' => microtime(true),
                                                        'message' => __('ai::ai.' . $lineData['error']['message']),
                                                        'code' => $lineData['error']['code'],
                                                    ]) . PHP_EOL . PHP_EOL;
                                                flush();

                                                $returnResponse['message'] .= __('ai::ai.' . $lineData['error']['message']);
                                                $returnResponse['is_error'] = true;

                                                return strlen($data);
                                            }

                                            if (!is_array($lineData) || !isset($lineData['choices']) || !isset($lineData['choices'][0]) || !isset($lineData['choices'][0]['delta']) || !isset($lineData['choices'][0]['delta']['content'])) {
//                        $finishReason = $lineData['choices'][0]['finish_reason'];
                                                $returnResponse['total_tokens'] = $lineData['usage']['total_tokens'] ?? 0;
                                                $returnResponse['input_tokens'] = $lineData['usage']['prompt_tokens'] ?? 0;
                                                $returnResponse['output_tokens'] = $lineData['usage']['completion_tokens'] ?? 0;
                                                continue;
                                            }
                                            $message .= $lineData['choices'][0]['delta']['content'];
                                            $finishReason = $lineData['choices'][0]['finish_reason'] ?? '';
                                            $returnResponse['source_id'] = $lineData['id'];
                                        }
                                    }

                                    echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                    echo "event:message" . PHP_EOL;
                                    echo "data: " . json_encode([
                                            'time' => microtime(true),
                                            'message' => $message,
                                            'finish_reason' => $finishReason,
                                        ]) . PHP_EOL . PHP_EOL;
                                    flush();

                                    $returnResponse['message'] .= $message;
//                                    $returnResponse['total_tokens'] = $lineData['usage']['total_tokens'] ?? 0;
//                                    $returnResponse['input_tokens'] = $lineData['usage']['prompt_tokens'] ?? 0;
//                                    $returnResponse['output_tokens'] = $lineData['usage']['completion_tokens'] ?? 0;
//                                    $returnResponse['source_id'] = $lineData['id'] ?? '';

                                    return strlen($data);
                                },
                            ],
                        ])
                        ->post($this->chatModel?->api_gateway ?? $this->uri, $body);

        return new AIChatResponse($returnResponse);
    }

    public function uploadFile(Request $request) {
        $res = Http::withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->asMultipart()
                   ->post($this->fileUri, [
                       [
                           'name' => 'purpose',
                           'contents' => 'file-extract',
                       ],
                       [
                           'name' => 'file',
                           'contents' => $request->file('file')
                                                 ->getContent(),
                           'filename' => $request->file('file')
                                                 ->getClientOriginalName(),
                       ],
                   ]);

        return $res->json();
    }

    protected function messageWithFiles($files = []) {
        $prefix = "fileid://";

        $filesWithPrefix = array_map(function ($file) use ($prefix) {
            return $prefix . ($file['id'] ?? $file);
        }, $files);

        $result = implode(',', $filesWithPrefix);

        return [
            'role' => $this->roleSystemKey,
            'content' => $result,
        ];
    }
}
