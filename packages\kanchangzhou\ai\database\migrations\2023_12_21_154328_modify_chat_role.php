<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->text('prompt')
                  ->change();
            $table->string('description')
                  ->nullable()
                  ->comment('描述')
                  ->after('prompt');
            $table->string('type')
                  ->nullable()
                  ->default('normal')
                  ->comment('类型')
                  ->after('prompt');
            $table->json('options')
                  ->nullable()
                  ->comment('选项')
                  ->after('type');
            $table->string('chat_role_category_id')
                  ->nullable()
                  ->default('')
                  ->comment('分组')
                  ->after('options');
            $table->tinyInteger('is_show')
                  ->nullable()
                  ->default(0)
                  ->comment('是否显示')
                  ->after('chat_role_category_id');
            $table->tinyInteger('is_hot')
                  ->nullable()
                  ->default(0)
                  ->comment('是否热门')
                  ->after('is_show');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->string('prompt')
                  ->change();
            $table->dropColumn('chat_role_category_id');
            $table->dropColumn('type');
            $table->dropColumn('group_key');
            $table->dropColumn('is_show');
            $table->dropColumn('description');
            $table->dropColumn('options');
            $table->dropColumn('is_hot');
        });
    }
};

