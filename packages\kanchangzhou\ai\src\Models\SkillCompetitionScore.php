<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\SkillCompetitionScore
 *
 * @property int $id
 * @property int $skill_competition_id
 * @property int $judge_id
 * @property string $judge_name
 * @property array $score
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore query()
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereJudgeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereJudgeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereSkillCompetitionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetitionScore whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperSkillCompetitionScore
 */
class SkillCompetitionScore extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'score' => 'array',
    ];
}
