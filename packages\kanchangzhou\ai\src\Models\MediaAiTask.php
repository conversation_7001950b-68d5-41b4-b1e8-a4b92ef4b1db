<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiTask
 *
 * @property int $id
 * @property int $media_ai_file_id 媒体文件id
 * @property string $file_id 文件id
 * @property string $module 模块
 * @property string $task_id 任务id
 * @property string $task_status 任务状态
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $task_status_str
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereModule($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereTaskStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiTask whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiTask
 */
class MediaAiTask extends Model
{
    use HasFactory;

    protected $guarded = [];

    // 状态: 1进行中 2已完成 3失败
    const STATUS_PROCESSING = 1;
    const STATUS_FINISHED = 2;
    const STATUS_FAILED = 3;

    const STATUS_MAP = [
        self::STATUS_PROCESSING => '进行中',
        self::STATUS_FINISHED => '已完成',
        self::STATUS_FAILED => '失败',
    ];

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }

    public function getTaskStatusStrAttribute() {
        return self::STATUS_MAP[$this->task_status] ?? '';
    }


}
