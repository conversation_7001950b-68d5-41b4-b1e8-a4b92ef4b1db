<?php


namespace Kanchangzhou\Administrator\Http\Middlewares;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Facades\AdministratorFacade;
use Kanchangzhou\Administrator\Models\AdminApplication;
use Kanchangzhou\Kernel\Exceptions\ThirdSignatureException;

class ThirdSignature
{
    /**
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     * @throws ThirdSignatureException
     */
    public function handle(Request $request, Closure $next) {
        $this->checkSignature($request);

        return $next($request);
    }

    protected function checkSignature(Request $request) {
        try {
            Validator::validate($request->input(), [
                'timestamp' => 'required',
                'nonce' => 'required',
                'signature' => 'required',
                'appkey' => 'required',
            ]);
        } catch (ValidationException $exception) {
            throw new ThirdSignatureException(ThirdSignatureException::SIGNATURE_FAIL_VALIDATOR);
        }

        if (Cache::has('nonce_' . $request->input('nonce'))) {
            throw new ThirdSignatureException(ThirdSignatureException::SIGNATURE_FAIL_NONCE);
        }

        Cache::put('nonce_' . $request->input('nonce'), true, 120);

        if (abs(intval($request->input('timestamp')) - time()) >= 600) {
            throw new ThirdSignatureException(ThirdSignatureException::SIGNATURE_FAIL_TIME);
        }

        $secretKey = AdminApplication::where('app_key', $request->input('appkey'))
                                     ->first();

        if (!$secretKey) {
            throw new ThirdSignatureException(ThirdSignatureException::SIGNATURE_FAIL_APPKEY);
        }

        $signatureData = $request->except([
            'signature',
        ]);

        ksort($signatureData);

        $signatureStr = [];
        foreach ($signatureData as $k => $v) {
//            if (empty($v)) {
//                continue;
//            }

            if (is_array($v)) {
                $v = json_encode($v, JSON_UNESCAPED_UNICODE);
            }

            $signatureStr[] = $k . '=' . $v;
        }

        $signatureStr = implode('&', $signatureStr);

        $signature = strtoupper(hash_hmac('sha256', $signatureStr, $secretKey->app_secret));

        if ($signature !== $request->input('signature')) {
            throw new ThirdSignatureException(ThirdSignatureException::SIGNATURE_FAIL, [
                'sign_str' => $signatureStr,
                'sign' => $signature,
            ]);
        }

        AdministratorFacade::auth()
                           ->login($secretKey->adminUser);
    }
}
