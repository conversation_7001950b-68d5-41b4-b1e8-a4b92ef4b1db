<?php
namespace Kanchangzhou\Advertisement\Exceptions;

class AdvertisingException extends BaseException{
    public static function message($code) {
        $msgArr = [
            self::HAS_EXISTS => '广告已经存在或正在合作中',
            self::NOT_EXISTS => '广告不存在',
            self::OPERATION_SUCCESS => '操作成功',
            self::OPERATION_FAIL => '操作失败',
            self::VALIDATE_FAIL=>"数据验证错误",
        ];
        return key_exists($code, $msgArr) ? $msgArr[$code] : parent::message($code);
    }
}
