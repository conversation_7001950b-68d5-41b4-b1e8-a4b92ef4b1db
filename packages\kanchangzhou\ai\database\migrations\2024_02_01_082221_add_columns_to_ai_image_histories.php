<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            //
			$table->string('point')
                  ->nullable()
                  ->comment('点数')
                  ->after('progress');
            $table->string('price')
                  ->nullable()
                  ->comment('金额')
                  ->after('progress');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            //
			$table->dropColumn('point');
			$table->dropColumn('price');
        });
    }
};
