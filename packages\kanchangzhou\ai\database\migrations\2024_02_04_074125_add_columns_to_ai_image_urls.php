<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ai_image_urls', function (Blueprint $table) {
            $table->text('image_base64')
                ->nullable()
                ->comment('图像base64编码')
                ->after('image_local');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ai_image_urls', function (Blueprint $table) {
            //
            $table->dropColumn('image_base64');
        });
    }
};
