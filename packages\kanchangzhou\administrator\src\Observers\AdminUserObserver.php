<?php

namespace Kanchangzhou\Administrator\Observers;

use Kanchangzhou\Administrator\Events\AdminUserEvent;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Auth\Facades\AuthFacade;

class AdminUserObserver
{
    /**
     * Handle the AdminUser "created" event.
     *
     * @param AdminUser $adminUser
     *
     * @return void
     */
    public function created(AdminUser $adminUser) {
        AuthFacade::setAdminUserCache($adminUser);
        event(new AdminUserEvent($adminUser));
    }

    /**
     * Handle the AdminUser "updated" event.
     *
     * @param AdminUser $adminUser
     *
     * @return void
     */
    public function updated(AdminUser $adminUser) {
        AuthFacade::setAdminUserCache($adminUser);
    }

    /**
     * Handle the AdminUser "deleted" event.
     *
     * @param AdminUser $adminUser
     *
     * @return void
     */
    public function deleted(AdminUser $adminUser) {
        AuthFacade::delAdminUserCache($adminUser->id);
    }

    /**
     * Handle the AdminUser "restored" event.
     *
     * @param AdminUser $adminUser
     *
     * @return void
     */
    public function restored(AdminUser $adminUser) {

    }

    /**
     * Handle the AdminUser "force deleted" event.
     *
     * @param AdminUser $adminUser
     *
     * @return void
     */
    public function forceDeleted(AdminUser $adminUser) {
        //
    }
}
