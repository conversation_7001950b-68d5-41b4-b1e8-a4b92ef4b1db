<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ai_images', function (Blueprint $table) {
            $table->integer('id')->primary();
            $table->string('module_name')->nullable()->comment('模型名');
            $table->string('module_key')->nullable()->comment('模型KEY');
            $table->string('provider')->nullable()->comment('服务');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_images');
    }
};
