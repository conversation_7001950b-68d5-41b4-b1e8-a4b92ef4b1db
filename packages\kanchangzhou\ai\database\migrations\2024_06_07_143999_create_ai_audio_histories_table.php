<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ai_audio_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->text('prompt')->nullable()->comment('关键词');
            $table->string('module')->nullable()->comment('模型名称');
            $table->string('provider')->nullable()->comment('服务提供者');
            $table->string('progress')->nullable()->comment('任务生成进度');
            $table->string('audio_local')->nullable()->comment('生成视频完整URL');
            $table->string('audio_origin')->nullable()->comment('生成视频原始URL');
            $table->string('status')->nullable()->comment('有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态');
            $table->string('point')->nullable()->comment('点数');
            $table->string('price')->nullable()->comment('价格');
            $table->string('task_id')->nullable()->comment('任务ID');
            $table->integer('user_id')->nullable()->comment('用户ID');
            $table->string('guard_name')->nullable()->comment('用户名称');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_audio_histories');
    }
};
