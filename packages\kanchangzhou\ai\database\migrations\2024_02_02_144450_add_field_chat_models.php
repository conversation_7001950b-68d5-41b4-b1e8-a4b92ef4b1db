<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->tinyInteger('status')
                  ->default(1)
                  ->comment('状态 1:正常 2:禁用')
                  ->after('chat_used');
            $table->tinyInteger('sort')
                  ->nullable()
                  ->default(1)
                  ->comment('排序')
                  ->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('sort');
        });
    }
};
