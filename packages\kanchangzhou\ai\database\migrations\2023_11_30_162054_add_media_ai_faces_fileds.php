<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('media_ai_faces', function (Blueprint $table) {
            $table->string('begin')
                  ->nullable()
                  ->comment('起始时间')
                  ->after('keyframe');
            $table->string('end')
                  ->nullable()
                  ->comment('结束时间')
                  ->after('begin');
            $table->renameColumn('rotation', 'angle');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('media_ai_faces', function (Blueprint $table) {
            $table->dropColumn('begin');
            $table->dropColumn('end');
            $table->renameColumn('angle', 'rotation');
        });
    }
};
