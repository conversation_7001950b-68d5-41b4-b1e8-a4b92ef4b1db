<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiNewsclassification
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $catalog_a_code 一级分类代码
 * @property string|null $catalog_a_tag 一级分类名称
 * @property string|null $catalog_b_code 二级分类代码
 * @property string|null $catalog_b_tag 二级分类名称
 * @property string|null $catalog_c_code 三级分类代码
 * @property string|null $catalog_c_tag 三级分类名称
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCatalogACode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCatalogATag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCatalogBCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCatalogBTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCatalogCCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCatalogCTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiNewsclassification whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiNewsclassification
 */
class MediaAiNewsclassification extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }
}
