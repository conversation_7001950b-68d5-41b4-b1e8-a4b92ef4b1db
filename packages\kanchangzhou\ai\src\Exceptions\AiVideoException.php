<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class AiVideoException extends BaseException
{
    const INVALID_CONFIG = 305001;
    const MISS_PARAM = 305002;
    const MISS_INTERFACE = 305003;
    // 内容不合规
    const HAS_SENSITIVE = 305004;

    const FAILED_REQUEST = 305005;

    const FAILED_ASR = 305006;
    const FAILED_CALLBACK = 305007;
    const INVALID_PARAMETER = 305008;

    public static function message($code) {
        $msgArr = [
            static::INVALID_CONFIG => '无效的配置',
            static::MISS_PARAM => '缺少参数',
            static::MISS_INTERFACE => '缺少服务提供者',
            static::HAS_SENSITIVE => '根据相关法律法规和政策，无法为您提供服务',
            static::FAILED_REQUEST => '接口请求错误',
            static::FAILED_ASR => '语音识别失败',
            static::FAILED_CALLBACK => '服务器详情失败，请稍后再试',
            static::INVALID_PARAMETER => '参数错误',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
