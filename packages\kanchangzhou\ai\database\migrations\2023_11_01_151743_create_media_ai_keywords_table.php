<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiKeywordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_ai_keywords', function (Blueprint $table) {
            $table->id();
            $table->integer('media_ai_file_id')->comment('文件ID');
            $table->string('keyword')
                ->nullable()->comment('关键词');
            $table->float('probability')
                  ->nullable()->comment('概率');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_ai_keywords');
    }
}
