<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            //
			$table->string('progress_data')
                ->nullable()
                ->comment('图片生成总进度详情')
                ->after('progress');
			$table->string('action')
                ->nullable()
                ->comment('生成图片执行的方法')
                ->after('progress');
            $table->string('progress')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            //
            $table->dropColumn('progress_data');
			$table->dropColumn('action');
            $table->dropColumn('progress');
        });
    }
};
