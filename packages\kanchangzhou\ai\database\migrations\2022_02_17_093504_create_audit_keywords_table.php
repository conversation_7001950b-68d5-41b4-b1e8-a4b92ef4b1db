<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('audit_keywords', function (Blueprint $table) {
            $table->id();
            $table->string('keyword');
            $table->string('hit_count')
                  ->nullable()
                  ->default(0);
            $table->integer('keyword_id');
            $table->integer('lib_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('audit_keywords');
    }
};
