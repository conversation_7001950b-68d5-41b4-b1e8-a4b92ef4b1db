<?php

namespace <PERSON><PERSON><PERSON>\SDK\OAuth;

use Changguan\SDK\Http\HttpClient;
use Changguan\SDK\Exceptions\OAuthException;

class OAuthClient
{

    /**
     * API路径定义
     */
    private const PATH_ACCESS_TOKEN = '/api/oauth2/access_token';
    private const PATH_USER_INFO = '/api/oauth2/user_info';
    // private const PATH_ADMIN_DEPARTMENT_list = '/api/oauth2/department-list';
    // private const PATH_ADMIN_USER_LIST = '/api/oauth2/admin-list';
    private const PATH_FULL_ADMIN_CONTACTS = '/api/oauth2/enterprise/full-contacts';
    private const PATH_DEPARTMENT_LIST = '/api/oauth2/enterprise/departments';
    /**
     * @var HttpClient
     */
    private HttpClient $httpClient;

    /**
     * OAuthClient constructor.
     *
     * @param HttpClient $httpClient
     */
    public function __construct(HttpClient $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    /**
     * 使用授权码获取访问令牌
     *
     * @param string $code 授权码
     *
     * @return array 包含访问令牌的响应数据
     * @throws OAuthException
     *
     * {
     * "errcode": 0,
     * "msg": "success",
     * "data": {
     * "access_token": "IYrzOK516mr2tYAlOdrk...Mjl3RsOHxAGtIgvRw",
     * "expires_in": 2591999,
     * "refresh_token": "evDZgwVXi9vV7SqYKxGLv....GDaBpxe6erq7Je3NlBldC5cqNLRf",
     * "refresh_expires_in": 7775999,
     * "open_id": "ByO_61a59a...fb1039b0ba4cf"
     * }
     * }
     */
    public function getAccessToken(string $code): array
    {
        try {
            if (empty($code)) {
                throw new OAuthException('授权码不能为空', OAuthException::ERROR_INVALID_PARAMS);
            }

            $params = [
                'grant_type' => 'authorization_code',
                'code'       => $code,
                'client_id'  => $this->httpClient->getClientId(),
            ];

            return $this->httpClient->request('POST', self::PATH_ACCESS_TOKEN, $params);
        } catch (\Exception $e) {
            throw new OAuthException('获取访问令牌失败: ' . $e->getMessage(), OAuthException::ERROR_TOKEN_INVALID);
        }
    }

    /**
     * 使用刷新令牌获取新的访问令牌
     *
     * @param string $refreshToken 刷新令牌
     *
     * @return array 包含新访问令牌的响应数据
     * @throws OAuthException
     */
    public function refreshToken(string $refreshToken): array
    {
        try {
            if (empty($refreshToken)) {
                throw new OAuthException('刷新令牌不能为空', OAuthException::ERROR_INVALID_PARAMS);
            }

            $params = [
                'grant_type'    => 'refresh_token',
                'refresh_token' => $refreshToken,
                'client_id'     => $this->httpClient->getClientId(),
            ];

            return $this->httpClient->request('POST', self::PATH_ACCESS_TOKEN, $params);
        } catch (\Exception $e) {
            throw new OAuthException('刷新访问令牌失败: ' . $e->getMessage(), OAuthException::ERROR_TOKEN_INVALID);
        }
    }

    /**
     * 获取用户信息
     *
     * @param string $accessToken 访问令牌
     * @param string $openId 用户的OpenID
     *
     * @return array 用户信息
     * @throws OAuthException
     */
    public function getUserInfo(string $accessToken, string $openId): array
    {
        try {
            if (empty($accessToken) || empty($openId)) {
                throw new OAuthException('访问令牌和OpenID不能为空', OAuthException::ERROR_INVALID_PARAMS);
            }

            $params = [
                'access_token' => $accessToken,
                'open_id'      => $openId,
            ];

            return $this->httpClient->request('POST', self::PATH_USER_INFO, $params);
        } catch (\Exception $e) {
            throw new OAuthException('获取用户信息失败: ' . $e->getMessage(), OAuthException::ERROR_TOKEN_INVALID);
        }
    }

    /**
     * 使用授权码获取用户信息
     *
     * @param string $code 授权码
     *
     * @return array 用户信息
     * @throws OAuthException
     */
    public function getUserInfoByCode(string $code)
    {
        $accessToken = $this->getAccessToken($code);

        return $this->getUserInfo($accessToken['data']['access_token'], $accessToken['data']['open_id']);
    }

    /**
     * 获取部门列表
     *
     * @return array 部门列表
     * @throws OAuthException
     */
    public function getEnterpriseDepartmentList(): array
    {
        $params = [];

        return $this->httpClient->request('POST', self::PATH_DEPARTMENT_LIST, $params);
    }

    /**
     * 获取用户列表
     *
     * @return array 用户列表
     * @throws OAuthException
     */
    public function getEnterpriseFullContacts(): array
    {
        $params = [];

        return $this->httpClient->request('POST', self::PATH_FULL_ADMIN_CONTACTS, $params);
    }
}
