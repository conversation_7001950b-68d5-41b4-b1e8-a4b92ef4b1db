<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Models\MediaAiNewsclassification;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiNlpNewsclassificationJob extends MediaAiBaseJob
{
// data demo: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"5bfc48990004469791e2968cad39b795","subDataTypes":[],"nlp/newsclassification":[{"fileId":"47f99a48-ad3c-497c-b443-63d2d0439163","statusCode":0,"statusInfo":"success","contents":[{"catalog_1":{"label":[{"code":"A","confidence":0.8540130853652954,"tag":"政治"}],"distribution":{"A-政治":0.8129}},"catalog_2":{"label":[{"code":"A12","confidence":0.6001043319702148,"tag":"中国共产党"}],"distribution":{"A12-中国共产党":0.5694,"A14-人民代表大会":0}},"catalog_3":{"label":[],"distribution":{"A1211-党的理论":0.0213,"A1213-党的组织、会议":0.1059}}}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $guid = $this->data['data']['guid'];
        $newsclassification = $this->data['data']['nlp/newsclassification'][0]['contents'][0] ?? [];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();
        if (!$task) {
            return;
        }

        MediaAiNewsclassification::updateOrCreate([
            'media_ai_file_id' => $task->media_ai_file_id,
        ], [
            'media_ai_file_id' => $task->media_ai_file_id,
            'catalog_a_code' => $newsclassification['catalog_1']['label'][0]['code'] ?? null,
            'catalog_a_tag' => $newsclassification['catalog_1']['label'][0]['tag'] ?? null,
            'catalog_b_code' => $newsclassification['catalog_2']['label'][0]['code'] ?? null,
            'catalog_b_tag' => $newsclassification['catalog_2']['label'][0]['tag'] ?? null,
            'catalog_c_code' => $newsclassification['catalog_3']['label'][0]['code'] ?? null,
            'catalog_c_tag' => $newsclassification['catalog_3']['label'][0]['tag'] ?? null,
        ]);


        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
