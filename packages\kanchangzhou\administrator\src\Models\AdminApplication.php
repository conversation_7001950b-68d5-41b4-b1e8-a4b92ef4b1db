<?php

namespace Kanchangzhou\Administrator\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\Administrator\Models\AdminApplication
 *
 * @property int $id
 * @property int $admin_user_id
 * @property string $title
 * @property string $app_key
 * @property string $app_secret
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\Administrator\Models\AdminUser|null $adminUser
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereAdminUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereAppKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereAppSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdminApplication whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAdminApplication
 */
class AdminApplication extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function adminUser() {
        return $this->belongsTo(AdminUser::class);
    }

}
