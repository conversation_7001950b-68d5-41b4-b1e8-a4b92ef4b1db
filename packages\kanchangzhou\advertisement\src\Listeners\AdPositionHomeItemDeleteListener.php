<?php

namespace Kanchangzhou\Advertisement\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Kanchangzhou\Advertisement\Models\AdvertisingPosition;
use Kanchangzhou\Kernel\Events\HomeItemDeleteEvent;

class AdPositionHomeItemDeleteListener
{
    public function __construct() {
        //
    }


    public function handle(HomeItemDeleteEvent $event) {
        if ($event->getTargetableType() == AdvertisingPosition::class) {
            $adPostion = AdvertisingPosition::where('id', $event->getTargetableId())
                                ->first();
            if (!$adPostion) {
                return true;
            }

            $hasPushed = $adPostion->has_pushed;

            foreach ($hasPushed as $k => $pushed) {
                if ($pushed['id'] == $event->getHomeItemId()) {
                    unset($hasPushed[$k]);
                }
            }
            $adPostion->has_pushed = $hasPushed;
            $adPostion->save();

        }
    }
}
