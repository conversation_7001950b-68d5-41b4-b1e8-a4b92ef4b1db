<?php

namespace Kanchangzhou\Administrator\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Kanchangzhou\Administrator\Facades\AdministratorFacade;
use Kanchangzhou\Kernel\Events\CheckRoleEvent;

class CheckRolesListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct() {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     *
     * @return void
     */
    public function handle(CheckRoleEvent $event) {
        return (int)AdministratorFacade::hasRole($event->getAdminUser(), $event->getRoles());
    }
}
