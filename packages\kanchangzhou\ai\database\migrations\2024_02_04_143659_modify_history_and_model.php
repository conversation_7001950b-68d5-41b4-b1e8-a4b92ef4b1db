<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->uuid('chat_role_id')
                  ->default(null)
                  ->nullable()
                  ->change();
            $table->uuid('chat_model_uuid')
                  ->nullable()
                  ->comment('模型uuid')
                  ->after('chat_role_id');
        });

        Schema::table('chat_models', function (Blueprint $table) {
            $table->string('icon', 1000)
                  ->nullable()
                  ->comment('图标')
                  ->after('title');
            $table->tinyInteger('is_default')
                  ->default(1)
                  ->nullable()
                  ->comment('是否默认模型')
                  ->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->integer('chat_role_id')
                  ->change();
            $table->dropColumn('chat_model_uuid');
        });

        Schema::table('chat_models', function (Blueprint $table) {
            $table->dropColumn('icon');
            $table->dropColumn('is_default');
        });
    }
};
