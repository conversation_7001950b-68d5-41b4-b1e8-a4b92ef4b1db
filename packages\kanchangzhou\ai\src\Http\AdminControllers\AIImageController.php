<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

use Kanchangzhou\AI\Exceptions\AIImageException;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Supports\GreenContent;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Hooks\SensitiveWordsHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Auth\Facades\AuthFacade;

use Kanchangzhou\AI\Models\AiImage;
use Kanchangzhou\AI\Models\AiImageHistory;
use Kanchangzhou\AI\Models\AiImageUrl;
use Kanchangzhou\AI\Http\Resources\AiImageHistoryResource;

class AIImageController extends BaseController
{
    public function txt2img(Request $request) {

        PermissionHook::can('智能多模态.文生图');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                    'midjourney',
                ]),
            ],
            //模型名称
            'text' => 'required|string',
            //输入内容
            //'negative_prompt' => 'required|string',   //反向输入内容
            //'resolution' =>"required|string",    //图片分辨率
            //'style' =>"required|string",    //目前支持风格
            //'num' =>'required|integer',    //图片生成数量，支持1-6张 默认1张
            //'seed' =>'required|integer',    //图片生成时的种子值
        ]);

        if (!GreenContent::plusIsLegal($request->input('text'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        // 增加多用户体系, 增加guard_name字段
        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function getImg(Request $request) {
        PermissionHook::can('智能多模态.文生图');

        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                    'midjourney',
                ]),
            ],
            //任务ID
            'task_id' => 'required|string',
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function txt2imgv2(Request $request) {
        PermissionHook::can('智能多模态.文生图高级版');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //模型名称
            'prompt' => [
                'required',
                'string',
                'regex:/^[\p{Han}\p{P}\s]+$/u',
            ],
            //生图的文本描述。仅支持中文、日常标点符号。不支持英文，特殊符号，限制 200 字
            'width' => 'required|integer',
            //图片宽度，支持：512x512、640x360、360x640、1024x1024、1280x720、720x1280、2048x2048、2560x1440、1440x2560
            'height' => 'required|integer',
            //图片高度，支持：512x512、640x360、360x640、1024x1024、1280x720、720x1280、2048x2048、2560x1440、1440x2560
            //'image_num' => '',   //生成图片数量，默认一张，支持生成 1-8 张
            //'image' => '',   //参考图，需 base64 编码，大小不超过 10M，最短边至少 15px，最长边最大 8192px，支持jpg/jpeg/png/bmp 格式。优先级：image > url > pdf_file，当image 字段存在时，url、pdf_file 字段失效
            //'url' => '',   //参考图完整 url，url 长度不超过 1024 字节，url 对应的图片需 base64 编码，大小不超过 10M，最短边至少 15px，最长边最大8192px，支持 jpg/jpeg/png/bmp 格式。优先级：image > url > pdf_file，当image 字段存在时，url 字段失效请注意关闭 URL 防盗链
            //'change_degree' => '',   //参考图影响因子，支持 1-10 内；数值越大参考图影响越大
        ], [
            'prompt.regex' => '仅支持中文、日常标点符号。不支持英文和特殊符号。',
        ]);

        if (!GreenContent::plusIsLegal($request->input('prompt'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function getImgv2(Request $request) {
        PermissionHook::can('智能多模态.文生图高级版');

        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //任务ID
            'task_id' => 'required|string',
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function txt2imgv3(Request $request) {
        PermissionHook::can('智能多模态.文生图实时版');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                    'chatgpt',
                ]),
            ],
            //模型名称
            'prompt' => 'required|string',
            //所需图像的文本描述。最大长度为 1000 个字符。必填字段。
            'size' => 'required|string',
            //生成的图像的大小。 必须是 1024x1024、1792x1024 或 1024x1792 之一，用于 DALL·E-3 型号。
            'style' => '',
            //样式‘natural’ or ‘vivid’（“自然”或“生动”）：生成图像的样式。必须是生动或自然的。生动使模型倾向于生成超真实和戏剧性的图像。自然使模型生成更自然、看起来不那么逼真的图像。默认为“vivid”。
            'quality' => '',
            //质量‘standard’ or ‘hd’（“标准”或“高清”）：将生成的图像的质量。“HD”创建的图像具有更精细的细节和更高的图像一致性。默认为“standard”。
            'response_format' => '',
            //返回生成的图像的格式。必须是“url”或“b64_json”之一。默认为“url”。
            'num' => '',
            //要生成的图像数量。必须介于 1 和 10 之间。默认值为 1。对于 dall-e-3，仅支持 n=1。
        ]);

        if (!GreenContent::plusIsLegal($request->input('prompt'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'openai'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function getImgHistory(Request $request) {
        PermissionHook::can('智能多模态.文生图高级版');

        $this->validate($request, [
            //模型名称
            'module' => [
                'nullable',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                    'midjourney',
                ]),
            ],
        ]);

        $query = AIImageHistory::with('aiImageUrls');
        if($request->input('module')){
            $query->where('module', Str::studly($request->input('module', 'baidu')));
        }
        $query->has('aiImageUrls');
        $history = $query->where('status', 'SUCCESS')->where('user_id', AuthFacade::adminUser()->getId())->orderByDesc('id')->paginate();

        //return Respond::respondWithData(AiImageHistoryResource::collection($history));
        return Respond::respondWithData(JsonResource::collection($history));
    }

    public function imageQualityEnhance(Request $request) {
        PermissionHook::can('智能多模态.图片无损放大');

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function inpainting(Request $request) {
        PermissionHook::can('智能多模态.图像修复');

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
            'rectangle' => "required",
            //要去除的位置为规则矩形时，给出坐标信息，每个元素包含left, top, width, height，int 类型。如： [{'width': 92, 'top': 25, 'height': 36, 'left': 543}] 注意：上传宽高、位置坐标参数要比图片实际宽高小
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function docRepair(Request $request) {
        PermissionHook::can('智能多模态.图片去底纹');

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function denoise(Request $request) {
        PermissionHook::can('智能多模态.图像去噪');

        $this->validate($request, [
            'image' => 'required_without:url',
            //被修复的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内。注意：图片的base64编码是不包含图片头的，如（data:image/jpg;base64,） (图片数据base64编码)
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过10M(参考：原图大约为8M以内），最短边至少10px，最长边最大5000px，长宽比4：1以内，支持jpg/png/bmp格式，当image字段存在时url字段失效。
            //'option' => '',     //可用于调节去噪强度，产生不同效果的去噪图，可根据期望进行效果调试。取值在[0，200]区间内, 默认100
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function logoSearch(Request $request) {
        PermissionHook::can('智能多模态.LOGO删除');

        $this->validate($request, [
            'image' => 'required_without:url',
            //图像数据，base64编码，要求base64编码后大小不超过4M，最短边至少15px，最长边最大4096px,支持jpg/png/bmp格式.注意：图片需要base64编码、去掉编码头后再进行urlencode。
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过4M，最短边至少15px，最长边最大4096px,支持jpg/png/bmp格式，当image字段存在时url字段失效。
            'custom_lib' => 'required|string',
            //是否只检索用户子库，true则只检索用户子库，false(默认)为检索底库+用户子库
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function logoAdd(Request $request) {
        PermissionHook::can('智能多模态.LOGO删除');

        $this->validate($request, [
            'image' => 'required_without:url',
            //图像数据，base64编码，要求base64编码后大小不超过4M，最短边至少15px，最长边最大4096px,支持jpg/png/bmp格式.注意：图片需要base64编码、去掉编码头后再进行urlencode。
            'url' => 'required_without:image',
            //图片完整URL，URL长度不超过1024字节，URL对应的图片base64编码后大小不超过4M，最短边至少15px，最长边最大4096px,支持jpg/png/bmp格式，当image字段存在时url字段失效。
            'brief' => 'required|string',
            //此处需要传对应的品牌名称name字段，必须为json格式，检索时带回。name长度小于100B，示例{"name": "abc"} 。brief需要进行urlencode。
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function logoDelete(Request $request) {
        PermissionHook::can('智能多模态.LOGO删除');

        $this->validate($request, [
            'image' => 'required_without:cont_sign',
            //base64编码后的图片数据（和cont_sign二选一）。要求base64编码后大小不超过4M，最短边至少15px，最长边最大4096px,支持jpg/png/bmp格式. 注意：图片需要base64编码、去掉编码头后再进行urlencode。。
            'cont_sign' => 'required_without:image',
            //图片签名（和image二选一，image优先级更高）
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function ttv(Request $request) {
        PermissionHook::can('智能多模态.图文转视频');

        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //用于生成视频的图文内容与设置项
            'source' => 'required|string',
            //视频生产配置
            'config' => 'required|string',
        ]);

        if (!GreenContent::plusIsLegal($request->input('source'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function getTtv(Request $request) {
        PermissionHook::can('智能多模态.图文转视频');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //模型名称
            'job_id' => 'required|string',
            //任务ID
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function text2audio(Request $request) {
        PermissionHook::can('智能多模态.文本转语音');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //模型名称
            'text' => 'required|string',
            //总字数不超过10万个字符，1个中文字、英文字母、数字或符号均算作1个字符
            'format' => '',
            //"mp3-16k"，"mp3-48k"，"wav"，"pcm-8k"，"pcm-16k"，默认为mp3-16k
            'voice' => '',
            //基础音库：度小宇=1，度小美=0，度逍遥（基础）=3，度丫丫=4； 精品音库：度逍遥（精品）=5003，度小鹿=5118，度博文=106，度小童=110，度小萌=111，度米朵=103，度小娇=5。默认为度小美
            'speed' => '',
            //取值0-15，默认为5中语速
            'pitch' => '',
            //取值0-15，默认为5中语调
            'volume' => '',
            //音量，基础音库取值0-9，精品音库取值0-15，默认为5中音量（取值为0时为音量最小值，并非为无声）
            'enable_subtitle' => '',
            //取值范围0, 1, 2，默认为0。0表示不开启字幕时间戳，1表示开启句级别字幕时间戳，2表示开启词级别字幕时间戳
            'break' => '',
            //取值 0-5000 ，单位ms，用于合成文本分段传入时设置段落间间隔。默认为0
        ]);

        if (!GreenContent::plusIsLegal($request->input('text'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function text2audioQuery(Request $request) {
        PermissionHook::can('智能多模态.文本转语音查询');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //模型名称
            'task_ids' => 'required|array',
            //推荐一次查询多个任务id，单次最多可查询200个
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function audio2text(Request $request) {
        PermissionHook::can('智能多模态.语音识别');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //模型名称
            'speech_url' => 'required|string',
            //可使用百度云对象存储进行音频存储，生成云端可外网访问的url链接，音频大小不超过500MB
            'format' => 'required|string',
            //["mp3", "wav", "pcm","m4a","amr"]单声道，编码 16bits 位深
            //'pid' => '',    //[80001（中文语音近场识别模型极速版）, 80006（中文音视频字幕模型，1737（英文模型）]
            //'rate' => '',   //[16000] 固定值
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function audio2textQuery(Request $request) {
        PermissionHook::can('智能多模态.语音识别查询');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'aliyun',
                    'tencent',
                    'sogou',
                    'xunfei',
                    'yitu',
                ]),
            ],
            //模型名称
            'task_ids' => 'required|array',
            //推荐一次查询多个任务id，单次最多可查询200个
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'baidu'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }


    public function img2video(Request $request) {
        PermissionHook::can('智能多模态.图片转视频');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in(['sd3']),
            ],
            //模型名称
            'image' => 'required',
            //输入内容
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'sd3'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function getImg2video(Request $request) {
        PermissionHook::can('智能多模态.图片转视频');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in(['sd3']),
            ],
            //模型名称
            'task_id' => 'required|string',
            //任务ID
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'sd3'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function txt2imgv4(Request $request) {
        PermissionHook::can('智能多模态.文生图SD3');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in(['sd3']),
            ],
            //模型名称
            'prompt' => 'required|string',
            //所需图像的文本描述。最大长度为 10000 个字符。必填字段。
            'aspect_ratio' => '',
            //
            'mode' => '',
            //
            'negative_prompt' => '',
            //
            'model' => '',
            //
            'seed' => '',
            //
            'output_format' => '',
            //
        ]);

        if (!GreenContent::plusIsLegal($request->input('prompt'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'sd3'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function txt2imgv5(Request $request) {
        PermissionHook::can('智能多模态.文生图MidJourney');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'midjourney',
                ]),
            ],
            // 具体操作
            'action' => 'required|string',
            // 模型名称
            'prompt' => 'required_if:action,generate',
        ]);

        if (!GreenContent::plusIsLegal($request->input('prompt'), 'llm_query_moderation')) {
            throw new AIImageException(AIImageException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'midjourney'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        // 增加多用户体系, 增加guard_name字段
        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

    public function getImgv5(Request $request) {
        PermissionHook::can('智能多模态.文生图MidJourney-获取结果');

        $this->validate($request, [
            'module' => [
                'required',
                Rule::in([
                    'midjourney',
                ]),
            ],
            //模型名称
            'task_id' => 'required|string',
        ]);

        $serviceProviderName = Str::studly($request->input('module', 'midjourney'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Image\Providers\\' . $serviceProviderName)) {
            throw new AIImageException(AIImageException::MISS_INTERFACE);
        }

        // 增加多用户体系, 增加guard_name字段
        $aiUser = new AiUser(AuthFacade::adminUser()
                                       ->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);

        $res = $service->handler(__FUNCTION__);

        return Respond::respondWithData($res);
    }

}
