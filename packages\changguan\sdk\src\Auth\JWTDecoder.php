<?php

namespace Changgua<PERSON>\SDK\Auth;

use Changguan\SDK\Crypto\GmSm;
use Changguan\SDK\Exceptions\AuthException;

/**
 * JWT Token 解码和验证类
 *
 * 用于处理JWT Token的解码、验证等操作。支持多种算法，包括HMAC系列和国密SM2。
 */
class JWTDecoder
{
    /**
     * @var GmSm 加密工具实例
     */
    protected $crypto;

    /**
     * @var array 支持的算法映射
     */
    protected $algorithms = [
        'HS256' => 'sha256',
        'HS384' => 'sha384',
        'HS512' => 'sha512',
        'SM2'   => 'SM2',
    ];

    /**
     * @param GmSm $crypto 加密工具实例
     */
    public function __construct(GmSm $crypto) {
        $this->crypto = $crypto;
    }

    /**
     * 解码JWT Token并验证其有效性
     *
     * @param string $token JWT token字符串
     * @param bool $returnPayload 是否返回payload数据
     * @param string|null $accessKey 可选的访问密钥，用于向后兼容
     *
     * @return array 解码后的payload数据
     */
    public function check($token, $returnPayload = false, $accessKey = null) {
        try {
            if (!$this->verify($token, $accessKey)) {
                throw new AuthException('Invalid token', AuthException::ERROR_INVALID_FORMAT);
            }

            $decoded = $this->decode($token);

            return $returnPayload ? $decoded['payload'] : true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 解码JWT Token
     *
     * @param string $token JWT token字符串
     *
     * @return array 解码后的数据
     * @throws AuthException 当解码失败时抛出
     */
    public function decode($token) {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                throw new AuthException('Invalid token format', AuthException::ERROR_INVALID_FORMAT);
            }

            $header = $this->decodeSegment($parts[0]);
            $payload = $this->decodeSegment($parts[1]);
            $signature = $this->urlsafeB64Decode($parts[2]);

            if (!$header || !$payload) {
                throw new AuthException('Token decode failed', AuthException::ERROR_DECODE_FAILED);
            }

            return [
                'header'     => $header,
                'headerRaw'  => $parts[0],
                'payload'    => $payload,
                'payloadRaw' => $parts[1],
                'signature'  => $signature,
            ];
        } catch (AuthException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new AuthException('Token decode failed: ' . $e->getMessage(), AuthException::ERROR_DECODE_FAILED);
        }
    }

    /**
     * 验证JWT Token
     *
     * @param string $token JWT token字符串
     * @param string|null $accessKey 密钥字符串（可选，用于向后兼容）
     *
     * @return bool 验证是否通过
     * @throws AuthException
     */
    public function verify($token, $accessKey = null) {
        try {
            $decodeToken = $this->decode($token);

            // 获取header和payload
            $header = $decodeToken['header'];
            $payload = $decodeToken['payload'];

            // 如果提供了新的accessKey，临时重新配置crypto
            $originalConfig = null;
            if ($accessKey !== null) {
                $originalConfig = $this->crypto->getKeys();
                $this->crypto->configure($accessKey);
            }

            try {
                // 验证签名
                $signatureValid = $this->calculateSignature($decodeToken['headerRaw'] . '.' . $decodeToken['payloadRaw'], $decodeToken['signature'], $header['alg'] ?? 'HS256', $payload['sub'] ?? null);

                if (!$signatureValid) {
                    throw new AuthException('Invalid signature', AuthException::ERROR_INVALID_SIGNATURE);
                }

                // 验证时间相关的声明
                $time = time();

                // 验证是否过期(exp)
                if (isset($payload['exp']) && $time >= $payload['exp']) {
                    throw new AuthException('Token has expired', AuthException::ERROR_EXPIRED);
                }

                // 验证生效时间(nbf)
                if (isset($payload['nbf']) && $time < $payload['nbf']) {
                    throw new AuthException('Token not valid yet', AuthException::ERROR_NOT_VALID_YET);
                }

                // 验证签发时间(iat)
                if (isset($payload['iat']) && $time < $payload['iat']) {
                    throw new AuthException('Token not valid yet', AuthException::ERROR_NOT_VALID_YET);
                }

                return true;
            } finally {
                // 如果之前修改了配置，恢复原始配置
                if ($originalConfig !== null) {
                    $this->crypto->configure($originalConfig['publicKey'] ?? null, $originalConfig['privateKey'] ?? null);
                }
            }
        } catch (AuthException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new AuthException('Token verification failed: ' . $e->getMessage(), AuthException::ERROR_INVALID_FORMAT);
        }
    }

    /**
     * 解码JWT段数据(header或payload)
     *
     * @param string $segment
     *
     * @return array
     * @throws AuthException
     */
    private function decodeSegment($segment) {
        $decoded = $this->urlsafeB64Decode($segment);
        if ($decoded === false) {
            throw new AuthException('Invalid segment encoding', AuthException::ERROR_DECODE_FAILED);
        }

        $data = json_decode($decoded, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new AuthException('Invalid segment data', AuthException::ERROR_DECODE_FAILED);
        }

        return $data;
    }

    /**
     * URL安全的Base64解码
     *
     * @param string $input
     *
     * @return string|false
     */
    private function urlsafeB64Decode($input) {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $input .= str_repeat('=', 4 - $remainder);
        }
        $input = strtr($input, '-_', '+/');

        return base64_decode($input);
    }

    /**
     * 计算签名
     *
     * @param string $payload 需要签名的数据
     * @param string $signature 签名
     * @param string $algorithm 算法
     * @param string|null $userId 用户ID（可选）
     *
     * @return bool
     * @throws AuthException
     */
    private function calculateSignature($payload, $signature, $algorithm, $userId = null) {
        // 检查算法是否支持
        if (!isset($this->algorithms[$algorithm])) {
            throw new AuthException('Unsupported algorithm: ' . $algorithm, AuthException::ERROR_INVALID_FORMAT);
        }

        $algo = $this->algorithms[$algorithm];

        try {
            if ($algo === 'SM2') {
                return $this->crypto->sm2VerifySign($payload, $signature, $userId, 'hex');
            } else {
                // 对于HMAC算法，从crypto获取密钥
                $keys = $this->crypto->getKeys();
                $key = $keys['privateKey'] ?? '';

                if (empty($key)) {
                    throw new AuthException('No key available for HMAC verification', AuthException::ERROR_MISSING_KEY);
                }

                return hash_equals(hash_hmac($algo, $payload, $key, true), $signature);
            }
        } catch (AuthException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new AuthException('Signature verification failed: ' . $e->getMessage(), AuthException::ERROR_INVALID_SIGNATURE);
        }
    }

    /**
     * 获取Token中的特定声明
     *
     * @param string $token JWT token字符串
     * @param string $claim 声明名称
     *
     * @return mixed|null
     */
    public function getClaim($token, $claim) {
        try {
            $decoded = $this->decode($token);

            return $decoded['payload'][$claim] ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取所有标准声明
     *
     * @param string $token JWT token字符串
     *
     * @return array 包含标准声明的数组
     */
    public function getStandardClaims($token) {
        try {
            $payload = $this->decode($token)['payload'];

            return [
                'sub' => $payload['sub'] ?? null,
                'exp' => $payload['exp'] ?? null,
                'iat' => $payload['iat'] ?? null,
                'nbf' => $payload['nbf'] ?? null,
                'iss' => $payload['iss'] ?? null,
                'aud' => $payload['aud'] ?? null,
            ];
        } catch (\Exception $e) {
            return [];
        }
    }
}
