<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            //
            $table->string('revised_prompt')
                ->nullable()
                ->comment('优化关键词')
                ->after('prompt');
            $table->text('prompt')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            //
            $table->dropColumn('revised_prompt');
            $table->string('prompt', 255)->change();
        });
    }
};
