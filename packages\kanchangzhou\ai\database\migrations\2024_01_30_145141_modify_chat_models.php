<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->dropColumn('root');
            $table->dropColumn('parent');

            $table->uuid('model_uuid')
                  ->after('id');
            $table->string('provider')
                  ->after('model_uuid');
            $table->float('temperature')
                  ->nullable()
                  ->default(0.8)
                  ->after('provider')
                  ->comment('温度');
            $table->float('top_p')
                  ->nullable()
                  ->default(0.9)
                  ->after('temperature')
                  ->comment('top_p');
            $table->integer('max_tokens')
                  ->nullable()
                  ->after('top_p')
                  ->comment('最大token数');
            $table->tinyInteger('enable_context')
                  ->nullable()
                  ->default(1)
                  ->after('max_tokens')
                  ->comment('是否启用上下文');
            $table->tinyInteger('context_round')
                  ->nullable()
                  ->default(10)
                  ->after('enable_context')
                  ->comment('上下文轮数');
            $table->tinyInteger('chat_used')
                  ->nullable()
                  ->default(1)
                  ->after('context_round')
                  ->comment('是否启用聊天');
            $table->softDeletes();
        });

        Schema::table('chat_roles', function (Blueprint $table) {
            $table->float('temperature')
                  ->nullable()
                  ->after('type')
                  ->comment('温度');
            $table->float('top_p')
                  ->nullable()
                  ->after('temperature')
                  ->comment('top_p');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->string('root')
                  ->nullable()
                  ->after('type')
                  ->comment('根节点');
            $table->string('parent')
                  ->nullable()
                  ->after('root')
                  ->comment('父节点');

            $table->dropColumn('model_uuid');
            $table->dropColumn('temperature');
            $table->dropColumn('top_p');
            $table->dropColumn('max_tokens');
            $table->dropColumn('enable_context');
            $table->dropColumn('context_round');
            $table->dropColumn('chat_used');
            $table->dropSoftDeletes();
        });

        Schema::table('chat_roles', function (Blueprint $table) {
            $table->dropColumn('temperature');
            $table->dropColumn('top_p');
        });
    }
};
