<?php

namespace Kanchangzhou\AI\Services\Audio\Providers\Volcengine;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\AI\Exceptions\AiAudioException;
use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsClient;
use Kanchangzhou\AI\Services\Audio\Handlers\AiAudioWsHandler;
use Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Auth;
use Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Config\StreamConfig;
use Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Config\TaskConfig;
use Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Config\TextConfig;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Models\AiAudioHistory;

class Client extends AiTtsClient
{
    const CLIENT_NAME = 'volcengine';
    const TASK_URL = 'https://openspeech.bytedance.com/api/v1/tts_async/submit';
    const TASK_QUERY_URL = 'https://openspeech.bytedance.com/api/v1/tts_async/query';
    const TASK_SHORT_URL = 'https://openspeech.bytedance.com/api/v1/tts';

    public function __construct() {
        parent::__construct();
    }

    public function textToSpeechStream(string $text) {
        return;
    }

    public function textToSpeech(string $text) {

        $requestConfig = (new TextConfig($this->getRequestConfig()))->toArray();
        $response = Http::acceptJson()
            ->withHeaders([
                "Authorization" => "Bearer;".config('kai.tts.providers.volcengine.access_token'),
            ])
            ->post(self::TASK_SHORT_URL, $requestConfig);

        if ($response->successful()) {

            if($response->json('code') != 3000){
                throw new AiAudioException(AiAudioException::FAILED_REQUEST,[],400,$response->json('message') );
            }

            $task_id = $response->json('reqid');

            $path = 'ai-audios'.'/'.date('Ymd').'/'.$task_id;
            if (!\Storage::disk('oss')->exists($path)) {
                \Storage::disk('oss')->put($path, base64_decode($response->json('data')));
            }
            $audio_local = \Storage::disk('oss')->url($path);

            //保存记录
            $request['module'] = self::CLIENT_NAME;
            $request['provider'] =  __FUNCTION__;
            $request['prompt'] = $text;
            $request['task_id'] = $task_id;
            $request['progress'] = 100;
            $request['status'] = 'SUCCESS';
            $request['audio_local'] = $audio_local;
            $request['task_params'] = json_encode($requestConfig);

            return AiAudioHistory::createFromRequest($task_id, $request);
        } else {
            throw new AiAudioException(AiAudioException::FAILED_REQUEST, [], 400, $response->json('message'));
        }
    }

    public function createTask(string $text) {
        $requestConfig = (new TaskConfig($this->getRequestConfig()))->toArray();

        $response = Http::acceptJson()
            ->withHeaders([
                "Authorization" => "Bearer;".config('kai.tts.providers.volcengine.access_token'),
                "Resource-Id" => "volc.tts_async.default"
            ])
            ->post(self::TASK_URL, $requestConfig);

        if ($response->successful()) {

            if($response->json('task_status') == 2){
                throw new AiAudioException(AiAudioException::FAILED_REQUEST,[],400,$response->json('message') );
            }

            //保存记录
            $request['module'] = 'volcengine';
            $request['provider'] =  __FUNCTION__;
            $request['prompt'] = $text;
            $request['task_params'] = json_encode($requestConfig);
            return AiAudioHistory::createFromRequest($response->json('task_id'), $request);
        } else {
            throw new AiAudioException(AiAudioException::FAILED_REQUEST, [], 400, $response->json('message'));
        }
    }

    public function fetchTaskResult($taskId) {

        $history = AiAudioHistory::where('task_id', $taskId)->firstOrFail()->toArray();
        if($history['status'] == 'FAILED'){
            throw new AiAudioException(AiAudioException::TASK_FAILED, [], 400, $history['failed_reason']);
        }

        $response = Http::acceptJson()
            ->withHeaders([
                "Authorization" => "Bearer;".config('kai.tts.providers.volcengine.access_token'),
                "Resource-Id" => "volc.tts_async.default"
            ])
            ->get(self::TASK_QUERY_URL, [
                'appid' => config('kai.tts.providers.volcengine.appid'),
                'task_id' => $taskId,
            ]);

        if ($response->successful()) {

            //保存记录
            if($history['status'] != 'SUCCESS'){

                $data = $response->json();

                if($data['task_status'] == 1){
                    //本地化视频
                    $path = 'ai-audio' . '/' . date('Ymd') . '/' . $taskId;
                    if (!\Storage::disk('oss')->exists($path)) {
                        \Storage::disk('oss')->putRemoteFile($path, $data['audio_url']);
                    }
                    $audioLocal = \Storage::disk('oss')->url($path);
                    if($history){
                        $updateData = [
                            'progress' => 100,
                            'status' => 'SUCCESS',
                            'audio_origin' => $data['audio_url'],
                            'audio_local' => $audioLocal,
                            'result_params' => json_encode($data),
                        ];
                        $res = AiAudioHistory::updateByTaskId($taskId, $updateData);
                        if($res){
                            $history = array_merge($history, $updateData);
                        }
                    }
                }else{
                    if($data['task_status'] == 2){
                        $updateData = [
                            'status' => 'FAILED',
                            'failed_reason' => $data['message'],
                        ];
                        AiAudioHistory::updateByTaskId($taskId, $updateData);

                        throw new AiAudioException(AiAudioException::TASK_FAILED, [], 400, $data['message']);
                    }
                }
            }
            return $history;
        }else{
            throw new AiAudioException(AiAudioException::FAILED_REQUEST, [], 400, $response->json('message'));
        }
    }
    public function train($request)
    {
        $audioPath = $request->file('audio')->path();
        $audioData = file_get_contents($audioPath);
        $encodedData = base64_encode($audioData);

        $url = "https://openspeech.bytedance.com/api/v1/mega_tts/audio/upload";
        $headers = [
            "Authorization" => "Bearer;" . config('kai.tts.providers.volcengine.access_token'),
            "Resource-Id" => "volc.megatts.voiceclone",
        ];

        $audios = [["audio_bytes" => $encodedData, "audio_format" => 'mp3']];
        $data = [
            "appid" => config('kai.tts.providers.volcengine.appid'),
            "speaker_id" => $request->input('speaker_id'),
            "audios" => $audios,
            "source" => 2
        ];

        $response = Http::acceptJson()
            ->withHeaders($headers)
            ->post($url, $data);

        if ($response->successful()) {
            if($response->json('BaseResp.StatusCode')){
                throw new AiAudioException(AiAudioException::TASK_FAILED, [], 400, $response->json('BaseResp.StatusMessage'));
            }
            return [
                'speaker_id'=>$data['speaker_id'],
                'status'=>'SUCCESS'
            ];
        }else{
            throw new AiAudioException(AiAudioException::TASK_FAILED);
        }
    }

    public function trainStatus($speakerId)
    {
        $url = "https://openspeech.bytedance.com/api/v1/mega_tts/status";
        $headers = [
            "Authorization" => "Bearer;" . config('kai.tts.providers.volcengine.access_token'),
            "Resource-Id" => "volc.megatts.voiceclone",
        ];

        $data = ["appid" => config('kai.tts.providers.volcengine.appid'), "speaker_id" => $speakerId];
        $response = Http::acceptJson()
            ->withHeaders($headers)
            ->post($url, $data);

        if ($response->successful()) {
            if($response->json('BaseResp.StatusCode')){
                throw new AiAudioException(AiAudioException::TASK_FAILED, [], 400, $response->json('BaseResp.StatusMessage'));
            }
            return [
                'speaker_id'=>$response->json('BaseResp.speaker_id'),
                'status'=>$response->json('BaseResp.status')
            ];
        }else{
            throw new AiAudioException(AiAudioException::TASK_FAILED);
        }
    }
}
