<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;

class ResponseMediaAiAudioClassificationJob extends MediaAiBaseJob
{
// data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"66e84572706d441d9655b05f7c43a224","subDataTypes":[{"type":"audioClassification","source":"索贝","version":null}],"audioClassification":[{"fileId":"370099d2-7447-4fea-87f0-24b6c032ccbc","statusCode":0,"statusInfo":"success","contents":[{"Music":0.9365234375,"Speech":0.935546875,"Speech synthesizer":0.73974609375,"Tick-tock":0.************,"Smash, crash":0.************}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {

    }
}
