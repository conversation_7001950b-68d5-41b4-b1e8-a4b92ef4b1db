<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyChatHistories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        // 给ChatHistories表哥添加字段, 字段是否重试标记,点赞,总计token数,本次token数
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->tinyInteger('is_retry')
                  ->default(\Kanchangzhou\AI\Models\ChatHistory::IS_RETRY_NO)
                  ->comment('是否重试')
                  ->nullable();
            $table->tinyInteger('is_like')
                  ->default(0)
                  ->comment('是否点赞')
                  ->nullable();
            $table->integer('total_tokens')
                  ->default(0)
                  ->comment('总计token数')
                  ->nullable();
            $table->integer('input_tokens')
                  ->default(0)
                  ->comment('输入token数')
                  ->nullable();
            $table->integer('output_tokens')
                  ->default(0)
                  ->comment('输出token数')
                  ->nullable();
            $table->string('provider_request_id')
                  ->nullable();
            $table->string('chat_model_id')
                  ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        // 删除ChatHistories表哥添加字段, 字段是否重试标记,点赞,点踩
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('is_retry');
            $table->dropColumn('is_like');
            $table->dropColumn('total_tokens');
            $table->dropColumn('input_tokens');
            $table->dropColumn('output_tokens');
            $table->dropColumn('provider_request_id');
            $table->string('chat_model_id')
                  ->change();
        });
    }
}
