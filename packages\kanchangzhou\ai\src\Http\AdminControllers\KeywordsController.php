<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\AI\Models\AuditKeyword;
use Kanchangzhou\AI\Supports\KeywordLib;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class KeywordsController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('敏感词.列表');
        $keywords = KeywordLib::searchKeywords(config('kai.other.aliyun.green.keyword_lib_id'), $request->input('keyword', ''));

        if ($keywords->isNotEmpty()) {
            foreach ($keywords as $keyword) {
                AuditKeyword::updateOrCreate(['keyword_id' => $keyword['keyword_id']], [
                    'keyword' => $keyword['keyword'],
                    'hit_count' => $keyword['hit_count'],
                    'keyword_id' => $keyword['keyword_id'],
                    'lib_id' => config('kai.other.aliyun.green.keyword_lib_id'),
                ]);
            }
        }

        return Respond::respondWithData(JsonResource::collection($keywords));
    }

    public function store(Request $request) {
        PermissionHook::can('敏感词.新增');

        $this->validate($request, ['keywords' => 'required|array|max:20'], []);

        $keywords = $request->input('keywords');

        $res = KeywordLib::createKeywords(config('kai.other.aliyun.green.keyword_lib_id'), $keywords);

        if ($res) {
            foreach ($res as $re) {
                AuditKeyword::updateOrCreate(['keyword_id' => $re['keyword_id']], [
                    'keyword' => $re['keyword'],
                    'hit_count' => 0,
                    'keyword_id' => $re['keyword_id'],
                    'lib_id' => config('kai.other.aliyun.green.keyword_lib_id'),
                ]);
            }
        }

        return Respond::respondWithData();
    }

    public function destroy($id) {
        PermissionHook::can('敏感词.删除');

        $keyword = AuditKeyword::where('keyword_id', $id)
                               ->firstOrFail();

        KeywordLib::delKeywords($keyword->lib_id, [$keyword->keyword_id]);

        $keyword->delete();

        return Respond::respondWithData();
    }
}
