<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class AIMusicException extends BaseException
{

    const HAS_SENSITIVE = 306001;
    const MISS_INTERFACE = 306002;
    const NOT_FOUND = 306003;


    public static function message($code) {
        $msgArr = [

            static::HAS_SENSITIVE => '根据相关法律法规和政策，无法为您提供服务',
            static::MISS_INTERFACE => '缺少服务提供者',
            static::NOT_FOUND => '未找到相关数据',

        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
