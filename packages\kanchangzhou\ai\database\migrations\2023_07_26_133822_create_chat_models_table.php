<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('chat_models', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('model_id');
            $table->string('type');
            $table->string('root')
                  ->nullable();
            $table->string('parent')
                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('chat_models');
    }
};
