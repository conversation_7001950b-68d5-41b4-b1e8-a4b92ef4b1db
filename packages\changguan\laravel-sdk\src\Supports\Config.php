<?php

namespace Changgua<PERSON>\LaravelSDK\Supports;

class Config
{
    /**
     * 配置数组
     *
     * @var array
     */
    protected $config;

    /**
     * 创建新的配置实例
     *
     * @param array $config
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * 获取默认项目名称
     *
     * @return string
     */
    public function getDefaultProject(): string
    {
        return $this->config['default'] ?? 'main';
    }

    /**
     * 获取指定项目的配置
     *
     * @param string $project
     * @return array
     * @throws \InvalidArgumentException
     */
    public function getProjectConfig(string $project): array
    {
        if (!$this->hasProject($project)) {
            throw new \InvalidArgumentException("项目 [$project] 未配置。");
        }

        return $this->config['projects'][$project];
    }

    /**
     * 检查项目是否存在于配置中
     *
     * @param string $project
     * @return bool
     */
    public function hasProject(string $project): bool
    {
        return isset($this->config['projects'][$project]);
    }

    /**
     * 获取所有已配置的项目
     *
     * @return array
     */
    public function getProjects(): array
    {
        return $this->config['projects'] ?? [];
    }

    /**
     * 获取完整的配置数组
     *
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
