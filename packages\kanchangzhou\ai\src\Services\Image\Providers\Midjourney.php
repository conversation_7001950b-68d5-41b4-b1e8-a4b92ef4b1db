<?php

namespace Kanchangzhou\AI\Services\Image\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rule;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AIImageException;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Services\Image\Contacts\AIImageService;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\AI\Models\AiImageHistory;
use Kanchangzhou\AI\Models\AiImageUrl;

class Midjourney extends AIImageService
{

    public function handler($action) {
        if (method_exists($this, $action)) {
            return $this->$action();
        }else{
            throw new AIException("{$this->module}不支持{$action}操作");
        }
    }

    //MJ 首次请求
    public function txt2imgv5() {
        ChatLimit::imageCheckLimit();

        $body = [
            'prompt' => $this->request->input('prompt'),
            'action' => $this->request->input('action') ?? 'generate',
            'image_id' => $this->request->input('image_id') ?? null,
            'key' => config('kai.image.providers.midjourney.api_key'),  //7jyPykypTws692WtcPqrqvu2mW
            'callback_url' => url(config('kai.image.providers.midjourney.call_back_url')),  ///api/v1/ai/image/mj-callback
        ];

        $get_post_data = http_build_query($body);
        $body['sign'] = md5($get_post_data . config('kai.image.providers.midjourney.api_sk'));

        $res = Http::acceptJson()
            ->withOptions([
               'query' => $body,
            ])
            ->post(config('kai.image.providers.midjourney.base_url'), $body);   //https://api.wike.cc/api/midjourney/imagine/fast

        if ($res->successful()) {
            if($res->json('code') != 200){
                throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,$res->json('msg') );
            }

            //计费 ￥0.18 / image
            $price = 0.18;
            $point = 1; //总张数
            ChatLimit::setProvider('Midjourney')->setModule('txt2imgv5','AIImage')->imageIncrement($point);

            $data = [
                'module' => Str::studly(class_basename(get_class($this))),
                'provider' => __FUNCTION__,
                'prompt' => $body['prompt'],
                'task_id' => $res->json('data.task_id'),
                'action' => $body['action'],
                'point' => $point,
                'price' => $price,
            ];
            $return = $this->createHistory($data);

            return $return;
        }else{
            throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,AIImageException::SERVICE_ERROR);
        }
    }

    public function getImgv5() {
        $info = AiImageHistory::with('aiImageUrls')
            ->where('task_id', $this->request->input('task_id'))
            ->firstOrFail();

        //绘图失败
        if($info['status'] == 'FAILED'){
            throw new AIImageException(AIImageException::INVALID_CALLBACK,[],400,$info['failed_reason']);
        }

        return $info;
    }
}
