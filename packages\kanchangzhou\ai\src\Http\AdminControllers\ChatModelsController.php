<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ChatModelsController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('智能模型.列表');

        $models = ChatModel::when($request->input('title'), function ($query, $title) {
            return $query->where('title', 'like', "%$title%");
        })
                           ->when($request->input('provider'), function ($query, $provider) {
                               return $query->where('provider', $provider);
                           })
                           ->when($request->input('chat_used'), function ($query, $chat_used) {
                               return $query->where('chat_used', $chat_used);
                           })
                           ->when($request->input('status'), function ($query, $status) {
                               return $query->where('status', $status);
                           })
                           ->when($request->input('model_type'), function ($query, $modelType) {
                               return $query->where('model_type', $modelType);
                           }, function ($query) {
                               return $query->where('model_type', ChatModel::MODEL_TYPE_CHAT);
                           })
                           ->orderBy('sort', 'asc')
                           ->paginate(10);

        return Respond::respondWithData(JsonResource::collection($models));
    }

    public function show($id) {
        PermissionHook::can('智能模型.详情');

        $model = ChatModel::findOrFail($id);

        return Respond::respondWithData(JsonResource::make($model));
    }

    public function formOptions() {
        $providers = ChatModel::PROVIDER_MAP;
        $status = ChatModel::STATUS_MAP;
        $chatUsed = ChatModel::CHAT_USED_MAP;
        $enableContext = ChatModel::ENABLE_CONTEXT_MAP;
        $temperatureRange = ChatModel::TEMPERATURE_RANGE;
        $penaltyRange = ChatModel::PENALTY_RANGE;
        $isDefault = ChatModel::IS_DEFAULT_MAP;
        $modelTypes = ChatModel::MODEL_TYPE_MAP;
        $canFiles = ChatModel::CAN_FILES_MAP;

        return Respond::respondWithData(compact('providers', 'status', 'chatUsed', 'enableContext', 'temperatureRange', 'penaltyRange', 'isDefault', 'modelTypes','canFiles'));
    }

    public function store(Request $request) {
        PermissionHook::can('智能模型.创建');

        $this->validate($request, [
            'title' => 'required',
            'icon' => '',
            'model_id' => 'required',
            'provider' => 'required|in:' . implode(',', array_keys(ChatModel::PROVIDER_MAP)),
            'temperature' => 'required',
            'penalty' => '',
            'max_tokens' => '',
            'enable_context' => '',
            'context_round' => '',
            'chat_used' => 'required',
            'status' => 'required',
            'sort' => 'required',
            'api_gateway' => '',
            'model_type' => '',
        ]);

        $chatModel = ChatModel::create([
            'model_uuid' => Str::uuid(),
            'provider' => $request->input('provider'),
            'icon' => $request->input('icon'),
            'title' => $request->input('title'),
            'model_id' => $request->input('model_id'),
            'model_type' => ChatModel::MODEL_TYPE_CHAT,
            'temperature' => $request->input('temperature'),
            'penalty' => $request->input('penalty', 1.1),
            'max_tokens' => $request->input('max_tokens', 2000),
            'enable_context' => $request->input('enable_context', 0),
            'context_round' => $request->input('context_round', 3),
            'chat_used' => $request->input('chat_used'),
            'status' => $request->input('status'),
            'sort' => $request->input('sort'),
            'is_default' => ChatModel::IS_DEFAULT_NO,
            'api_gateway' => $request->input('api_gateway'),
        ]);

        return Respond::respondWithData();
    }

    public function update(Request $request, $id) {
        PermissionHook::can('智能模型.编辑');

        $chatModel = ChatModel::findOrFail($id);

        $this->validate($request, [
            'title' => 'required',
            'model_id' => 'required',
            'provider' => 'required:in:' . implode(',', array_keys(ChatModel::PROVIDER_MAP)),
            'temperature' => 'required',
            'penalty' => '',
            'max_tokens' => '',
            'enable_context' => '',
            'context_round' => '',
            'chat_used' => 'required',
            'status' => 'required',
            'sort' => 'required',
            'api_gateway' => '',
        ]);

        $chatModel->update([
            'provider' => $request->input('provider'),
            'icon' => $request->input('icon'),
            'title' => $request->input('title'),
            'model_id' => $request->input('model_id'),
//            'model_type' => ChatModel::MODEL_TYPE_CHAT,
            'temperature' => $request->input('temperature', 0.85),
            'penalty' => $request->input('penalty', 1.1),
            'max_tokens' => $request->input('max_tokens', 50),
            'enable_context' => $request->input('enable_context', 0),
            'context_round' => $request->input('context_round', 3),
            'chat_used' => $request->input('chat_used'),
            'status' => $request->input('status'),
            'sort' => $request->input('sort'),
            'api_gateway' => $request->input('api_gateway'),
        ]);

        return Respond::respondWithData();
    }

    public function destroy($id) {
        PermissionHook::can('智能模型.删除');

        $chatModel = ChatModel::findOrFail($id);

        $chatModel->delete();

        return Respond::respondWithData();
    }
}
