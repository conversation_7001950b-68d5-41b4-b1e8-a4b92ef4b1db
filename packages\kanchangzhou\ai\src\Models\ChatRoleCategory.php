<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\ChatRoleCategory
 *
 * @property int $id
 * @property string $title
 * @property int|null $is_show
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $sort
 * @property-read mixed $is_show_str
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\ChatRole> $roles
 * @property-read int|null $roles_count
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory whereIsShow($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleCategory whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperChatRoleCategory
 */
class ChatRoleCategory extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'is_show_str',
    ];

    const IS_SHOW_NO = 1;
    const IS_SHOW_YES = 2;

    const IS_SHOW_MAP = [
        self::IS_SHOW_NO => '否',
        self::IS_SHOW_YES => '是',
    ];

    public function roles() {
        return $this->hasMany(ChatRole::class, 'chat_role_category_id', 'id');
    }

    public function getIsShowStrAttribute() {
        return self::IS_SHOW_MAP[$this->is_show] ?? '';
    }
}
