<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Kanchangzhou\Kernel\Supports\Redirectable;

class FixShareConfigDataCommend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kcz:shareConfig';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $this->info('============ Article ============');
        \Kanchangzhou\Article\Models\Article::chunk(100, function ($articles, $page) {
            $this->info("============== Page {$page}=============");
            $insertData = [];
            foreach ($articles as $article) {
//                if (Cache::has('share_config_article_' . $article)) {
//                    continue;
//                }

                $redirectTo = Redirectable::make()
                                          ->buildRedirectToByModel($article);

                $insertData[] = [
                    'share_key' => md5($redirectTo),
                    'redirect_to' => $redirectTo,
                    'share_title' => $article->share_config['title'],
                    'share_summary' => $article->share_config['summary'],
                    'share_img' => $article->share_config['share_img'],
                ];

//                Cache::put('share_config_article_' . $article->id, 1);
            }

            \Kanchangzhou\System\Models\ShareConfig::insert($insertData);
        });

        $this->info('========== Live ==========');
        \Kanchangzhou\Live\Models\Live::chunk(100, function ($lives, $page) {
            $this->info("=============  Page {$page} ============");
            $insertData = [];
            foreach ($lives as $live) {
                $redirectTo = Redirectable::make()
                                          ->buildRedirectToByRedirectable($live->homeItemRedirectable()
                                                                               ->toArray());

                $insertData[] = [
                    'share_key' => md5($redirectTo),
                    'redirect_to' => $redirectTo,
                    'share_title' => $live->share_config['title'],
                    'share_summary' => $live->share_config['summary'],
                    'share_img' => $live->share_config['share_img'],
                ];
            }
            \Kanchangzhou\System\Models\ShareConfig::insert($insertData);
        });

        $this->info('========== Widget ==========');
        \Kanchangzhou\HomePage\Models\HomeWidgetItem::chunk(100, function ($items, $page) {
            $this->info("=============  Page {$page} ============");
            $insertData = [];
            foreach ($items as $item) {
                $redirectTo = Redirectable::make()
                                          ->buildRedirectToByRedirectable($item->redirectable);

                $insertData[] = [
                    'share_key' => md5($redirectTo),
                    'redirect_to' => $redirectTo,
                    'share_title' => $item->share_config['title'],
                    'share_summary' => $item->share_config['summary'],
                    'share_img' => $item->share_config['share_img'],
                ];
            }
            \Kanchangzhou\System\Models\ShareConfig::insert($insertData);
        });

        $this->info('========== Public Service ==========');
        \Kanchangzhou\PublicService\Models\PublicService::chunk(100, function ($items, $page) {
            $this->info("=============  Page {$page} ============");
            $insertData = [];
            foreach ($items as $item) {
                $redirectTo = Redirectable::make()
                                          ->buildRedirectToByRedirectable($item->redirectable);

                $insertData[] = [
                    'share_key' => md5($redirectTo),
                    'redirect_to' => $redirectTo,
                    'share_title' => $item->share_config['title'],
                    'share_summary' => $item->share_config['summary'],
                    'share_img' => $item->share_config['share_img'],
                ];
            }
            \Kanchangzhou\System\Models\ShareConfig::insert($insertData);
        });
    }
}
