<?php

namespace Kanchangzhou\AI\Services\Audio;

use Illuminate\Support\Arr;
use Kanchangzhou\AI\Exceptions\AiAudioException;
use Kanchangzhou\AI\Services\Audio\AiAudioClientFactory;


class AiAudioTtsManager
{
    protected $clients = [];

    protected $factroy;

    protected $defaultClient = 'aliyun';

    public function __construct() {
        $this->factroy = new AiAudioClientFactory();
    }


    public function client($name = null) {
        $name = $this->parseClientName($name);

        if (!isset($this->clients[$name])) {
            $this->clients[$name] = $this->makeClient($name);
        }

        return $this->clients[$name];
    }

    public function parseClientName($name) {
        return $name ?: $this->getDefaultClient();
    }

    public function getDefaultClient() {
        return $this->defaultClient;
    }

    public function setDefaultClient($name) {
        $this->defaultClient = $name;

        return $this;
    }

    protected function configuration($name) {
        $name = $name ?: $this->getDefaultClient();

        $clients = config('kai.tts.providers');

        if (is_null($config = Arr::get($clients, $name))) {
            throw new AiAudioException(AiAudioException::INVALID_CONFIG);
        }

        return $config;
    }

    protected function makeClient($name) {
        $config = $this->configuration($name);

        return $this->factroy->make($config);
    }
}
