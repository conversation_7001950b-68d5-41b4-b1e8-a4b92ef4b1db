<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiSummary
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $title 标题
 * @property string|null $summary 摘要
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary whereSummary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiSummary whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiSummary
 */
class MediaAiSummary extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }
}
