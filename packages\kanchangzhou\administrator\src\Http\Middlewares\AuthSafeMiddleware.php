<?php

namespace Kanchangzhou\Administrator\Http\Middlewares;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Kanchangzhou\Administrator\Supports\AuthSafe;
use Kanchangzhou\Auth\Facades\AuthFacade;

class AuthSafeMiddleware
{

    protected $auth;

    public function __construct(Auth $auth) {
        $this->auth = $auth;
    }

    /**
     * @param $request
     * @param Closure $next
     *
     * @return mixed
     * @throws \Kanchangzhou\Administrator\Exceptions\AuthSafeException
     */
    public function handle($request, Closure $next) {
        if (AuthFacade::adminUser() && !$this->shouldPass($request) && AuthSafe::isEnable()) {

            AuthSafe::loginTimeOut(AuthFacade::adminUser()
                                             ->getId(), true);
        }

        AuthSafe::loginTimeOut(AuthFacade::adminUser()
                                         ->getId());

        return $next($request);
    }

    protected function shouldPass($request) {
        $excepts = [
//            'api/v1/admin/login',
            'api/v1/admin/logout',
            'api/v1/admin/my/edit',
        ];

        return collect($excepts)->contains(function ($item) use ($request) {
            if ($item !== '/') {
                $item = trim($item, '/');
            }

            return $request->is($item);
        });
    }
}
