<?php

namespace Kanchangzhou\Administrator\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminDepartmentEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $adminDepartment;

    /**
     * AdminDepartmentEvent constructor.
     *
     * @param $adminDepartment
     */
    public function __construct($adminDepartment) {
        $this->adminDepartment = $adminDepartment;
    }

    /**
     * @return mixed
     */
    public function getAdminDepartment() {
        return $this->adminDepartment;
    }


    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn() {
        return new PrivateChannel('channel-name');
    }
}
