{"name": "changguan/sdk", "description": "Changguan Platform SDK", "type": "library", "license": "MIT", "authors": [{"name": "Changguan Team", "email": "<EMAIL>"}], "minimum-stability": "stable", "require": {"php": "^7.2||^8.1", "guzzlehttp/guzzle": "^6.0|^7.0", "lpilp/guomi": "^2.0", "ext-json": "*", "ext-openssl": "*"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.0", "mockery/mockery": "^1.0"}, "suggest": {"illuminate/http": "Required for Laravel integration if using Tools class with Request facade", "symfony/http-foundation": "Required for Symfony integration if using Tools class with Request"}, "autoload": {"psr-4": {"Changguan\\SDK\\": "src/"}}, "autoload-dev": {"psr-4": {}}}