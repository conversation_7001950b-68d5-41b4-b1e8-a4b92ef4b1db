<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->text('reasoning_content')
                  ->after('message')
                  ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('reasoning_content');
        });
    }
};
