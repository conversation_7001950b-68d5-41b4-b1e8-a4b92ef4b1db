# Changguan Laravel SDK

Laravel 框架的常观 SDK 扩展包。

## 要求

- PHP >= 8.0
- Laravel >= 8.0
- [changguan/sdk ^1.0](https://gitee.com/zhong5w/changguan-sdk)

## 安装

通过 Composer 安装:

```bash
composer config repositories.changguan/<NAME_EMAIL>:zhong5w/changguan-sdk.git

composer config repositories.changguan/laravel-<NAME_EMAIL>:zhong5w/changguan-laravel-sdk.git

composer require changguan/laravel-sdk
```

## 配置

### 注册服务提供者

> Laravel 5.5+ 会自动注册服务提供者和 Facade

如果需要手动注册，请在 `config/app.php` 中添加：

```php
'providers' => [
    // ...
    Changguan\Laravel\ChangguanServiceProvider::class,
],

'aliases' => [
    // ...
    'Changguan' => Changguan\Laravel\Facades\Changguan::class,
],
```

### 发布配置文件

```bash
php artisan vendor:publish --tag=changguan-config
```

这将会在 `config` 目录下创建 `changguan.php` 配置文件。

### 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 默认项目
CHANGGUAN_PROJECT=main

# 主项目配置
CHANGGUAN_CLIENT_ID=your-client-id
CHANGGUAN_ACCESS_KEY=your-access-key
CHANGGUAN_ACCESS_SECRET=your-access-secret
CHANGGUAN_PUSH_URL=https://api.changguan.com
CHANGGUAN_OAUTH_URL=https://oauth.changguan.com

# 其他项目配置（如需要）
CHANGGUAN_PROJECT2_CLIENT_ID=project2-client-id
CHANGGUAN_PROJECT2_ACCESS_KEY=project2-access-key
CHANGGUAN_PROJECT2_ACCESS_SECRET=project2-access-secret
```

## 使用方法

### 基本使用

```php
// 使用默认项目
Changguan::push()
    ->withUser('user123')
    ->withTemplate('template1')
    ->pushToUser();

// 使用加密服务
$encrypted = Changguan::crypto()->sm2Encrypt('hello world');

// 验证 JWT
$token = '...';
$decoded = Changguan::jwt()->decode($token);

// 使用签名验证
$isValid = Changguan::signature()
    ->verifySignature($data, $signature);

// 获取设备信息
$deviceInfo = Changguan::tools()->getDeviceInfo();

// 创建跳转链接
$url = Changguan::redirect()
    ->rongmei('article', '123');
```

### 多项目支持

```php
// 切换到其他项目
Changguan::project('project2')
    ->push()
    ->withUser('user456')
    ->pushToUser();

// 获取当前项目名称
$currentProject = Changguan::getCurrentProject();
```

### 完整的配置文件示例

```php
return [
    // 默认项目
    'default' => env('CHANGGUAN_PROJECT', 'main'),

    // 项目配置
    'projects' => [
        'main' => [
            'client_id' => env('CHANGGUAN_CLIENT_ID'),
            'client_access_key' => env('CHANGGUAN_ACCESS_KEY'),
            'client_access_secret' => env('CHANGGUAN_ACCESS_SECRET'),
            'push_base_url' => env('CHANGGUAN_PUSH_URL', 'https://api.changguan.com'),
            'oauth_base_url' => env('CHANGGUAN_OAUTH_URL', 'https://oauth.changguan.com'),
        ],
        'project2' => [
            'client_id' => env('CHANGGUAN_PROJECT2_CLIENT_ID'),
            'client_access_key' => env('CHANGGUAN_PROJECT2_ACCESS_KEY'),
            'client_access_secret' => env('CHANGGUAN_PROJECT2_ACCESS_SECRET'),
            'push_base_url' => env('CHANGGUAN_PROJECT2_PUSH_URL'),
            'oauth_base_url' => env('CHANGGUAN_PROJECT2_OAUTH_URL'),
        ],
    ],
];
```

## 可用服务

SDK 提供以下主要服务：

- `push()` - 消息推送服务
- `crypto()` - 加密服务（SM2/SM3）
- `jwt()` - JWT 解码和验证
- `signature()` - 签名验证
- `tools()` - HTTP工具集
- `redirect()` - 导航链接构建
- `oauth()` - OAuth2 授权服务
- `httpClient()` - 常观 HTTP 客户端

## 提示

- 所有配置值建议使用环境变量
- 可以根据需要配置多个项目
- 确保敏感信息（如密钥）不要直接写在配置文件中
- 使用 Facade 可以获得 IDE 自动完成支持

