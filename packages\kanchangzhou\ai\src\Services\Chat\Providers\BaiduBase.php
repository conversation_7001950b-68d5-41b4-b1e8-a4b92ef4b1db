<?php

namespace Kanchangzhou\AI\Services\Chat\Providers;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatResponse;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatService;

class BaiduBase extends AIChatService
{
    protected $serviceProvider = '百度云';
    protected $uri = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro";

    protected $bufferData;

    public function handler(): AIChatResponse {
        $body = [
            'messages' => $this->messageWithHistory(),
            'stream' => false,
            'system' => $this->getRoleSystemPrompt(),
            'temperature' => $this->chatModel?->temperature ?? 0.8,
            'penalty_score' => $this->chatModel?->penalty ?? 1.0,
        ];

        $res = Http::acceptJson()
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                       ],
                   ])
                   ->post($this->chatModel?->api_gateway ?? $this->uri, $body);

        if ($res->successful()) {
            if ($res->json('error_code') == 110) {
                $res = Http::acceptJson()
                           ->withOptions([
                               'query' => [
                                   'access_token' => $this->accessToken(true),
                               ],
                           ])
                           ->post($this->chatModel?->api_gateway ?? $this->uri, $body);
            }

            if ($res->successful() && !$res->json('error_code')) {

                return new AIChatResponse([
                    'message' => $res->json('result'),
                    'chat_uuid' => $this->chatUuid,
                    'total_tokens' => $res->json('usage.total_tokens', 0),
                    'input_tokens' => $res->json('usage.prompt_tokens', 0),
                    'output_tokens' => $res->json('usage.completion_tokens', 0),
                    'source_id' => $res->json('id'),
                ]);
            }
        }

        throw new AIException(AIException::NETWORK_ERROR, [], 400, '请求失败(' . $res->json('error_msg') . ')');
    }

    public function streamHandler() {
        $body = [
            'messages' => $this->messageWithHistory(),
            'stream' => true,
            'system' => $this->getRoleSystemPrompt(),
            'temperature' => $this->chatModel?->temperature ?? 0.8,
            'penalty_score' => $this->chatModel?->penalty ?? 1.0,
        ];

        $returnResponse = [
            'message' => '',
            'chat_uuid' => $this->chatUuid,
            'total_tokens' => 0,
            'input_tokens' => 0,
            'output_tokens' => 0,
            'source_id' => '',
        ];

        $res = Http::acceptJson()
                   ->withHeaders([
                       'X-DashScope-SSE' => 'enable',
                       'Cache-Control' => 'no-cache',
                       'Content-Type' => 'application/json',
                       'Accept' => 'text/event-stream',
                   ])
                   ->withOptions([
                       'query' => [
                           'access_token' => $this->accessToken(),
                       ],
                       'curl' => [
                           CURLOPT_WRITEFUNCTION => function ($curl, $data) use (&$returnResponse) {
                               return $this->streamCallback($curl, $data, $returnResponse);
                           },
                       ],
                   ])
                   ->post($this->chatModel?->api_gateway ?? $this->uri, $body);

        if ($res->successful()) {
            if ($res->json('error_code') == 110) {
                $response = Http::withHeaders([
                    'X-DashScope-SSE' => 'enable',
                    'Cache-Control' => 'no-cache',
                    'Content-Type' => 'application/json',
                    'Accept' => 'text/event-stream',
                ])
                                ->withToken(config('kai.chat.providers.aliyun.api_key'))
                                ->withOptions([
                                    'query' => [
                                        'access_token' => $this->accessToken(true),
                                    ],
                                    'curl' => [
                                        CURLOPT_WRITEFUNCTION => function ($curl, $data) use (&$returnResponse) {
                                            return $this->streamCallback($curl, $data, $returnResponse);
                                        },
                                    ],
                                ])
                                ->post($this->chatModel?->api_gateway ?? $this->uri, $body);
            }

            return new AIChatResponse($returnResponse);
        }
    }

    protected function streamCallback($curl, $data, &$returnResponse) {
//        info('baidu', [$data]);

        $buffer = $this->bufferData . $data;
        $buffer = str_replace('data: {', '{', $buffer);
        $buffer = str_replace('data: [', '[', $buffer);

        $buffer = str_replace("}\n\n{", '}[br]{', $buffer);
        $buffer = str_replace("}\n\n[", '}[br][', $buffer);

        $lines = explode('[br]', $buffer);

        foreach ($lines as $line) {
            $lineData = json_decode(trim($line), true);
            if (!is_array($lineData)) {
                $this->bufferData = $line;
                break;
            }

            if ($lineData['error_code'] ?? 0) {
                echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                echo "event:error" . PHP_EOL;
                echo "data: " . json_encode([
                        'error_code' => $lineData['error_code'],
                        'message' => $lineData['error_msg'],
                    ]) . PHP_EOL . PHP_EOL;
                flush();

                $this->bufferData = '';

                $returnResponse['message'] = $lineData['error_msg'];
                $returnResponse['is_error'] = true;

                break;
            }

            echo "id:" . (microtime(true) * 10000) . PHP_EOL;
            echo "event:message" . PHP_EOL;
            echo "data:" . json_encode([
                    'time' => microtime(true),
                    'message' => $lineData['result'],
                    'finish_reason' => $lineData['finish_reason'],
                ]) . PHP_EOL . PHP_EOL;
            flush();

            $returnResponse['message'] .= $lineData['result'];
            $returnResponse['total_tokens'] = $lineData['usage']['total_tokens'] ?? 0;
            $returnResponse['input_tokens'] = $lineData['usage']['prompt_tokens'] ?? 0;
            $returnResponse['output_tokens'] += $lineData['usage']['completion_tokens'] ?? 0;
            $returnResponse['source_id'] = $lineData['id'] ?? '';

            $this->bufferData = '';
        }

        return strlen($data);
    }

    protected function accessToken($isForce = false) {
        // 获取accessToken, 缓存时间29天, 如果缓存时间不足, 则重新获取, 增加是否强制在线获取, 该方法直接返回accessToken值.
        // 该方法需要在config/kai.php中配置baidu.ai.app_id和baidu.ai.api_key和baidu.ai.secret_key

        $accessToken = cache()->get('baidu_ai_access_token');
        if (!$accessToken || $isForce) {
            $res = Http::acceptJson()
                       ->withOptions([
                           'query' => [
                               'grant_type' => 'client_credentials',
                               'client_id' => config('kai.chat.providers.baidu.client_id'),
                               'client_secret' => config('kai.chat.providers.baidu.client_secret'),
                           ],
                       ])
                       ->post('https://aip.baidubce.com/oauth/2.0/token');

            if ($res->successful()) {
                $accessToken = $res->json('access_token');
                cache()->put('baidu_ai_access_token', $accessToken, 29 * 24 * 60);
            }
        }

        return $accessToken;
    }

    public function uploadFile(Request $request) {
        // TODO: Implement uploadFile() method.
    }
}
