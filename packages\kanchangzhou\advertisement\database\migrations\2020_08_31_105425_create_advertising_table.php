<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdvertisingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('advertising', function (Blueprint $table) {
            $table->id();
            $table->string("title", 20)
                  ->comment("广告标题");
            $table->bigInteger("advertisers_id")
                  ->comment("广告商ID号");
            $table->bigInteger("advertising_position_id")
                  ->comment("广告位ID号");
            $table->integer("status")
                  ->comment("状态");
            $table->string("username")
                  ->comment("添加人");
            $table->string("thumb_img")
                  ->comment("缩略图");
            $table->dateTime("start_show_time")
                  ->comment("开始显示时间");
            $table->dateTime("stop_show_time")
                  ->comment("结束显示时间");
            $table->string("bz")
                  ->comment("备注信息");
            $table->integer("type")
                  ->comment("广告类型 0为图片 1为动图 2为视频");
            $table->integer("style")
                  ->comment("广告样式 0为全屏显示 1为非全屏显示");
            $table->string("redirect_to", 1000)
                  ->comment("跳转地址");
            $table->integer("power")
                  ->comment("权重");
            $table->integer("clickNum")
                  ->default(0)
                  ->comment("点击量");
            $table->integer("serve_time")
                  ->default(0)
                  ->comment("投放时长");
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('advertising');
    }
}
