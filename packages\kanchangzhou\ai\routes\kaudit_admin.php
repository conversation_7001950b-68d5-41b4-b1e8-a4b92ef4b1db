<?php

use Illuminate\Support\Facades\Route;
use Kanchangzhou\AI\Http\AdminControllers\{KeywordsController,
    NLPController,
    ContentAuditController,
};


Route::prefix('api/v1/admin/content-audit')
     ->middleware([
         'api',
         'auth.kadmin',
     ])
     ->group(function () {
         Route::resource('keywords', KeywordsController::class)
              ->only([
                  'index',
                  'store',
                  'destroy',
              ]);

         Route::post('nlp/correction', [
             NLPController::class,
             'textCorrection',
         ]);

         Route::get('nlp/correction-check', [
             ContentAuditController::class,
             'checkAudits',
         ]);

         Route::post('nlp/keywords', [
             NLPController::class,
             'contentKeywords',
         ]);

         Route::post('nlp/text-rewrite', [
             NLPController::class,
             'textRewrite',
         ]);

         Route::get('nlp/form-options', [
             NLPController::class,
             'formOptions',
         ]);

         Route::get('nlp/white-and-black', [
             NLPController::class,
             'textCorrectionWordsList',
         ]);

         Route::post('nlp/white-and-black', [
             NLPController::class,
             'textCorrectionWords',
         ]);

         Route::delete('nlp/white-and-black/{id}', [
             NLPController::class,
             'delTextCorrectionWord',
         ]);

         Route::get('get-oss-policy', [
             ContentAuditController::class,
             'getOssPolicy',
         ]);

         Route::get('video-moderation', [
             ContentAuditController::class,
             'videoModerationList',
         ]);

         Route::get('video-moderation/show/{videoId}', [
             ContentAuditController::class,
             'videoModerationShow',
         ]);

         Route::get('video-moderation/result/{videoId}', [
             ContentAuditController::class,
             'getVideoModerationResult',
         ]);

     });


Route::prefix('api/v1/admin/third')
     ->middleware([
         'api',
         'third.signature',
     ])
     ->group(function () {
         Route::post('content-audit/text-scan', [
             ContentAuditController::class,
             'audit',
         ]);

     });


Route::prefix('api/v1/miniapp/content-audit')
     ->middleware([
         'api',
         'review.miniapp',
         'review.membercheck',
     ])
     ->as('miniapp.review-center.')
     ->group(function () {
         Route::post('nlp/correction', [
             NLPController::class,
             'textCorrection',
         ]);

         Route::post('nlp/keywords', [
             NLPController::class,
             'contentKeywords',
         ]);
     });
