<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('ai_model_prices', function (Blueprint $table) {
            $table->id();
            $table->string('provider');
            $table->string('model_id');
            $table->string('model_type');
            $table->decimal('price', 10, 6)
                  ->default(0)
                  ->comment('价格');
            $table->integer('unit_count')
                  ->default(1)
                  ->comment('单位数量');
            $table->unsignedTinyInteger('is_show')
                  ->default(1)
                  ->comment('是否显示 1:显示 2:不显示');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('ai_model_prices');
    }
};
