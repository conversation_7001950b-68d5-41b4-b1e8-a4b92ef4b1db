<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiImageHistory
 *
 * @property int $id
 * @property string|null $prompt 关键词
 * @property string|null $revised_prompt 优化关键词
 * @property string|null $module 模型名称
 * @property string|null $provider 服务提供者
 * @property string|null $style 图片风格
 * @property float|null $width 图片宽度
 * @property float|null $height 图片高度
 * @property int|null $image_num 图片数量
 * @property string|null $image_base64 参考图base64
 * @property string|null $image_url 参考图完整URL
 * @property string|null $change_degree 参考图影响因子
 * @property string|null $progress 图片生成总进度，进度包含2种，0为未处理完，1为处理完成
 * @property string|null $action 生成图片执行的方法
 * @property string|null $progress_data 图片生成总进度详情
 * @property string|null $price 金额
 * @property string|null $point 点数
 * @property string|null $status 有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
 * @property string|null $failed_reason ʧ??ԭ?
 * @property string|null $log_id 日志ID
 * @property string|null $task_id 任务ID
 * @property int|null $user_id 用户ID
 * @property string $guard_name
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\AiImageUrl> $aiImageUrls
 * @property-read int|null $ai_image_urls_count
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereChangeDegree($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereFailedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereImageBase64($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereImageNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereLogId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereModule($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereProgress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereProgressData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory wherePrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereRevisedPrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereStyle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory whereWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageHistory withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiImageHistory
 */
class AiImageHistory extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [

    ];

    protected $casts = [

    ];

    public function aiImageUrls() {
        return $this->hasMany(AiImageUrl::class, 'history_id', 'id');
    }
}
