<?php

namespace Kanchangzhou\Advertisement\Http\Controllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingException;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingPositionException;
use Kanchangzhou\Advertisement\Http\Resources\AdvertisingResource;
use Kanchangzhou\Advertisement\Models\Advertiser;
use Kanchangzhou\Advertisement\Models\Advertising;
use Kanchangzhou\Advertisement\Exceptions\AdException;
use Kanchangzhou\Advertisement\Http\Requests\StoreEditAdvertisingPost;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Kanchangzhou\Advertisement\Models\AdvertisingPosition;
use Kanchangzhou\Kernel\Supports\Redirectable;
use Kanchangzhou\Kernel\Supports\Respond;

class AdvertisingController extends Controller
{
    /**
     * @param Request $request
     * @param $key
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function ad(Request $request, $key) {
        $adPositionInfo = AdvertisingPosition::where("key", $key)
                                             ->first();
        if (!$adPositionInfo || $adPositionInfo == null) {
            throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
        }

        $nowData = date("Y-m-d H:i:s");

        if ($adPositionInfo->type == 0) {//根据权重获取广告数据
            $tmpAd = Advertising::where("advertising_position_id", $adPositionInfo->id)
                                ->where("start_show_time", "<", $nowData)
                                ->where("stop_show_time", ">", $nowData)
                                ->where("status", 1)
                                ->get();
            $tmpAdIdArray = [];
            if (!empty($tmpAd)) {
                foreach ($tmpAd as $everyAd) {
                    for ($i = 0; $i < $everyAd->power; $i++) {
                        array_push($tmpAdIdArray, $everyAd->id);
                    }
                }
            }
            if (empty($tmpAdIdArray)) {

                return Respond::respondWithData([], AdvertisingException::OPERATION_SUCCESS);

//                throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
            }
            $idKey = array_rand($tmpAdIdArray);
            $adId = $tmpAdIdArray[$idKey];
            $adInfo = Advertising::where("id", $adId)
                                 ->get();
        }

        if ($adPositionInfo->type == 1) {//随机获取广告数据
            $tmpAd = Advertising::where("advertising_position_id", $adPositionInfo->id)
                                ->where("start_show_time", "<", $nowData)
                                ->where("stop_show_time", ">", $nowData)
                                ->where("status", 1)
                                ->get();
            $tmpAdIdArray = [];
            if (!empty($tmpAd)) {
                foreach ($tmpAd as $everyAd) {
                    array_push($tmpAdIdArray, $everyAd->id);
                }
            }
            if (empty($tmpAdIdArray)) {
                return Respond::respondWithData([], AdvertisingException::OPERATION_SUCCESS);
//                throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
            }
            $idKey = array_rand($tmpAdIdArray);
            $adId = $tmpAdIdArray[$idKey];
            $adInfo = Advertising::where("id", $adId)
                                 ->get();
        }

        if ($adPositionInfo->type == 2) {//获取所有广告数据
            $adInfo = Advertising::where("advertising_position_id", $adPositionInfo->id)
                                 ->where("start_show_time", "<", $nowData)
                                 ->where("stop_show_time", ">", $nowData)
                                 ->where("status", 1)
                                 ->get();
        }

        return Respond::respondWithData(AdvertisingResource::collection($adInfo), AdvertisingException::OPERATION_SUCCESS);
    }

    /**
     * @param Request $request
     * @param $id
     */
    public function click(Request $request, $id) {
        $advertising = Advertising::find($id);
        if (!$advertising || $advertising == null) {
            throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
        }
        $advertising->clickNum = $advertising->clickNum + 1;
        $saveRes = $advertising->save();
        if ($saveRes) {
            return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
        }
    }


    public function types() {
        return Respond::respondWithData(config("kadvertisement.advertising-type"));
    }

}
