<?php

namespace Kanchangzhou\AI\Services\Image\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AIImageException;
use Kanchangzhou\AI\Services\Image\Contacts\AIImageService;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\Auth\Facades\AuthFacade;

class Baidu extends AIImageService
{

    public function handler($action) {
        if (method_exists($this, $action)) {
            return $this->$action();
        }else{
            throw new AIException("{$this->module}不支持{$action}操作");
        }
    }

    public function txt2img() {
        $body = [
            'text' => $this->request->input('text'),
            'resolution' => $this->request->input('resolution') ?: '1024x1024',
            'style' => $this->request->input('style') ?: '写实风格',
            'num' => intval($this->request->input('num') ?: 1),
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/ernievilg/v1/txt2img", $body);

        if ($res->successful() && !$res->json('error_code')) {

            $data = [
                'module' => Str::studly(class_basename(get_class($this))),
                'provider' => __FUNCTION__,
                'prompt' => $body['text'],
                'style' => $body['style'],
                'width' => explode('*', $body['resolution'])[0],
                'height' => explode('*', $body['resolution'])[1],
                'image_num' => $body['num'],
                'log_id' => $res->json('log_id'),
                'task_id' => $res->json('data.taskId'),
            ];

            $res = $this->createHistory($data);
            $res->offsetSet('id', $res->id);

            return $res;
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，文生查询结果
    public function getImg() {
        $body = [
            'taskId' => $this->request->input('task_id'),
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                    'taskId' => $this->request->input('task_id'),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/ernievilg/v1/getImg", $body);

        if ($res->successful() && !$res->json('error_code')) {

            $images = [];
            $imgUrls = $res->json('data.imgUrls');
            if($imgUrls && is_array($imgUrls)){
                foreach($imgUrls as $k=>$v){
                    $images[]['image_orgin'] = $v['image'];
                    //TODO图片本地化处理
                }
            }

            //有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
            $status = 'INIT';
            if($res->json('data.status') == 1){
                $status = 'SUCCESS';
            }

            $data = [
                'task_id' => $res->json('data.taskId'),
                'log_id' => $res->json('log_id'),
                'result_list' => $images,
                'waiting' => $res->json('data.waiting'),
                'status' => $status,    //RUNNING | SUCCEEDED | FAILED
            ];

            return $this->updateHistory($data);
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    public function txt2imgv2() {

        ChatLimit::imageCheckLimit();

        $body = [
            'prompt' => $this->request->input('prompt'),
            'width' => intval($this->request->input('width')) ?: 512,
            'height' => intval($this->request->input('height')) ?: 512,
            'image_num' => intval($this->request->input('image_num')) ?: 1,
            'url' => $this->request->input('url'),
            'image' => $this->request->input('image'),
            'change_degree' => intval($this->request->input('change_degree')) ?: 1,
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(true),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/ernievilg/v1/txt2imgv2", $body);

        if ($res->successful() && !$res->json('error_code')) {

            //基础尺寸：512x512、640x360、360x640、1024x1024、1280x720、720x1280
            //高级尺寸：2048x2048、2560x1440、1440x2560
            //基础尺寸 3（点/张）
            //高级尺寸 6（点/张）

            $basePonit = 3; //点数
            $basePirce = 0.18;  //单价
            $multiple =  $body['width'] >= 1440 ? 2 : 1;
            $point = $multiple * $body['image_num'];   //计算张数
            $price = $point * $basePonit * $basePirce;   //计算价格

            // 计量使用张数
            ChatLimit::setProvider('Baidu')->setModule('txt2imgv2','AIImage')->imageIncrement($point);

            $data = [
                'module' => Str::studly(class_basename(get_class($this))),
                'provider' => __FUNCTION__,
                'prompt' => $body['prompt'],
                'width' => $body['width'],
                'height' => $body['height'],
                'image_num' => $body['image_num'],
                'image_url' => $body['url'],
                'image_base64' => $body['image'],
                'change_degree' => $body['change_degree'],
                'progress' => 0,

                'point' => $point,
                'price' => $price,

                'status' => 'INIT',  //有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
                'log_id' => $res->json('log_id'),
                'task_id' => $res->json('data.task_id'),
            ];

            $res = $this->createHistory($data);
            $res->offsetSet('id', $res->id);

            return $res;
        }else{
            //throw new \Exception($res->json('error_msg'));
            throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,__($res->json('error_msg')));
        }
    }

    //百度人工智能，文生查询结果
    public function getImgv2() {
        $body = [
            'task_id' => $this->request->input('task_id'),
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                    'taskId' => $this->request->input('task_id'),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/ernievilg/v1/getImgv2", $body);

        if ($res->successful() && !$res->json('error_code')) {

            $list = $res->json();
            $return = [
                'log_id' => $list['log_id'],
                'task_id' => $body['task_id'],  //任务ID
                'status' => $list['data']['task_status'],  //有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
                'progress' => $list['data']['task_progress'] == 1 ? '100%':'0%',  //图片生成总进度，进度包含2种，0为未处理完，1为处理完成
                'result_list' => [],
            ];

            if ($list['data']['task_status'] == "SUCCESS") {
                foreach($list['data']['sub_task_result_list'] as $key=>$item){

                    if($item['sub_task_status'] != 'SUCCESS'){
                        continue;
                    }

                    if(!isset($item['final_image_list'][0]['img_url'])){
                        continue;
                    }

                    $image_orgin = $item['final_image_list'][0]['img_url'];

                    $response = Http::get($image_orgin);
                    $contentType = $response->header('Content-Type');
                    if (str_starts_with($contentType, 'application/json')) {
                        // 图片过期，不予显示
                        //throw new \Exception($response->json('message'));
                        throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,$response->json('message') );
                    }

                    //$image_name = \Str::uuid();
                    $image_name = $return['task_id'].'-'.($key+1);
                    $path = 'ai-images'.'/'.date('Ymd').'/'.$image_name;
                    //判断图片是否存在
                    if (!\Storage::disk('oss')->exists($path)) {
                        \Storage::disk('oss')->putRemoteFile($path, $image_orgin);
                    }
                    $image_local = \Storage::disk('oss')->url($path);

                    $return['result_list'][] = [
                        'image_orgin' => $image_orgin,   //图片所在 BOS http 地址，默认 24 小时失效
                        'image_local' => $image_local,
                        'approve_conclusion' => $item['final_image_list'][0]['img_approve_conclusion'], //图片机审结果，"block"：输出图片违规；"review": 输出图片疑似违规；"pass": 输出图片未发现问题；
                        'error_code' => $item['sub_task_error_code'],  //单风格任务错误码。0:正常；501:文本黄反拦截；201:模型生图失败
                        'status' => $item['sub_task_status'],  //单风格图片状态。有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
                        'progress' => $item['sub_task_progress'] == 1 ? '100%':'0%',  //单任务图片生成进度，进度包含2种，0为未处理完，1为处理完成
                    ];
                }
            }

            //更新记录
            $this->updateHistory($return);

            return $return;
        }else{
            //throw new \Exception($res->json('error_msg'));
            throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,$res->json('error_msg'));
        }
    }

    //百度人工智能，图像修复
    public function inpainting() {

        $rectangle = $this->request->input('rectangle');
        if($rectangle){
            foreach ($rectangle as $key => $value) {
                $rectangle[$key] = json_decode($value, true);
            }
        }

        $body = [
            'rectangle' => $rectangle ?? [],
            'image' => $this->request->input('image'),
            'url' => $this->request->input('url'),
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/inpainting", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return [
                'image' => $res->json('image'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg')."[{$res->json('error_code')}]");
        }
    }

    //百度人工智能，图片无损放大
    public function imageQualityEnhance() {

        $body = [
            'image' => $this->request->input('image'),
            'url' => $this->request->input('url'),
        ];

        $res = Http::asForm()
            ->acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/image_quality_enhance", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return [
                'image' => $res->json('image'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，文档图片去底纹
    public function docRepair() {

        $body = [
            'image' => $this->request->input('image'),
            'url' => $this->request->input('url'),
        ];

        $res = Http::asForm()
            ->acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/doc_repair", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return [
                'image' => $res->json('result'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，图像去噪
    public function denoise() {

        $body = [
            'image' => $this->request->input('image'),
            'url' => $this->request->input('url'),
            'option' => $this->request->input('option', 100),
        ];

        $res = Http::asForm()
            ->acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rest/2.0/image-process/v1/denoise", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return[
                'image' => $res->json('result'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，logo识别-检索
    public function logoSearch() {

        $body = [
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'image' => $this->request->input('image'),
                    'url' => $this->request->input('url'),
                    'custom_lib' => $this->request->input('custom_lib'),

                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rest/2.0/image-classify/v2/logo", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return [
                'result_num' => $res->json('result_num'),
                'result' => $res->json('result'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，logo识别-入库
    public function logoAdd() {

        $body = [
            'brief' => json_encode(['name'=>$this->request->input('brief')]),
        ];

        $res = Http::asForm()
            ->acceptJson()
            ->withOptions([
                'query' => [
                    'image' => $this->request->input('image'),
                    'url' => $this->request->input('url'),
                    'brief' => json_encode(['name'=>$this->request->input('brief')]),

                    'access_token' => $this->accessToken(),
                ],
            ])

            ->post("https://aip.baidubce.com/rest/2.0/realtime_search/v1/logo/add", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return [
                'cont_sign' => $res->json('cont_sign'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg').' cont_sign:'.$res->json('cont_sign'));
        }
    }

    //百度人工智能，logo识别-删除
    public function logoDelete() {

        $body = [
            'image' => $this->request->input('image'),
            'cont_sign' => $this->request->input('cont_sign'),
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])

            ->post(
                "https://aip.baidubce.com/rest/2.0/realtime_search/v1/logo/delete",
                $body
            );

        if ($res->successful() && !$res->json('error_code')) {
            return [
                'image' => $res->json('image'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，AI成片
    public function ttv() {

        $structs = json_decode($this->request->input('source'));
        $config = json_decode($this->request->input('config'));

        $body = [
            'source' => ['structs'=>$structs],    //用于生成视频的图文内容与设置项
            'config' => $config,    //视频生产配置
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/brain/creative/ttv/material", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return [
                //'id' => $res->json('data.id'),
                'job_id' => $res->json('data.jobId'),
                'log_id' => $res->json('log_id'),
            ];
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //百度人工智能，AI成片查询结果
    public function getTtv() {
        $body = [
            'jobId' => $this->request->input('job_id'),
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/brain/creative/ttv/query", $body);

        if ($res->successful() && !$res->json('error_code')) {
            $data = $res->json('data');
            return $data;
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //文本在线合成
    public function text2audio() {
        $body = [
            'text' => $this->request->input('text'),  //总字数不超过10万个字符，1个中文字、英文字母、数字或符号均算作1个字符
            'format' => $this->request->input('format') ?? "mp3-16k",  //"mp3-16k"，"mp3-48k"，"wav"，"pcm-8k"，"pcm-16k"，默认为mp3-16k
            'voice' => intval($this->request->input('voice')) ?? 5003,  //基础音库：度小宇=1，度小美=0，度逍遥（基础）=3，度丫丫=4； 精品音库：度逍遥（精品）=5003，度小鹿=5118，度博文=106，度小童=110，度小萌=111，度米朵=103，度小娇=5。默认为度小美
            'speed' => intval($this->request->input('speed')) ?? 5,  //取值0-15，默认为5中语速
            'pitch' => intval($this->request->input('pitch')) ?? 5,  //取值0-15，默认为5中语调
            'volume' => intval($this->request->input('volume')) ?? 5,    //音量，基础音库取值0-9，精品音库取值0-15，默认为5中音量（取值为0时为音量最小值，并非为无声）
            'enable_subtitle' => intval($this->request->input('enable_subtitle')) ?? 2,  //取值范围0, 1, 2，默认为0。0表示不开启字幕时间戳，1表示开启句级别字幕时间戳，2表示开启词级别字幕时间戳
            'break' => intval($this->request->input('break')) ?? 0,  //取值 0-5000 ，单位ms，用于合成文本分段传入时设置段落间间隔。默认为0
            'lang' => 'zh',  //语言选择,填写zh
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/tts/v1/create", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return $res->json();
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //文本在线合成查询结果
    public function text2audioQuery() {
        $body = [
            'task_ids' => $this->request->input('task_ids'),  //成的文本，使用UTF-8编码。不超过60个汉字或者字母数字。文本在百度服务器内转换为GBK后，长度必须小于120字节。如需合成更长文本，推荐使用长文本在线合成
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/tts/v1/query", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return $res->json();
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //短语音识别
    public function audio2text() {
        $body = [
            'speech_url' => $this->request->input('speech_url'),    //可使用百度云对象存储进行音频存储，生成云端可外网访问的url链接，音频大小不超过500MB
            'format' => $this->request->input('format'),    //["mp3", "wav", "pcm","m4a","amr"]单声道，编码 16bits 位深
            'pid' => $this->request->input('pid') ?? 80001, //[80001（中文语音近场识别模型极速版）, 80006（中文音视频字幕模型，1737（英文模型）]
            'rate' => $this->request->input('rate') ?? 16000,   //[16000] 固定值
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/aasr/v1/create", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return $res->json();
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    //短语音识别
    public function audio2textQuery() {
        $body = [
            'task_ids' => $this->request->input('task_ids'),  //成的文本，使用UTF-8编码。不超过60个汉字或者字母数字。文本在百度服务器内转换为GBK后，长度必须小于120字节。如需合成更长文本，推荐使用长文本在线合成
        ];

        $res = Http::acceptJson()
            ->withOptions([
                'query' => [
                    'access_token' => $this->accessToken(),
                ],
            ])
            ->post("https://aip.baidubce.com/rpc/2.0/aasr/v1/query", $body);

        if ($res->successful() && !$res->json('error_code')) {
            return $res->json();
        }else{
            throw new \Exception($res->json('error_msg'));
        }
    }

    protected function accessToken($isForce = false) {
        //百度Access Token的有效期(秒为单位，有效期30天) 系统缓存有效期20天
        $accessToken = cache()->get('baidu_api_access_token');
        if (!$accessToken || $isForce) {
            $res = Http::acceptJson()
                ->withOptions([
                    'query' => [
                        'grant_type' => 'client_credentials',
                        'client_id' => config('kai.image.providers.baidu.api_key'),
                        'client_secret' => config('kai.image.providers.baidu.secret_key'),
                        //'client_id' => 'Yod0rPC4o3WeRg49A114r1sH',
                        //'client_secret' => 'Q8mLdw6k9MdinOBgCGmG45B5TRKIAUWM',
                    ],
                ])
                ->post('https://aip.baidubce.com/oauth/2.0/token');

            if ($res->successful()) {
                $accessToken = $res->json('access_token');
                cache()->put('baidu_api_access_token', $accessToken, 20 * 24 * 60);
            }
        }

        return $accessToken;
    }
}
