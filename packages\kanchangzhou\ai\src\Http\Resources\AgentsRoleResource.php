<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Hooks\PermissionHook;

class AgentsRoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request) {
        return [
            'id' => $this->role_uuid,
            'title' => $this->title,
            'icon' => $this->icon,
            'examples' => $this->examples,
            'description' => $this->description,
            $this->mergeWhen($this->owner_id == AuthFacade::adminUser()
                                                          ->getId() || PermissionHook::can('智能场景.场景审核', '', false), [
                'module_key' => $this->module_key,
                'prompt' => $this->system_prompt,
                'temperature' => $this->temperature,
                'penalty' => $this->penalty,
                'is_published' => $this->is_published,
            ]),
            'chat_histories' => ChatHistoryResource::make($this->whenLoaded('chatHistories')),
            'can_operate' => $this->owner_id == AuthFacade::adminUser()
                                                          ->getId() || PermissionHook::can('智能场景.场景审核', '', false),
        ];
    }
}
