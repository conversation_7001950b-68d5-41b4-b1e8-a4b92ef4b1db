<?php

namespace Kanchangzhou\Administrator\Observers;

use Kanchangzhou\Administrator\Events\AdminDepartmentEvent;
use Kanchangzhou\Administrator\Models\AdminDepartment;
use Kanchangzhou\Auth\Facades\AuthFacade;

class AdminDepartmentObserver
{
    /**
     * Handle the AdminDepartment "created" event.
     *
     * @param AdminDepartment $adminDepartment
     *
     * @return void
     */
    public function created(AdminDepartment $adminDepartment) {
        event(new AdminDepartmentEvent($adminDepartment));
    }

    /**
     * Handle the AdminDepartment "updated" event.
     *
     * @param AdminDepartment $adminDepartment
     *
     * @return void
     */
    public function updated(AdminDepartment $adminDepartment) {
    }

    /**
     * Handle the AdminDepartment "deleted" event.
     *
     * @param AdminDepartment $adminDepartment
     *
     * @return void
     */
    public function deleted(AdminDepartment $adminDepartment) {
    }

    /**
     * Handle the AdminDepartment "restored" event.
     *
     * @param AdminDepartment $adminDepartment
     *
     * @return void
     */
    public function restored(AdminDepartment $adminDepartment) {

    }

    /**
     * Handle the AdminDepartment "force deleted" event.
     *
     * @param AdminDepartment $adminDepartment
     *
     * @return void
     */
    public function forceDeleted(AdminDepartment $adminDepartment) {
        //
    }
}
