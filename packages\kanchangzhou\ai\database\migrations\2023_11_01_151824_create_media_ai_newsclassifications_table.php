<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiNewsclassificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('media_ai_newsclassifications', function (Blueprint $table) {
            $table->id();
            $table->integer('media_ai_file_id')
                  ->comment('文件ID');
            $table->string('catalog_a_code')
                  ->nullable()
                  ->comment('一级分类代码');
            $table->string('catalog_a_tag')
                  ->nullable()
                  ->comment('一级分类名称');
            $table->string('catalog_b_code')
                  ->nullable()
                  ->comment('二级分类代码');
            $table->string('catalog_b_tag')
                  ->nullable()
                  ->comment('二级分类名称');
            $table->string('catalog_c_code')
                  ->nullable()
                  ->comment('三级分类代码');
            $table->string('catalog_c_tag')
                  ->nullable()
                  ->comment('三级分类名称');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('media_ai_newsclassifications');
    }
}
