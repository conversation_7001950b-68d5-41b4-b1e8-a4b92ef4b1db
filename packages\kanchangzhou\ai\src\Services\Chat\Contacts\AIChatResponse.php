<?php

namespace Kanchangzhou\AI\Services\Chat\Contacts;

// 创建一个类, 用于ChatService返回的数据结构, 需要字段有: message,chat_uuid,total_tokens,input_tokens,output_tokens,source_id
// 类继承Collection, 重写toArray方法, 返回上述字段, 验证必要字段: message,chat_uuid,input_tokens,output_tokens

use Illuminate\Support\Collection;

// 写phpdoc注释, 用于IDE提示

/**
 * Class AIChatResponse
 * @package Kanchangzhou\AI\Services\Chat\Contacts
 * @property string $message 消息
 * @property string $chat_uuid 会话ID
 * @property int $total_tokens 总token数
 * @property int $input_tokens 输入token数
 * @property int $output_tokens 输出token数
 * @property int $source_id 源ID
 * @property int $message_id 系统消息ID
 * @property array $media_data 媒体数据
 */
class AIChatResponse extends Collection
{
    /**
     * @throws \Exception
     */
    public function __construct($items = []) {
        parent::__construct($items);
        $this->validate();
    }

    public function toArray() {
        return [
            'status'            => $this->get('status', true),
            'reasoning_content' => $this->get('reasoning_content', ''),
            'message'           => $this->get('message'),
            'chat_uuid'         => $this->get('chat_uuid'),
            'total_tokens'      => $this->get('total_tokens'),
            'input_tokens'      => $this->get('input_tokens'),
            'output_tokens'     => $this->get('output_tokens'),
            'source_id'         => $this->get('source_id'),
            'message_id'        => $this->get('message_id'),
            'media_data'        => $this->get('media_data', []),
            'module_name'       => $this->get('module_name'),
            'module_key'        => $this->get('module_key'),
            'pluging_info'      => $this->get('pluging_info', []),
        ];
    }

    public function validate() {
        if (!$this->has('message')) {
            throw new \Exception('message字段不存在');
        }

        if (!$this->has('chat_uuid')) {
            throw new \Exception('chat_uuid字段不存在');
        }

        //        if (!$this->has('input_tokens')) {
        //            throw new \Exception('input_tokens字段不存在');
        //        }
        //
        //        if (!$this->has('output_tokens')) {
        //            throw new \Exception('output_tokens字段不存在');
        //        }
    }

    // 直接取值, 不存在的字段返回null
    public function __get($key) {
        return $this->get($key);
    }

}
