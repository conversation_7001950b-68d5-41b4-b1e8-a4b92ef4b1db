<?php

namespace Kanchangzhou\Administrator\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Kanchangzhou\Administrator\Facades\AdministratorFacade;
use Kanchangzhou\Kernel\Events\CheckPermissionEvent;
use <PERSON><PERSON>\Permission\Models\Permission;

class CheckPermissionListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct() {
        //
    }

    /**
     * @param CheckPermissionEvent $event
     *
     * @return bool
     * @throws \Kanchangzhou\Administrator\Exceptions\NotHasPermissionException
     */
    public function handle(CheckPermissionEvent $event) {
        if (!$event->getAdminUser()) {
            return false;
        }

        if ($event->getPermission()) {
//            if (is_string($event->getPermission()) && false !== strpos($event->getPermission(), '|')) {
            $permissions = explode('|', $event->getPermission());
            foreach ($permissions as $permission) {
                Permission::findOrCreate($permission, config('kadmin.auth.guard'));
            }
//            }
        }

        switch ($event->getFunType()) {
            case CheckPermissionEvent::FUN_TYPE_CAN:
                return $this->can($event);
                break;
            case CheckPermissionEvent::FUN_TYPE_CAN_ONLY:
                return $this->canOnly($event);
                break;
        }
    }

    /**
     * @param CheckPermissionEvent $event
     *
     * @return bool
     * @throws \Kanchangzhou\Administrator\Exceptions\NotHasPermissionException
     */
    private function can(CheckPermissionEvent $event) {
        return (int)AdministratorFacade::can($event->getAdminUser(), $event->getPermission(), $event->getTargetModel(), $event->getIsthr());
    }

    /**
     * @param CheckPermissionEvent $event
     *
     * @return bool
     * @throws \Kanchangzhou\Administrator\Exceptions\NotHasPermissionException
     */
    private function canOnly(CheckPermissionEvent $event) {
        return (int)AdministratorFacade::canOnly($event->getAdminUser(), $event->getTargetModel(), $event->getIsthr());
    }
}
