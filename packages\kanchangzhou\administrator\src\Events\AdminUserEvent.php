<?php

namespace Kanchangzhou\Administrator\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminUserEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $adminUser;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($adminUser) {
        $this->adminUser = $adminUser;
    }

    /**
     * @return mixed
     */
    public function getAdminUser() {
        return $this->adminUser;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn() {
        return new PrivateChannel('channel-name');
    }
}
