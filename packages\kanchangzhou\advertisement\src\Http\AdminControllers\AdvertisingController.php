<?php

namespace Kanchangzhou\Advertisement\Http\AdminControllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingException;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingPositionException;
use Kanchangzhou\Advertisement\Http\Controllers\Controller;
use Kanchangzhou\Advertisement\Http\Resources\AdvertisingResource;
use Kanchangzhou\Advertisement\Models\Advertiser;
use Kanchangzhou\Advertisement\Models\Advertising;
use Kanchangzhou\Advertisement\Http\Requests\StoreEditAdvertisingPost;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Kanchangzhou\Advertisement\Models\AdvertisingPosition;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Supports\Redirectable;
use Kanchangzhou\Kernel\Supports\Respond;

class AdvertisingController extends Controller
{

    /**
     * @param $positionid
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws AdvertisingPositionException
     */
    public function preAdd($positionid) {
        $advertisingPositionInfo = AdvertisingPosition::find($positionid);
        if (!$advertisingPositionInfo) {
            throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
        }
        $typeInfo = config("kadvertisement.advertising-type");

        return Respond::respondWithData($typeInfo[$advertisingPositionInfo->position_type_id], AdvertisingPositionException::OPERATION_SUCCESS);//;
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(StoreEditAdvertisingPost $request, $positionid) {
        PermissionHook::can("广告.新建");
        $advertisingPositionInfo = AdvertisingPosition::find($positionid);
        if (!$advertisingPositionInfo) {
            throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
        }

        $validated = $request->validated();
        $title = $validated["title"];
        $is_show_title = $validated["is_show_title"];
        $advertisers_id = $validated["advertisers_id"];
        $advertising_position_id = $positionid;//$validated["advertising_position_id"];
        $status = $validated["status"];
        $username = $validated["username"];
        $thumb_img = isset($validated["thumb_img"]) && $validated["thumb_img"] != "" ? $validated["thumb_img"] : [];
        $start_show_time = $validated["start_show_time"];
        $stop_show_time = $validated["stop_show_time"];
        $bz = isset($validated["bz"]) && $validated["bz"] != "" ? $validated["bz"] : "";
        $type = $validated["type"];
        $style = $validated["style"];
        $redirect_to = Redirectable::make()
                                   ->buildRedirectToByRequest($request);
        $power = $validated["power"];
        $serve_time = $validated["serve_time"];
        $media_type = $validated["media_type"];
        $display_times = $validated["display_times"];

        $nowTimeStamp = date("Y-m-d H:i:s");
        $hasAdvertising = Advertising::where("title", $title)
                                     ->where("start_show_time", "<=", $nowTimeStamp)
                                     ->where("stop_show_time", ">=", $nowTimeStamp)
                                     ->first();
        if ($hasAdvertising) {
            throw new AdvertisingException(AdvertisingException::HAS_EXISTS);
        }

        $advertising = new Advertising();
        $advertising->title = $title;
        $advertising->is_show_title = $is_show_title;
        $advertising->advertisers_id = $advertisers_id;
        $advertising->advertising_position_id = $advertising_position_id;
        $advertising->status = $status;
        $advertising->username = $username;
        $advertising->thumb_img = $thumb_img;
        $advertising->start_show_time = $start_show_time;
        $advertising->stop_show_time = $stop_show_time;
        $advertising->bz = $bz;
        $advertising->type = $type;
        $advertising->style = $style;
        $advertising->redirect_to = $redirect_to;
        $advertising->power = $power;
        $advertising->serve_time = $serve_time;
        $advertising->media_type = $media_type;
        $advertising->display_times = $display_times;

        $insertRes = $advertising->save();
        if ($insertRes) {
            return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, $id) {
        PermissionHook::can("广告.删除");
        $advertising = Advertising::find($id);
        if (!$advertising || $advertising == null) {
            throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
        }
        $deleteRes = $advertising->delete();
        if ($deleteRes) {
            return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @param $updown
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updown(Request $request, $id, $updown) {
        PermissionHook::can("广告.上下架");
        $idArray = explode(",", $id);
        if (!empty($idArray)) {
            DB::beginTransaction();//开启事务
            try {
                foreach ($idArray as $everyId) {
                    $advertising = Advertising::find($everyId);
                    if (!$advertising || $advertising == null) {
                        throw new AdvertisingException(AdvertisingException::HAS_EXISTS);
                    }
                    $advertising->status = $updown;
                    $saveRes = $advertising->save();
                    if (!$saveRes) {
                        throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
                    }
                }
                DB::commit();//提交至数据库

                return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
            } catch (\Exception $err) {
                DB::rollback();//数据库回滚
                $message = json_decode($err->getMessage());
                throw (new AdvertisingException($message));
            }
        }
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function copy(Request $request, $id) {
        PermissionHook::can("广告.拷贝");
        $oldAdvertising = Advertising::find($id);
        if (!$oldAdvertising || $oldAdvertising == null) {
            throw new AdvertisingException(AdvertisingException::HAS_EXISTS);
        }

        $advertising = new Advertising();
        $advertising->title = $oldAdvertising->title;
        $advertising->is_show_title = $oldAdvertising->is_show_title;
        $advertising->advertisers_id = $oldAdvertising->advertisers_id;
        $advertising->advertising_position_id = $oldAdvertising->advertising_position_id;
        $advertising->status = $oldAdvertising->status;
        $advertising->username = $oldAdvertising->username;
        $advertising->thumb_img = $oldAdvertising->thumb_img;
        $advertising->start_show_time = $oldAdvertising->start_show_time;
        $advertising->stop_show_time = $oldAdvertising->stop_show_time;
        $advertising->bz = $oldAdvertising->bz;
        $advertising->type = $oldAdvertising->type;
        $advertising->style = $oldAdvertising->style;
        $advertising->redirect_to = $oldAdvertising->redirect_to;
        $advertising->power = $oldAdvertising->power;
        $advertising->clickNum = 0;
        $advertising->serve_time = $oldAdvertising->serve_time;
        $advertising->display_times = $oldAdvertising->display_times;
        $insertRes = $advertising->save();
        if ($insertRes) {
            return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
        }
    }


    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(StoreEditAdvertisingPost $request, $id) {
        PermissionHook::can("广告.编辑");
        $validated = $request->validated();
        $title = $validated["title"];
        $is_show_title = $validated["is_show_title"];
        $advertisers_id = $validated["advertisers_id"];
//        $advertising_position_id = $validated["advertising_position_id"];
        $status = $validated["status"];
        $username = $validated["username"];
        $thumb_img = isset($validated["thumb_img"]) && $validated["thumb_img"] != "" ? $validated["thumb_img"] : "";//$validated["thumb_img"];
        $start_show_time = $validated["start_show_time"];
        $stop_show_time = $validated["stop_show_time"];
        $bz = isset($validated["bz"]) && $validated["bz"] != "" ? $validated["bz"] : "";//$validated["bz"];
        $type = $validated["type"];
        $style = $validated["style"];
        $redirect_to = Redirectable::make()
                                   ->buildRedirectToByRequest($request);
        $power = $validated["power"];
        $clickNum = $validated["clickNum"];
        $serve_time = $validated["serve_time"];
        $media_type = $validated["media_type"];
        $display_times = $validated["display_times"];

        $nowTimeStamp = date("Y-m-d H:i:s");
        $hasAdvertising = Advertising::where("title", $title)
                                     ->where("start_show_time", "<=", $nowTimeStamp)
                                     ->where("stop_show_time", ">=", $nowTimeStamp)
                                     ->first();
        if ($hasAdvertising) {
            if ($id != $hasAdvertising->id) {
                throw new AdvertisingException(AdvertisingException::HAS_EXISTS);
            }

        }

        $advertising = Advertising::find($id);
        if (!$advertising || $advertising == null) {
            throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
        }

        $advertising->title = $title;
        $advertising->is_show_title = $is_show_title;
        $advertising->advertisers_id = $advertisers_id;
//        $advertising->advertising_position_id = $advertising_position_id;
        $advertising->status = $status;
        $advertising->username = $username;
        $advertising->thumb_img = $thumb_img;
        $advertising->start_show_time = $start_show_time;
        $advertising->stop_show_time = $stop_show_time;
        $advertising->bz = $bz;
        $advertising->type = $type;
        $advertising->style = $style;
        $advertising->redirect_to = $redirect_to;
        $advertising->power = $power;
        $advertising->clickNum = $clickNum;
        $advertising->serve_time = $serve_time;
        $advertising->media_type = $media_type;
        $advertising->display_times = $display_times;

        $saveRes = $advertising->save();
        if ($saveRes) {
            return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
        } else {
            throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail(Request $request, $id) {
        PermissionHook::can("广告.详情");
        $advertising = Advertising::find($id);
        if (!$advertising || $advertising == null) {
            throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
        }

        return Respond::respondWithData(AdvertisingResource::make($advertising), AdvertisingException::OPERATION_SUCCESS);
    }


    /**
     * @param Request $request
     * @param $pagesize
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request) {
        PermissionHook::can("广告.列表");
        $title = $request->title;
        $advertisers_name = $request->advertisers_name;
        $advertising_position_id = $request->advertising_position_id;
        $status = $request->status;
        $username = $request->username;

        $allAdvertiser = Advertiser::where("advertiser_name", "like", "%" . $advertisers_name . "%")
                                   ->get();
        $advIdArray = [];
        if (!empty($allAdvertiser)) {
            foreach ($allAdvertiser as $everyAdvertiser) {
                array_push($advIdArray, $everyAdvertiser->id);
            }
        }

        $list = Advertising::with("advertiserInfo")
                           ->with("advertisingPositionInfo")
                           ->when($title, function ($query, $title) {
                               return $query->where("title", "like", "%" . $title . "%");
                           })
                           ->when($advIdArray, function ($query, $advIdArray) {
                               return $query->whereIn("advertisers_id", $advIdArray);
                           })
                           ->when($advertising_position_id, function ($query, $advertising_position_id) {
                               return $query->where("advertising_position_id", $advertising_position_id);
                           })
                           ->when($status, function ($query, $status) {
                               return $query->where("status", $status);
                           })
                           ->when($username, function ($query, $username) {
                               return $query->where("username", "like", "%" . $username . "%");
                           })
                           ->orderBy("id", "desc")
                           ->paginate(10);

        return Respond::respondWithData(AdvertisingResource::collection($list), AdvertisingException::OPERATION_SUCCESS);

    }

    public function search(Request $request) {
        $title = $request->title;
        $advertisers_name = $request->advertisers_name;
        $advertising_position_id = $request->advertising_position_id;
        $status = $request->status;
        $username = $request->username;

        $allAdvertiser = Advertiser::where("advertiser_name", "like", $advertisers_name . "%")
                                   ->get();
        $advIdArray = [];
        if (!empty($allAdvertiser)) {
            foreach ($allAdvertiser as $everyAdvertiser) {
                array_push($advIdArray, $everyAdvertiser->id);
            }
        }

        $list = Advertising::with("advertiserInfo")
                           ->with("advertisingPositionInfo")
                           ->when($title, function ($query, $title) {
                               return $query->where("title", "like", $title . "%");
                           })
                           ->when($advIdArray, function ($query, $advIdArray) {
                               return $query->whereIn("advertisers_id", $advIdArray);
                           })
                           ->when($advertising_position_id, function ($query, $advertising_position_id) {
                               return $query->where("advertising_position_id", $advertising_position_id);
                           })
                           ->when($status, function ($query, $status) {
                               return $query->where("status", $status);
                           })
                           ->when($username, function ($query, $username) {
                               return $query->where("username", "like", $username . "%");
                           })
                           ->paginate(10);

//        print_r(DB::getQueryLog());


        return Respond::respondWithData(AdvertisingResource::collection($list), AdvertisingException::OPERATION_SUCCESS);
    }

    public function types() {
        return Respond::respondWithData(config("kadvertisement.advertising-type"));
    }

//    public function ad(Request $request, $key){
//        $adPositionInfo = AdvertisingPosition::where("key", $key)->first();
//        if(!$adPositionInfo || $adPositionInfo == null){
//
//            throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
//        }
//        if($adPositionInfo->type == 0){//根据权重获取广告数据
//            $tmpAd = Advertising::where("advertising_position_id", $adPositionInfo->id)->get();
//            $tmpAdIdArray = [];
//            if(!empty($tmpAd)){
//                foreach($tmpAd as $everyAd){
//                    for($i =0; $i < $everyAd->power; $i++){
//                        array_push($tmpAdIdArray, $everyAd->id);
//                    }
//                }
//            }
//            $idKey = array_rand($tmpAdIdArray);
//            $adId = $tmpAdIdArray[$idKey];
//            $adInfo = Advertising::where("id", $adId)->get();
//        }
//
//        if($adPositionInfo->type == 1){//随机获取广告数据
//            $tmpAd = Advertising::where("advertising_position_id", $adPositionInfo->id)->get();
//            $tmpAdIdArray = [];
//            if(!empty($tmpAd)){
//                foreach($tmpAd as $everyAd){
//                    array_push($tmpAdIdArray, $everyAd->id);
//                }
//            }
//            $idKey = array_rand($tmpAdIdArray);
//            $adId = $tmpAdIdArray[$idKey];
//            $adInfo = Advertising::where("id", $adId)->get();
//        }
//
//        if($adPositionInfo->type == 2){//获取所有广告数据
//            $adInfo = Advertising::where("advertising_position_id", $adPositionInfo->id)->get();
//        }
//
//
//        return Respond::respondWithData(AdvertisingResource::collection($adInfo), AdvertisingException::OPERATION_SUCCESS);
//    }
//
//    /**
//     * @param Request $request
//     * @param $id
//     */
//    public function click(Request $request, $id){
//        $advertising = Advertising::find($id);
//        if(!$advertising || $advertising == null){
//            throw new AdvertisingException(AdvertisingException::NOT_EXISTS);
//        }
//        $advertising->clickNum = $advertising->clickNum + 1;
//        $saveRes = $advertising->save();
//        if($saveRes){
//            return Respond::respondWithData("operation success", AdvertisingException::OPERATION_SUCCESS);
//        }else{
//            throw new AdvertisingException(AdvertisingException::OPERATION_FAIL);
//        }
//    }


    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function formOptions() {
        $positionList = AdvertisingPosition::all()
                                           ->pluck("position_name", "id");
        $status = [
            "0" => "下架",
            "1" => "上架",
        ];

        return Respond::respondWithData(compact('positionList', 'status'));
    }
}
