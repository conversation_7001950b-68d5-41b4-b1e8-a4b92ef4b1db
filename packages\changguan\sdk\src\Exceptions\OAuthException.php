<?php

namespace Changgua<PERSON>\SDK\Exceptions;

/**
 * OAuth认证异常类
 * 用于处理OAuth授权过程中的异常
 */
class OAuthException extends \Exception
{
    /**
     * 无效的参数
     */
    public const ERROR_INVALID_PARAMS = 6001;

    /**
     * 令牌已过期
     */
    public const ERROR_TOKEN_EXPIRED = 6002;

    /**
     * 无效的令牌
     */
    public const ERROR_TOKEN_INVALID = 6003;

    /**
     * 无效的授权码
     */
    public const ERROR_CODE_INVALID = 6004;
}
