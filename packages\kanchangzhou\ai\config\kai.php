<?php

return [
    'other' => [
        'aliyun'            => [
            'green'  => [
                'key'            => env('ALI_GREEN_KEY'),
                'secret'         => env('ALI_GREEN_SECRET'),
                'keyword_lib_id' => 13432,
            ],
            'shield' => [
                'key'    => env('ALI_LIVESTREAM_KEY'),
                'secret' => env('ALI_LIVESTREAM_SECRET'),
            ],
            'oss'    => [
                'uid'               => env('MODERATION_OSS_UID'),
                'access_key_id'     => env('MODERATION_OSS_ACCESS_KEY_ID'),
                'access_key_secret' => env('MODERATION_OSS_ACCESS_KEY_SECRET'),
                'bucket'            => env('MODERATION_OSS_BUCKET'),
                'endpoint'          => env('MODERATION_OSS_ENDPOINT'),
                'internal_endpoint' => env('MODERATION_OSS_INTERNAL_ENDPOINT'),
                'domain'            => env('MODERATION_OSS_DOMAIN'),
                'region'            => env('MODERATION_OSS_REGION'),
            ],
        ],
        'xunfei'            => [
            'textcorrection' => [
                'app_id'     => env('XF_TC_APPID', env('XF_APPID')),
                'api_key'    => env('XF_TC_APIKEY', env('XF_APIKEY')),
                'api_secret' => env('XF_TC_APISECRET', env('XF_APISECRET')),
                'uid'        => env('XF_TC_UID', ''),
                'res_id'     => env('XF_TC_RES_ID', ''),
            ],
            'keywords'       => [
                'app_id'  => env('XF_KEY_APPID', env('XF_APPID')),
                'api_key' => env('XF_KEY_APIKEY', env('XF_APIKEY')),
            ],
            'textrewrite'    => [
                'app_id'     => env('XF_TR_APPID', env('XF_APPID')),
                'api_key'    => env('XF_TR_APIKEY', env('XF_APIKEY')),
                'api_secret' => env('XF_TR_APISECRET', env('XF_APISECRET')),
            ],
            'ppt'            => [
                'app_id'     => env('XF_PPT_APPID', env('XF_APPID')),
                'api_secret' => env('XF_PPT_APISECRET', env('XF_APISECRET')),
            ],
        ],
        'media_ai'          => [
            'host'       => env('MEDIA_AI_HOST', ''),
            'secret_key' => env('MEDIA_AI_SECRET_KEY', ''),
        ],
        'skill_competition' => [
            'score_role'  => env('SKILL_COMPETITION_SCORE_ROLE', ''),
            'score_model' => env('SKILL_COMPETITION_SCORE_MODEL', ''),
        ],
    ],
    'chat'  => [
        'providers' => [
            'sensenova'  => [
                'access_id'  => env('SENSENOVA_ACCESS_ID', ''),
                'secret_key' => env('SENSENOVA_SECRET_KEY', ''),
            ],
            'aliyun'     => [
                'api_key' => env('ALI_DASHSCOPE_API_KEY', ''),
            ],
            'alibailian' => [
                'api_key' => env('ALIBAILIAN_API_KEY', ''),
            ],
            'baidu'      => [
                'client_id'     => env('BAIDU_AI_CLIENT_ID'),
                'client_secret' => env('BAIDU_AI_CLIENT_SECRET'),
            ],
            'openai'     => [
                'api_key'  => env('OPENAI_API_KEY'),
                'api_org'  => env('OPENAI_API_ORG'),
                'base_url' => env('OPENAI_BASE_URL'),
            ],
            'xunfei'     => [],
        ],
    ],
    'image' => [
        'providers' => [
            'aliyun'     => [
                'access_key_id'     => env('ALIBABA_CLOUD_ACCESS_KEY_ID', ''),
                'access_key_secret' => env('ALIBABA_CLOUD_ACCESS_KEY_SECRET', ''),
            ],
            'baidu'      => [
                'api_key'    => env('BAIDU_AI_API_KEY'),
                'secret_key' => env('BAIDU_AI_SECRET_KEY'),
            ],
            'openai'     => [
                'api_key'  => env('OPENAI_API_KEY'),
                'api_org'  => env('OPENAI_API_ORG'),
                'base_url' => env('OPENAI_BASE_URL'),
            ],
            'sd3'        => [
                'api_key'  => env('SD3_API_KEY', ''),
                'api_org'  => env('SD3_API_ORG', ''),
                'base_url' => env('SD3_BASE_URL', ''),
            ],
            'midjourney' => [
                'api_key'       => env('MIDJOURNEY_API_KEY', ''),
                'api_sk'        => env('MIDJOURNEY_API_SK', ''),
                'base_url'      => env('MIDJOURNEY_BASE_URL', ''),
                'call_back_url' => env('MIDJOURNEY_CALL_BACK_URL', ''),
            ],
        ],
    ],
    'tts'   => [
        'default'   => env('TTS_CLIENT', 'aliyun'),
        'providers' => [
            'aliyun'     => [
                'driver'     => 'aliyun',
                'appid'      => env('ALIYUN_TTS_APPID', ''),
                'secret_id'  => env('ALIYUN_TTS_SECRET_ID', ''),
                'secret_key' => env('ALIYUN_TTS_SECRET_KEY', ''),
            ],
            'baidu'      => [
                'driver'     => 'baidu',
                'appid'      => env('BAIDU_TTS_APPID', ''),
                'secret_id'  => env('BAIDU_TTS_SECRET_ID', ''),
                'secret_key' => env('BAIDU_TTS_SECRET_KEY', ''),
            ],
            'xunfei'     => [
                'driver'     => 'xunfei',
                'appid'      => env('XUNFEI_TTS_APPID', ''),
                'secret_id'  => env('XUNFEI_TTS_SECRET_ID', ''),
                'secret_key' => env('XUNFEI_TTS_SECRET_KEY', ''),
            ],
            'tencent'    => [
                'driver'     => 'tencent',
                'appid'      => env('TENCENT_TTS_APPID', ''),
                'secret_id'  => env('TENCENT_TTS_SECRET_ID', ''),
                'secret_key' => env('TENCENT_TTS_SECRET_KEY', ''),
            ],
            'volcengine' => [
                'driver'        => 'volcengine',
                'appid'         => env('VOLCENGINE_TTS_APPID', ''),
                'secret_id'     => env('VOLCENGINE_TTS_SECRET_ID', ''),
                'secret_key'    => env('VOLCENGINE_TTS_SECRET_KEY', ''),
                'access_token'  => env('VOLCENGINE_TTS_ACCESS_TOKEN', ''),
                'call_back_url' => env('VOLCENGINE_TTS_CALL_BACK_URL', ''),
            ],
            'huawei'     => [
                'driver'     => 'huawei',
                'appid'      => env('HUAWEI_TTS_APPID', ''),
                'secret_id'  => env('HUAWEI_TTS_SECRET_ID', ''),
                'secret_key' => env('HUAWEI_TTS_SECRET_KEY', ''),
            ],
            'azure'      => [
                'driver'     => 'azure',
                'appid'      => env('AZURE_TTS_APPID', ''),
                'secret_id'  => env('AZURE_TTS_SECRET_ID', ''),
                'secret_key' => env('AZURE_TTS_SECRET_KEY', ''),
                'region'     => env('AZURE_TTS_REGION', ''),
                'language'   => env('AZURE_TTS_LANGUAGE', ''),
            ],
        ],
    ],
    'video' => [
        'default'   => env('VIDEO_CLIENT', 'pixverse'),
        'providers' => [
            'pixverse' => [
                'driver'        => 'pixverse',
                'appid'         => env('PIXVERSE_API_APPID', ''),
                'secret_id'     => env('PIXVERSE_API_SECRET_ID', ''),
                'secret_key'    => env('PIXVERSE_API_SECRET_KEY', ''),
                'api_key'       => env('PIXVERSE_API_KEY', ''),
                'api_sk'        => env('PIXVERSE_API_SK', ''),
                'base_url'      => env('PIXVERSE_BASE_URL', ''),
                'call_back_url' => env('PIXVERSE_CALL_BACK_URL', ''),
                'result_url'    => env('PIXVERSE_RESULT_URL', ''),
            ],
        ],
    ],
];
