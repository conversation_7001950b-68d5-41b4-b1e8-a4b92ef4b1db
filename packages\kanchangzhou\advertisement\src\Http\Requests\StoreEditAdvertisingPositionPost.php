<?php

namespace Kanchangzhou\Advertisement\Http\Requests;

use Kanchangzhou\Advertisement\Exceptions\AdException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;

class StoreEditAdvertisingPositionPost extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "position_name"=>"required|max:50|min:3",
            "position_type_id"=>"required|numeric|min:1",
            "type"=>"required|numeric|min:0|max:2",
            "ad_type"=>"required|numeric|min:0",
            "ad_tag"=>"required"
        ];
    }

    public function messages()
    {
        return [
            "position_name.required"=>"广告位名称必填",
            "position_name.max"=>"广告位名称最长为50个字符",
            "position_name.min"=>"广告位名称最短为3个字符",
            "position_type_id.required"=>"广告位类型ID必填",
            'position_type_id.numeric'=>"广告位类型ID必须为数字",
            "position_type_id.min"=>"广告位类型ID必须大于0",
            "type.required"=>"广告位提取广告类型必传",
            "type.numeric"=>"广告位提取广告类型必须是数字",
            "type.min"=>"广告位提取广告类型最小为0",
            "type.max"=>"广告位提取广告类型最小为2",
            "ad_type.required"=>"广告位类型必传",
            "ad_type.numeric"=>"广告位类型必须是数字",
            "ad_type.min"=>"广告位类型最小为0",
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw(new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $validator->errors()));
    }
}
