<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('ai_order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ai_order_id')
                  ->comment('订单ID');
            $table->unsignedBigInteger('model_id')
                  ->nullable()
                  ->comment('模型ID');
            $table->string('model_uuid')
                  ->nullable()
                  ->comment('模型UUID');
            $table->string('provider')
                  ->nullable()
                  ->comment('供应商');
            $table->integer('input_tokens')
                  ->default(0)
                  ->comment('输入');
            $table->integer('output_tokens')
                  ->default(0);
            $table->integer('total_tokens')
                  ->default(0);
            $table->decimal('price', 10, 6)
                  ->comment('价格');
            $table->integer('unit_count')
                  ->comment('单位数量');
            $table->decimal('total', 10, 6);
            $table->string('order_type')
                  ->comment('订单类型');
            $table->unsignedTinyInteger('status')
                  ->default(1)
                  ->comment('状态');
            $table->bigInteger('user_package_id')
                  ->nullable()
                  ->comment('资源包ID');
            $table->bigInteger('chat_history_id')
                  ->nullable()
                  ->comment('聊天记录ID');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('ai_order_items');
    }
};
