<?php


namespace Kanchangzhou\Administrator\Supports;

use Carbon\Carbon;
use Kanchangzhou\Administrator\Exceptions\AuthSafeException;
use Kanchangzhou\Auth\Facades\AuthFacade;

class AuthSafe
{
    public static function isEnable() {
        return config('kadmin.auth_safe.enable');
    }

    public static function clearLoginFailLimit($userKey) {
        $key = 'auth_login_fail_' . md5($userKey);
        \Cache::forget($key);
    }

    /**
     * @param $userKey
     * @param false $check
     *
     * @throws AuthSafeException
     */
    public static function loginFailLimit($userKey, $check = false) {
        if (!static::isEnable() || !config('kadmin.auth_safe.login_fail_lock')) {
            return;
        }
        $key = 'auth_login_fail_' . md5($userKey);

        if ($check) {
            static::loginFailCheck($key);
        } else {
            static::LoginFailCounter($key);
        }
    }

    protected static function LoginFailCounter($key) {
        if (\Cache::has($key)) {
            \Cache::put($key, \Cache::get($key) + 1, config('kadmin.auth_safe.login_fail_lock_sec'));
        } else {
            \Cache::put($key, 1);
        }
    }

    /**
     * @param $key
     *
     * @throws AuthSafeException
     */
    protected static function loginFailCheck($key) {
        if (\Cache::get($key) >= config('kadmin.auth_safe.login_fail_times')) {
            throw new AuthSafeException(AuthSafeException::AUTH_SAFE_LOGIN_FAIL_LOCK);
        }
    }

    /**
     * @param $userId
     * @param false $check
     *
     * @throws AuthSafeException
     */
    public static function loginTimeOut($userId, $check = false) {
        if (!static::isEnable() && !config('kadmin.auth_safe.login_timeout')) {
            return;
        }

        if ($check && config('kadmin.auth_safe.login_timeout') + \Cache::get('login_timeout_' . $userId) <= time()) {
            auth(config('kadmin.auth.guard'))->logout(true);
            throw new AuthSafeException(AuthSafeException::AUTH_SAFE_LOGIN_TIMEOUT, [], 401);
        }

        \Cache::put('login_timeout_' . $userId, time(), config('kadmin.auth_safe.login_timeout') + 1);
    }

    /**
     * @param $userKey
     * @param string $lastUpdatedAt
     *
     * @return \Illuminate\Config\Repository|\Illuminate\Contracts\Foundation\Application|int|mixed|void
     * @throws AuthSafeException
     */
    public static function pwdExpiredCheck($userKey, $lastUpdatedAt = '') {
        if (!static::isEnable() && !config('kadmin.auth_safe.pwd_expired')) {
            return;
        }

        if (Carbon::now()
                  ->diffInDays($lastUpdatedAt) >= config('kadmin.auth_safe.pwd_expired')) {
            throw new AuthSafeException(AuthSafeException::AUTH_SAFE_PWD_EXP);
        }

        return config('kadmin.auth_safe.pwd_expired') - Carbon::now()
                                                              ->diffInDays($lastUpdatedAt);

//        $key = 'auth_safe_pwd_exp_' . md5($userKey);
//        if (\Cache::has($key)) {
//
//
//
//        } else {
//            \Cache::put($key, $lastUpdatedAt);
//        }
    }
}
