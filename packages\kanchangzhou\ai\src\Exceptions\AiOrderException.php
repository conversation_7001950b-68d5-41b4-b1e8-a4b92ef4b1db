<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class AiOrderException extends BaseException
{
    // 重复购买
    const  FREE_REPEAT_BUY  = 305001;

    // 资源包不足
    const  RESOURCE_PACKAGE_NOT_ENOUGH  = 305002;

    public static function message($code) {
        $msgArr = [
            self::FREE_REPEAT_BUY => '您已经购买过免费套餐',
            self::RESOURCE_PACKAGE_NOT_ENOUGH => '资源包不足,可以领取体验资源包',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
