<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class MediaAiException extends BaseException
{
    const INVALID_CONFIG = 301001;
    const MISS_PARAM = 301002;
    const MISS_INTERFACE = 301003;
    // 内容不合规
    const HAS_SENSITIVE = 301004;

    public static function message($code) {
        $msgArr = [
            static::INVALID_CONFIG => '无效的配置',
            static::MISS_PARAM => '缺少参数',
            static::MISS_INTERFACE => '缺少服务提供者',
            static::HAS_SENSITIVE => '根据相关法律法规和政策，无法为您提供服务',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
