<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\ChatUsedRole
 *
 * @property int $id
 * @property int $user_id
 * @property string $guard_name
 * @property string $role_uuid
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\ChatRole|null $role
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole whereRoleUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatUsedRole whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperChatUsedRole
 */
class ChatUsedRole extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function role() {
        return $this->belongsTo(ChatRole::class, 'role_uuid', 'role_uuid');
    }
}
