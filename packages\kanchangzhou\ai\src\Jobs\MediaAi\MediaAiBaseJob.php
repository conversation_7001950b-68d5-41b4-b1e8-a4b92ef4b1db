<?php

namespace Kanchangzhou\AI\Jobs\MediaAi;


namespace Kanchangzhou\AI\Jobs\MediaAi;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MediaAiBaseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $deleteWhenMissingModels = true;

    public function __construct() {
        $this->queue = 'media_ai';
    }
}

