<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\SkillCompetition
 *
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property string $title
 * @property array $content
 * @property string|null $background_music
 * @property string $status
 * @property string|null $ai_score_desc
 * @property string|null $ai_score
 * @property array|null $ai_media_score
 * @property string $ai_score_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $ai_score_status_str
 * @property-read mixed $status_str
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\SkillCompetitionScore> $scores
 * @property-read int|null $scores_count
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition query()
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereAiMediaScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereAiScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereAiScoreDesc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereAiScoreStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereBackgroundMusic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SkillCompetition whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperSkillCompetition
 */
class SkillCompetition extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'content' => 'array',
        'ai_media_score' => 'array',
    ];

    protected $appends = [
        'status_str',
        'ai_score_status_str',
    ];

    const STATUS_SAVED = 'saved';
    const STATUS_FINISHED = 'finished';

    const AI_SCORE_STATUS_WAITING = 'waiting';
    const AI_SCORE_STATUS_FINISHED = 'finished';


    public function getStatusStrAttribute() {
        return [
                   self::STATUS_SAVED => '已保存',
                   self::STATUS_FINISHED => '已完成',
               ][$this->status];
    }

    public function getAiScoreStatusStrAttribute() {
        return [
                   self::AI_SCORE_STATUS_WAITING => '等待评分',
                   self::AI_SCORE_STATUS_FINISHED => '已评分',
               ][$this->ai_score_status];
    }

    public function scores() {
        return $this->hasMany(SkillCompetitionScore::class);
    }
}
