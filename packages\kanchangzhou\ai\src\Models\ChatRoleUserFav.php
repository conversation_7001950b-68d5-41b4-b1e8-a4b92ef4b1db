<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\ChatRoleUserFav
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $guard_name
 * @property int $chat_role_id 角色ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\ChatRole|null $chatRole
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav whereChatRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRoleUserFav whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperChatRoleUserFav
 */
class ChatRoleUserFav extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function chatRole() {
        return $this->belongsTo(ChatRole::class);
    }
}
