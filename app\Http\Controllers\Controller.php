<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Kanchangzhou\Article\Models\Article;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Hooks\IntegralOrderHook;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\System\Models\SystemSensitiveWord;
use Kanchangzhou\User\Models\User;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function index() {
        $app = app();
        $routes = $app->routes->getRoutes();
        foreach ($routes as $k => $value) {
            if (!\Str::startsWith($value->uri, 'api/v1/admin')) {
                $path[$k]['uri'] = $value->uri;
                $path[$k]['name'] = $value->getName();
                $path[$k]['path'] = $value->methods[0];
            }

        }

        return $path;


//        $file = 'https://livehls.oss-cn-shanghai.aliyuncs.com/2020/12/22/video/3e7c507c76ac5bff573f16850285ff34.mov';
//        return response()->download($file);
//
//        $dict = fopen('/data/www/akm_dict/a', 'w');
//
//        SystemSensitiveWord::chunk(300, function ($words) use ($dict) {
//            foreach ($words as $word) {
//                fwrite($dict, $word->word . "\n");
//            }
//        });
//
//        fclose($dict);
//
//        dd(1);

//        return \JWTAuth::fromUser(User::find(4));

//        PermissionHook::can(AuthFacade::adminUser(), 'article.edit',Article::first());
//return Respond::respondWithData();
//        $payload = app('tymon.jwt.provider.jwt.lcobucci')
//            ->setKeys([
//                'public' => env('JWT_PUBLIC_KEY'),
//            ])

////            ->decode('eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.m7d1JkocYsY58kh7MWICfi30ljQQcwQEmflRyvHV7todgu9u7citVB0k9ttAKI4dOrVkZOcDKosIsMl1nNG_hIoYcj0hYiz5STSddS9RCagg76qPAsTGkwR0Eyiy7i-sdg8PQX4qq8sTuGw_PVlacgmV1dC7qdvqz-aOpj8wGGT2-pgcr23Wz1S_KP26EbE7QJopUHXv1-rUHJOBCjTDeXynqAatInWbAp43f9QBAFMe5TbAeCvvwlzp-D7W_MFLeHnYpo9iXspOOCbwGRzfl6Ebp6ESALHgtG5a8L_qBFo-5k8Hbw8SoYvLsqbQHpDfnz4rXCkfmwcWIcAvAdaW-A');
//            ->decode('*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
//        dd($payload);
    }


    public function test(){
        return response()->json(['request_method'=>request()->header()]);
    }
}
