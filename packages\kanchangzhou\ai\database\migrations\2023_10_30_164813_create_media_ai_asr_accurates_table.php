<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiAsrAccuratesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('media_ai_asr_accurates', function (Blueprint $table) {
            $table->id();
            $table->integer('media_ai_file_id')
                  ->comment('文件ID');
            $table->string('sentence', 1024)
                  ->nullable()
                  ->comment('句子');
            $table->string('begin')
                  ->nullable()
                  ->comment('起始时间');
            $table->string('end')
                  ->nullable()
                  ->comment('结束时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('media_ai_asr_accurates');
    }
}
