<?php

namespace Kanchangzhou\Administrator\Http\Controllers;

use Carbon\Carbon;
use Changguan\LaravelSDK\Facades\Changguan;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Http\Resources\AdminUserResource;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Administrator\Supports\AuthSafe;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\AuthFailException;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\User\Exceptions\JWTFailException;
use Tymon\JWTAuth\Exceptions\TokenBlacklistedException;

class AuthController extends BaseController
{
    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthFailException
     * @throws ValidateFailedException
     * @throws \Kanchangzhou\Administrator\Exceptions\AuthSafeException
     */
    public function login(Request $request) {
        try {
            $this->validate($request, [
                'account'  => 'required',
                'password' => 'required',
            ], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        AuthSafe::loginFailLimit($request->input('account'), true);

        $user = AdminUser::where('username', $request->input('account'))
                         ->orWhere('mobile', $request->input('account'))
                         ->first();

        if (!$user) {
            throw (new AuthFailException(AuthFailException::LOGIN_FAIL))->setStatusCode(422)->setErrData(['account' => "登录失败"]);
        }

        if (!\Hash::check($request->input('password'), $user->password)) {
            AuthSafe::loginFailLimit($user->username);

            throw (new AuthFailException(AuthFailException::LOGIN_FAIL))->setStatusCode(422)->setErrData(['account' => "登录失败"]);
        }

        if ($user->status != AdminUser::STATUS_VALID) {
            throw new AuthFailException(AuthFailException::USER_STATUS_ABNORMAL, [], 403);
        }

        AuthSafe::clearLoginFailLimit($user->username);
        $pwExpiredAt = AuthSafe::pwdExpiredCheck($user->getId(), $user->updated_at);
        AuthSafe::loginTimeOut($user->getId());

        AuthFacade::setAdminUserCache($user);

        $user->timestamps = false;
        $user->last_logon_time = Carbon::now();
        $user->save();

        $user->load([
            'roles',
            'permissions',
        ]);

        return Respond::respondWithData(AdminUserResource::make($user)
                                                         ->additional([
                                                             'auth'           => [
                                                                 'token'              => \JWTAuth::fromUser($user),
                                                                 'expired_at'         => Carbon::now()
                                                                                               ->addMinutes(config('jwt.ttl'))
                                                                                               ->toDateTimeString(),
                                                                 'refresh_expired_at' => Carbon::now()
                                                                                               ->addMinutes(config('jwt.refresh_ttl'))
                                                                                               ->toDateTimeString(),
                                                             ],
                                                             'pwd_expired_at' => $pwExpiredAt <= 7 ? '账号的密码将在 ' . $pwExpiredAt . ' 天过期，请在到期之前修改密码' : '',
                                                         ]));
    }

    /**
     * 登出
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout() {
        AuthFacade::delAdminUserCache(AuthFacade::adminUser()
                                                ->getId());

        auth(config('kauth.admin.guard'))->logout(true);

        return Respond::respondWithData();
    }

    /**
     * 刷新Token
     * @return \Illuminate\Http\JsonResponse
     * @throws JWTFailException
     */
    public function refresh() {
        try {
            $token = auth(config('kauth.admin.guard'))->refresh(true);
            auth(config('kauth.admin.guard'))->setToken($token);
            $user = AuthFacade::adminUser();
            if (!$user) {
                throw new TokenBlacklistedException();
            }
        } catch (\Exception $exception) {
            throw new JWTFailException($exception);
        }

        return Respond::respondWithData(AdminUserResource::make($user)
                                                         ->additional([
                                                             'auth' => [
                                                                 'token'              => \JWTAuth::fromUser($user),
                                                                 'expired_at'         => Carbon::now()
                                                                                               ->addMinutes(config('jwt.ttl'))
                                                                                               ->toDateTimeString(),
                                                                 'refresh_expired_at' => Carbon::now()
                                                                                               ->addMinutes(config('jwt.refresh_ttl'))
                                                                                               ->toDateTimeString(),
                                                             ],
                                                         ]));
    }

    public function oauth(Request $request) {
        $this->validate($request, [
            'code' => 'required',
        ]);

        $oauth2 = Changguan::project('admin')->oauth();

        try {
            $res = $oauth2->getUserInfoByCode($request->input('code'));
        } catch (\Exception $e) {
            throw (new AuthFailException())->setErrMsg($e->getMessage());
        }
        
        if($res['errcode'] != 0) {
            throw (new AuthFailException())->setErrMsg($res['errmsg']);
        }

        $userInfo = $res['data'];

        $user = AdminUser::where('uc_openid', $userInfo['open_id'])
                         ->first();

        if(!$user) {
            $user = AdminUser::where('mobile', $userInfo['mobile'])
                             ->first();

            if(!$user) {
                $user = AdminUser::create([
                    'username' => $userInfo['mobile'],
                    'password' => \Hash::make(Str::random(16)),
                    'mobile'   => $userInfo['mobile'],
                    'uc_openid' => $userInfo['open_id'],
                    'status'    => AdminUser::STATUS_VALID,
                    'nickname'  => $userInfo['nickname'],
                    'true_name' => $userInfo['true_name'],
                    'avatar'    => $userInfo['avatar'],
                ]);
            }
            
            $user->uc_openid = $userInfo['open_id'];
            $user->save();
        }

        AuthSafe::clearLoginFailLimit($user->username);
        $pwExpiredAt = AuthSafe::pwdExpiredCheck($user->getId(), $user->updated_at);
        AuthSafe::loginTimeOut($user->getId());

        AuthFacade::setAdminUserCache($user);

        $user->timestamps = false;
        $user->last_logon_time = Carbon::now();
        $user->save();

        $user->load([
            'roles',
            'permissions',
        ]);

        return Respond::respondWithData(AdminUserResource::make($user)
                                                         ->additional([
                                                             'auth'           => [
                                                                 'token'              => \JWTAuth::fromUser($user),
                                                                 'expired_at'         => Carbon::now()
                                                                                               ->addMinutes(config('jwt.ttl'))
                                                                                               ->toDateTimeString(),
                                                                 'refresh_expired_at' => Carbon::now()
                                                                                               ->addMinutes(config('jwt.refresh_ttl'))
                                                                                               ->toDateTimeString(),
                                                             ],
                                                             'pwd_expired_at' => $pwExpiredAt <= 7 ? '账号的密码将在 ' . $pwExpiredAt . ' 天过期，请在到期之前修改密码' : '',
                                                         ]));
    }

    public function getUserInfo() {
        $user = AuthFacade::adminUser();

        return Respond::respondWithData(AdminUserResource::make($user));
    }
}
