<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class TextCorrectionException extends BaseException
{
    const TEXT_CONTENT_FAIL = 300001;

    public static function message($code) {
        $msgArr = [
            static::TEXT_CONTENT_FAIL => '检测项目无实际内容或内容过长(超10000字)',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
