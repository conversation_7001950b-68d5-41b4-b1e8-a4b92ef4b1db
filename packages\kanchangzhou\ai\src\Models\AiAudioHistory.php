<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\Auth\Facades\AuthFacade;

/**
 * Kanchangzhou\AI\Models\AiAudioHistory
 *
 * @property int $id
 * @property string|null $prompt 关键词
 * @property string|null $module 模型名称
 * @property string|null $provider 服务提供者
 * @property string|null $progress 任务生成进度
 * @property string|null $audio_local 生成视频完整URL
 * @property string|null $audio_origin 生成视频原始URL
 * @property string|null $status 有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
 * @property string|null $failed_reason 失败原因
 * @property string|null $point 点数
 * @property string|null $price 价格
 * @property string|null $task_id 任务ID
 * @property string|null $result_params 返回参数
 * @property string|null $task_params 请求参数
 * @property int|null $user_id 用户ID
 * @property string|null $guard_name 用户名称
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereAudioLocal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereAudioOrigin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereFailedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereModule($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereProgress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory wherePrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereResultParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereTaskParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiAudioHistory withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiAudioHistory
 */
class AiAudioHistory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'task_id', 'prompt', 'module', 'provider', 'progress', 'audio_local','audio_origin',
        'status', 'failed_reason', 'point', 'price', 'result_params', 'task_params', 'user_id', 'guard_name'
    ];

    protected $guarded = [];

    protected $appends = [

    ];

    protected $casts = [

    ];

    public static function createFromRequest($task_id, $request)
    {
        $aiUser = new AiUser(AuthFacade::adminUser()
            ->getId(), 'kadmin');

        return self::create([
            'task_id' => $task_id,
            'prompt' => $request['prompt'],
            'module' => $request['module'],
            'provider' => $request['provider'],
            'progress' => $request['progress'] ?? 0,
            'audio_local' => $request['audio_local'] ?? null,
            'audio_origin' => $request['audio_origin'] ?? null,
            'status' => $request['status'] ?? 'INIT',
            'failed_reason' => $request['failed_reason'] ?? null,
            'point' => $request['point'] ?? null,
            'price' => $request['price'] ?? null,
            'result_params' => $request['result_params'] ?? null,
            'task_params' => $request['task_params'] ?? null,
            'user_id' => $aiUser->getUserId(),
            'guard_name' => $aiUser->getGuardName(),
        ]);
    }

    public static function updateByTaskId($taskId, array $updateData)
    {
        return self::where('task_id', $taskId)->update($updateData);
    }
}
