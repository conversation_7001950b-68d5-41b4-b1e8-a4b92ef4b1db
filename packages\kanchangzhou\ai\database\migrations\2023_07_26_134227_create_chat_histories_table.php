<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('chat_histories', function (Blueprint $table) {
            $table->id();
            $table->uuid('chat_uuid');
            $table->integer('chat_model_id')
                  ->nullable();
            $table->text('message')->nullable();
            $table->string('role');
            $table->string('provider');
            $table->integer('user_id')
                  ->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('chat_histories');
    }
}
