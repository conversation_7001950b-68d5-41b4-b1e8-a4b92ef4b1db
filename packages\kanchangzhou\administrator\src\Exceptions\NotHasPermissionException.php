<?php


namespace Kanchangzhou\Administrator\Exceptions;


use Kanchangzhou\Kernel\Exceptions\BaseException;

class NotHasPermissionException extends BaseException
{

    const HAS_NO_PERMISSION = 20301;
    const HAS_NO_ROLE = 20302;
    const CANT_DEL_SELF = 20303;
    const CANT_DEL_ROOT = 20304;

    public function __construct($errCode = 10000, $errData = []) {
        $statusCode = 403;
        parent::__construct($errCode, $errData, $statusCode);
    }

    public static function message($code) {
        $msgArr = [
            self::HAS_NO_PERMISSION => '无权限进行此操作',
            self::HAS_NO_ROLE => '无权限进行此操作',
            self::CANT_DEL_SELF => '无法删除自身账号',
            self::CANT_DEL_ROOT => '无法删除终极管理员账号',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : parent::message($code);
    }

    public function dontReport() {
        return true;
    }
}
