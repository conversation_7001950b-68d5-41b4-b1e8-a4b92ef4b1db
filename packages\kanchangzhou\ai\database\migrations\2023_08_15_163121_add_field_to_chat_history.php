<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldToChatHistory extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->integer('chat_role_id')
                  ->nullable()
                  ->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('chat_role_id');
        });
    }
}
