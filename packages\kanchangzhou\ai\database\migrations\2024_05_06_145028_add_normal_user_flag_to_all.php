<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('ai_image_urls', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('ai_orders', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('ai_user_packages', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('chat_api_keys', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('chat_histories', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('chat_role_user_favs', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });

        Schema::table('chat_roles', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('owner_id');
        });

        Schema::table('chat_used_roles', function (Blueprint $table) {
            $table->string('guard_name')
                  ->default('kadmin')
                  ->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('ai_image_histories', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('ai_image_urls', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('ai_orders', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('ai_user_packages', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('chat_api_keys', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('chat_role_user_favs', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('chat_roles', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });

        Schema::table('chat_used_roles', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });
    }
};
