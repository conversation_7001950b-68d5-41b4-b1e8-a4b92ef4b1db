<?php

namespace Kanchangzhou\Advertisement\Http\AdminControllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Kanchangzhou\Advertisement\Exceptions\AdvertiserException;
use Kanchangzhou\Advertisement\Http\Controllers\Controller;
use Kanchangzhou\Advertisement\Http\Requests\StoreEditAdvertiserPost;
use Kanchangzhou\Advertisement\Http\Resources\AdvertiserResource;
use Kanchangzhou\Advertisement\Models\Advertiser;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Supports\Respond;

class AdvertiserController extends Controller
{

    public function add(StoreEditAdvertiserPost $request) {
        PermissionHook::can("广告商.新建");
        $validated = $request->validated();

        $advertiser_name = $validated["advertiser_name"];
        $phone = $validated["phone"];
        $email = $validated["email"];
        $addr = $validated["addr"];
        $start_coperation = $validated["start_coperation"];
        $stop_coperation = $validated["stop_coperation"];
        $bz = $validated["bz"];
        $salesman = $validated["salesman"];

        $nowTimeStamp = date("Y-m-d H:i:s");
        $hasAdvertiser = Advertiser::where("advertiser_name", $advertiser_name)
                                   ->where("start_coperation", "<=", $nowTimeStamp)
                                   ->where("stop_coperation", ">=", $nowTimeStamp)
                                   ->first();
        if ($hasAdvertiser) {
            throw new AdvertiserException(AdvertiserException::HAS_EXISTS);
        }
        $advertiser = new Advertiser();
        $advertiser->advertiser_name = $advertiser_name;
        $advertiser->phone = $phone;
        $advertiser->email = $email;
        $advertiser->addr = $addr;
        $advertiser->start_coperation = $start_coperation;
        $advertiser->stop_coperation = $stop_coperation;
        $advertiser->bz = $bz;
        $advertiser->salesman = $salesman;
        $insertRes = $advertiser->save();
        if ($insertRes) {
            return Respond::respondWithData($advertiser->id, AdvertiserException::OPERATION_SUCCESS);
        } else {
            throw new AdvertiserException(AdvertiserException::OPERATION_FAIL);
        }

    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, $id) {
        PermissionHook::can("广告商.删除", $id);
        $advertiser = Advertiser::find($id);
        if (!$advertiser) {
            throw new AdvertiserException(AdvertiserException::NOT_EXISTS);
        }
        $deleteRes = $advertiser->delete();
        if ($deleteRes) {
            return Respond::respondWithData("operation success", AdvertiserException::OPERATION_SUCCESS);
        } else {
            throw new AdvertiserException(AdvertiserException::OPERATION_FAIL);
        }
    }


    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(StoreEditAdvertiserPost $request, $id) {
        PermissionHook::can("广告商.编辑", $id);
        $validated = $request->validated();

        $advertiser_name = $validated["advertiser_name"];
        $phone = $validated["phone"];
        $email = $validated["email"];
        $addr = $validated["addr"];
        $start_coperation = $validated["start_coperation"];
        $stop_coperation = $validated["stop_coperation"];
        $bz = $validated["bz"];
        $salesman = $validated["salesman"];

        $nowTimeStamp = date("Y-m-d H:i:s");
        $hasAdvertiser = Advertiser::where("advertiser_name", $advertiser_name)
                                   ->where("start_coperation", "<=", $nowTimeStamp)
                                   ->where("stop_coperation", ">=", $nowTimeStamp)
                                   ->first();
        if ($hasAdvertiser) {
            if ($hasAdvertiser->id != $id) {
                throw new AdvertiserException(AdvertiserException::HAS_EXISTS);
            }
        }

        $advertiser = Advertiser::find($id);
        if (!$advertiser || $advertiser == null) {
            throw new AdvertiserException(AdvertiserException::NOT_EXISTS);
        }
        $advertiser->advertiser_name = $advertiser_name;
        $advertiser->phone = $phone;
        $advertiser->email = $email;
        $advertiser->addr = $addr;
        $advertiser->start_coperation = $start_coperation;
        $advertiser->stop_coperation = $stop_coperation;
        $advertiser->bz = $bz;
        $advertiser->salesman = $salesman;
        $saveRes = $advertiser->save();
        if ($saveRes) {
            return Respond::respondWithData("operation success", AdvertiserException::OPERATION_SUCCESS);
        } else {
            throw new AdvertiserException(AdvertiserException::OPERATION_FAIL);
        }
    }

    /**
     * @param Request $request
     * @param $pagesize
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request) {
        PermissionHook::can("广告商.列表");

        return Respond::respondWithData(AdvertiserResource::collection(Advertiser::orderBy("id", "desc")
                                                                                 ->paginate(10)), AdvertiserException::OPERATION_SUCCESS);
    }

    /**
     * @param Request $request
     * @param $id
     */
    public function detail(Request $request, $id) {
        PermissionHook::can("广告商.详情", $id);
        $advertiser = Advertiser::find($id);
        if (!$advertiser || $advertiser == null) {
            throw new AdvertiserException(AdvertiserException::NOT_EXISTS);
        }

        return Respond::respondWithData(AdvertiserResource::make($advertiser), AdvertiserException::OPERATION_SUCCESS);
    }
}
