<?php

namespace Kanchangzhou\AI\Services\Audio;

use Illuminate\Support\Str;
use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsClient;

class AiAudioClientFactory
{
    public function make(array $config) {
        return $this->createClient($config['driver'], $config['appid'], $config['secret_id'], $config['secret_key']);
    }

    public function createClient(string $provider, $appid, $secret_id, $secret_key): AiTtsClient {
        $className = __NAMESPACE__ . "\\Providers\\" . Str::ucfirst(strtolower($provider)) . "\\Client";

        try {
            $refClass = new \ReflectionClass($className);
            return $refClass->newInstance($appid, $secret_id, $secret_key);
        } catch (\Exception $exception) {dd($exception);
            throw new \Exception("Provider [{$provider}] not supported.");
        }
    }
}
