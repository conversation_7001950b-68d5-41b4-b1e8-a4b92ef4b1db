<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiFile
 *
 * @property int $id
 * @property string $fileaiable_type
 * @property int $fileaiable_id
 * @property string $file_id 媒体文件ID
 * @property string|null $media_file_url 媒体文件地址
 * @property string $media_type 媒体类型
 * @property string|null $nlp_text 文本内容
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiAsrAccurate> $asrAccurates
 * @property-read int|null $asr_accurates_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiEventName> $eventnames
 * @property-read int|null $eventnames_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiFace> $faces
 * @property-read int|null $faces_count
 * @property-read Model|\Eloquent $fileaiable
 * @property-read mixed $media_type_str
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiKeyword> $keywords
 * @property-read int|null $keywords_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiNer> $ners
 * @property-read int|null $ners_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiNewsclassification> $newsclassifications
 * @property-read int|null $newsclassifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiSummary> $summary
 * @property-read int|null $summary_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\MediaAiTask> $tasks
 * @property-read int|null $tasks_count
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereFileaiableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereFileaiableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereMediaFileUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereMediaType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereNlpText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFile whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiFile
 */
class MediaAiFile extends Model
{
    use HasFactory;

    /**
     * @var mixed
     */
    public $nlp_txt;
    protected $fillable = [
        'fileaiable_type',
        'fileaiable_id',
        'file_id',
        'media_file_url',
        'media_type',
    ];

    // 媒体类型: 1-图片 2-视频 3-音频 4-文档
    const MEDIA_TYPE_IMAGE = 'image';
    const MEDIA_TYPE_VIDEO = 'video';
    const MEDIA_TYPE_AUDIO = 'audio';
    const MEDIA_TYPE_DOC = 'doc';

    public static $mediaTypeMap = [
        self::MEDIA_TYPE_IMAGE => '图片',
        self::MEDIA_TYPE_VIDEO => '视频',
        self::MEDIA_TYPE_AUDIO => '音频',
        self::MEDIA_TYPE_DOC => '文稿',
    ];

    public function fileaiable() {
        return $this->morphTo();
    }

    public function getMediaTypeStrAttribute() {
        return static::$mediaTypeMap[$this->media_type] ?? '';
    }

    public function tasks() {
        return $this->hasMany(MediaAiTask::class, 'media_ai_file_id', 'id');
    }

    public function faces() {
        return $this->hasMany(MediaAiFace::class, 'media_ai_file_id', 'id');
    }

    public function keywords() {
        return $this->hasMany(MediaAiKeyword::class, 'media_ai_file_id', 'id');
    }

    public function ners() {
        return $this->hasMany(MediaAiNer::class, 'media_ai_file_id', 'id');
    }

    public function summary() {
        return $this->hasMany(MediaAiSummary::class, 'media_ai_file_id', 'id');
    }

    public function eventnames() {
        return $this->hasMany(MediaAiEventName::class, 'media_ai_file_id', 'id');
    }

    public function newsclassifications() {
        return $this->hasMany(MediaAiNewsclassification::class, 'media_ai_file_id', 'id');
    }

    public function asrAccurates() {
        return $this->hasMany(MediaAiAsrAccurate::class, 'media_ai_file_id', 'id');
    }
}
