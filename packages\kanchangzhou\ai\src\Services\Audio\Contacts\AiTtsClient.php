<?php

namespace Kanchangzhou\AI\Services\Audio\Contacts;

abstract class AiTtsClient
{
    private $requestConfig;

    public function __construct() {
        $this->setRequestConfig();
    }

    public function setRequestConfig(array $config = []): AiTtsClient {
        $this->requestConfig = $config;
        return $this;
    }

    public function getRequestConfig(): array {
        return $this->requestConfig;
    }

    abstract function textToSpeechStream(string $text);

    abstract function createTask(string $text);

    abstract function fetchTaskResult($taskId);
}
