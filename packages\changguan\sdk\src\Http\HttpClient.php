<?php

namespace <PERSON><PERSON><PERSON>\SDK\Http;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Changguan\SDK\Exceptions\HttpException;
use Changguan\SDK\Auth\SignatureValidator;
use Changguan\SDK\Constants\HeaderConstants;

class HttpClient
{

    /**
     * @var Client HTTP客户端实例
     */
    private $client;

    /**
     * @var string 客户端ID
     */
    private string $clientId;

    /**
     * @var string 客户端密钥
     */
    private string $clientSecret;

    /**
     * @var string 当前基础URL
     */
    private string $baseUrl;

    /**
     * @var array|null 最后一次请求的响应
     */
    private ?array $lastResponse = null;

    /**
     * @var string 客户端版本号
     */
    private string $clientVersion = '5.2.0';

    private string $clientAppId = 'changguan-sdk';

    /**
     * @var array 默认的客户端配置
     */
    private array $defaultConfig = [
        'timeout'         => 30,
        'connect_timeout' => 10,
        'http_errors'     => false,
    ];

    /**
     * HttpClient constructor.
     *
     * @param string $clientId 客户端ID
     * @param string $clientSecret 客户端密钥
     * @param string|null $baseUrl 可选的基础URL
     * @param array $config 额外的客户端配置
     *
     * @throws HttpException
     */
    public function __construct(string $clientId, string $clientSecret, ?string $baseUrl = null, array $config = []) {
        if (empty($clientId) || empty($clientSecret)) {
            throw new HttpException('客户端ID和密钥不能为空', HttpException::ERROR_CONFIG);
        }

        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->baseUrl = $baseUrl ? rtrim($baseUrl, '/') : '';

        // 合并配置
        $clientConfig = array_merge($this->defaultConfig, $config);
        if ($this->baseUrl) {
            $clientConfig['base_uri'] = $this->baseUrl;
        }

        $this->client = new Client($clientConfig);
    }

    /**
     * 创建一个新的请求实例
     *
     * @param string|null $baseUrl 新的基础URL
     *
     * @return static
     */
    public function new(?string $baseUrl = null): self {
        return new self($this->clientId, $this->clientSecret, $baseUrl, $this->defaultConfig);
    }

    /**
     * 使用新的基础URL
     *
     * @param string $baseUrl
     *
     * @return static
     * @throws HttpException
     */
    public function withBaseUrl(string $baseUrl): self {
        //        if (!filter_var($baseUrl, FILTER_VALIDATE_URL)) {
        //            throw new HttpException('无效的API基础URL', HttpException::ERROR_CONFIG);
        //        }

        return $this->new($baseUrl);
    }

    /**
     * 更新客户端配置
     *
     * @param array $config
     *
     * @return static
     */
    public function withConfig(array $config): self {
        $instance = clone $this;
        $instance->defaultConfig = array_merge($this->defaultConfig, $config);
        $clientConfig = $instance->defaultConfig;
        if ($this->baseUrl) {
            $clientConfig['base_uri'] = $this->baseUrl;
        }
        $instance->client = new Client($clientConfig);

        return $instance;
    }

    /**
     * 发送GET请求
     *
     * @param string $path
     * @param array $params
     * @param array $headers
     * @param bool $needSign
     *
     * @return array
     * @throws HttpException
     */
    public function get(string $path, array $params = [], array $headers = [], bool $needSign = true): array {
        return $this->request('GET', $path, $params, $headers, $needSign);
    }

    /**
     * 发送POST请求
     *
     * @param string $path
     * @param array $params
     * @param array $headers
     * @param bool $needSign
     *
     * @return array
     * @throws HttpException
     */
    public function post(string $path, array $params = [], array $headers = [], bool $needSign = true): array {
        return $this->request('POST', $path, $params, $headers, $needSign);
    }

    /**
     * 发送PUT请求
     *
     * @param string $path
     * @param array $params
     * @param array $headers
     * @param bool $needSign
     *
     * @return array
     * @throws HttpException
     */
    public function put(string $path, array $params = [], array $headers = [], bool $needSign = true): array {
        return $this->request('PUT', $path, $params, $headers, $needSign);
    }

    /**
     * 发送DELETE请求
     *
     * @param string $path
     * @param array $params
     * @param array $headers
     * @param bool $needSign
     *
     * @return array
     * @throws HttpException
     */
    public function delete(string $path, array $params = [], array $headers = [], bool $needSign = true): array {
        return $this->request('DELETE', $path, $params, $headers, $needSign);
    }

    /**
     * 发送HTTP请求
     *
     * @param string $method HTTP方法
     * @param string $path 请求路径
     * @param array $params 请求参数
     * @param array $headers 额外的请求头
     * @param bool $needSign 是否需要签名
     *
     * @return array 响应数据
     * @throws HttpException
     */
    public function request(string $method, string $path, array $params = [], array $headers = [], bool $needSign = true): array {
        try {
            $requestHeaders = $this->prepareHeaders($headers, $params, $needSign);

            $options = [
                'headers' => $requestHeaders,
            ];

            // 根据请求方法设置参数
            if ($method === 'GET') {
                $options['query'] = $params;
            } else {
                $options['form_params'] = $params;
            }

            // 如果path是完整URL，就直接使用
            $url = filter_var($path, FILTER_VALIDATE_URL) ? $path : $path;

            // 发送请求
            $response = $this->client->request($method, $url, $options);

            // 解析响应
            $result = json_decode($response->getBody()
                                           ->getContents(), true);
            if (!$result) {
                throw new HttpException('响应解析失败', HttpException::ERROR_RESPONSE_PARSE);
            }

            // 检查API错误
            if (isset($result['errcode']) && $result['errcode'] !== 0) {
                throw new HttpException($result['errmsg'] ?? '未知错误', $result['errcode'] ?? HttpException::ERROR_API_ERROR);
            }

            $this->lastResponse = $result;

            return $result;

        } catch (GuzzleException $e) {
            throw new HttpException("HTTP请求失败: " . $e->getMessage(), HttpException::ERROR_NETWORK);
        } catch (HttpException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new HttpException('请求处理失败: ' . $e->getMessage(), HttpException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 准备请求头
     */
    private function prepareHeaders(array $headers, array $params, bool $needSign): array {
        $timestamp = time();
        $nonce = $this->generateNonce();

        $defaultHeaders = [
            HeaderConstants::APP_ID         => $this->clientAppId,
            HeaderConstants::CLIENT_VERSION => $this->clientVersion,
            HeaderConstants::CLIENT_ID      => $this->clientId,
            HeaderConstants::NONCE          => $nonce,
            HeaderConstants::TIMESTAMP      => $timestamp,
        ];

        if ($needSign) {
            $signatureData = [
                    'client_id' => $this->clientId,
                    'nonce'     => $nonce,
                    'timestamp' => $timestamp,
                ] + $params;

            $signatureValidator = new SignatureValidator($this->clientId, $this->clientSecret);
            $defaultHeaders[HeaderConstants::SIGNATURE] = $signatureValidator->generateSignature($signatureData);
        }

        return array_merge($defaultHeaders, $headers);
    }

    /**
     * 生成随机字符串
     */
    private function generateNonce(int $length = 16): string {
        try {
            return bin2hex(random_bytes($length));
        } catch (\Exception $e) {
            throw new HttpException('生成随机字符串失败: ' . $e->getMessage(), HttpException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 获取最后一次请求的响应
     */
    public function getLastResponse(): ?array {
        return $this->lastResponse;
    }

    /**
     * 设置客户端版本号
     */
    public function setClientVersion(string $version): self {
        $this->clientVersion = $version;

        return $this;
    }

    /**
     * 获取当前基础URL
     */
    public function getBaseUrl(): string {
        return $this->baseUrl;
    }

    /**
     * 获取原始的Guzzle客户端实例
     */
    public function getClient(): Client {
        return $this->client;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId() {
        return $this->clientId;
    }
}
