<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdvertiserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('advertiser', function (Blueprint $table) {
            $table->id();
            $table->string("advertiser_name", 30)->comment("客户名称");
            $table->string("phone", 13)->comment("联系电话");
            $table->string("email", 50)->comment("邮箱");
            $table->string("addr", "50")->comment("地址");
            $table->dateTime("start_coperation")->comment("合作开始时间");
            $table->dateTime("stop_coperation")->comment("合作结束时间");
            $table->text("bz")->comment("备注信息");
            $table->string("salesman")->comment("业务员");
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('advertiser');
    }
}
