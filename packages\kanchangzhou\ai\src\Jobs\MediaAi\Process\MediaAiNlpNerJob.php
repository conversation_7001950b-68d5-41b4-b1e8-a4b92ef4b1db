<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Process;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Jobs\MediaAi\MediaAiQueryTaskJob;
use Kanchangzhou\AI\Models\MediaAiTask;
use Kanchangzhou\AI\Services\MediaAi\NlpNer;
use Kanchangzhou\AI\Supports\StringTools;

class MediaAiNlpNerJob extends MediaAiBaseJob
{

    protected $mediaFile;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($mediaFile) {
        $this->mediaFile = $mediaFile;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {

                $files[] = [
                    'id' => $this->mediaFile->file_id,
                    'txt' => StringTools::trimString($this->mediaFile->nlp_text)
                ];

                $nlpNer = new NlpNer();

                $task = MediaAiTask::where('media_ai_file_id', $this->mediaFile->id)
                                ->where('module', $nlpNer->getTaskType())
                                ->where('file_id', $this->mediaFile->file_id)
                                ->first();

        if ($task && ($task->task_status == MediaAiTask::STATUS_PROCESSING || $task->task_status == MediaAiTask::STATUS_FINISHED)) {

                    MediaAiQueryTaskJob::dispatch($task);
                    return;
                }

                $res = $nlpNer->nlpNer($files);

                if ($res->successful()) {
                    MediaAiTask::create([
                        'media_ai_file_id' => $this->mediaFile->id,
                        'file_id' => $this->mediaFile->file_id,
                        'module' => $nlpNer->getTaskType(),
                        'task_id' => $res->json('data.guid'),
                        'task_status' => MediaAiTask::STATUS_PROCESSING,
                    ]);
                }
    }
}
