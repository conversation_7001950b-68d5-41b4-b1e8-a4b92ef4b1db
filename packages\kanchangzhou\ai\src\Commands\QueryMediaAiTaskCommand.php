<?php

namespace Kanchangzhou\AI\Commands;

use Illuminate\Console\Command;
use Kanchangzhou\AI\Jobs\MediaAi\MediaAiQueryTaskJob;
use Kanchangzhou\AI\Models\MediaAiTask;

class QueryMediaAiTaskCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:mediaAiQueryTask';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询media ai task';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $this->line("------ start ------");
        MediaAiTask::where('task_status', MediaAiTask::STATUS_PROCESSING)
                   ->chunkById(100, function ($tasks) {
                       foreach ($tasks as $task) {
                           $this->line("task_id: {$task->task_id}");
                           dispatch(new MediaAiQueryTaskJob($task));
                       }
                   });
        $this->line("------ end ------");
    }
}
