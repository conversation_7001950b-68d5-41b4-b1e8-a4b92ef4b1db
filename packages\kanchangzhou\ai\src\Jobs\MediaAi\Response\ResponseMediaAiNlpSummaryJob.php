<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Models\MediaAiSummary;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiNlpSummaryJob extends MediaAiBaseJob
{
    // data demo: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"8b4f96115b67432dbb03a406555ee425","subDataTypes":[{"type":"nlp/summary","source":"索贝","version":null}],"nlp/summary":[{"fileId":"47f99a48-ad3c-497c-b443-63d2d0439163","statusCode":0,"statusInfo":"success","contents":[{"title":"在9月5号举行的比赛中，米沙齐布尔曼布敌加拿大选手布莱恩扬首轮出局。","summary":"在9月5号举行的比赛中，米沙齐布尔曼布敌加拿大选手布莱恩扬首轮出局。此外，林家糖市场常规农产品量足价稳，其中蔬菜价格整体稳中小跌，西洋芹、韭菜、四季豆跌幅位居前三，每斤价格分别为4.73元、4.45元、7.86元，猪环比分别下跌六点三四四点九一百分之四点五，蔬菜日均销量6000吨左右。求职创业补贴发放标准为每人1500元，统一发放到毕业生本省社会保障卡。"}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $taskType = $this->data['data']['subDataTypes'][0]['type'];
        $guid = $this->data['data']['guid'];
        $summary = $this->data['data']['nlp/summary'][0]['contents'] ? $this->data['data']['nlp/summary'][0]['contents'][0] : [];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();
        if (!$task) {
            return;
        }

        if (!$summary) {
            $task->task_status = MediaAiTask::STATUS_FINISHED;
            $task->save();
            return;
        }

        MediaAiSummary::updateOrCreate([
            'media_ai_file_id' => $task->media_ai_file_id,
        ], [
            'media_ai_file_id' => $task->media_ai_file_id,
            'title' => $summary['title'],
            'summary' => $summary['summary'],
        ]);

        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
