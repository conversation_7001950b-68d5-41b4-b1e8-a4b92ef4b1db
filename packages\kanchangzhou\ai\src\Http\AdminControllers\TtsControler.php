<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use App\Events\TalkToTalk;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Models\AiAudioHistory;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\AI\Services\Audio\AiAudioTtsManager;

class TtsControler extends BaseController
{
    public function textToSpeech(Request $request) {
        $this->validate($request, [
            'text' => [
                'required',
                'string',
            ],
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'azure',
                    'volcengine'
                ]),
            ],
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
            ->setRequestConfig($request->all())
            ->textToSpeech($request->input('text'));

        return Respond::respondWithData($response);
    }

    public function voicesList(Request $request) {
        $this->validate($request, [
            'module' => ['required', 'string'],
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
                                             ->getVoicesList($request);

        return Respond::respondWithData($response);
    }

    public function task(Request $request) {

        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'azure',
                    'volcengine'
                ]),
            ],
            'text' => ['required', 'string']
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
                                           ->setRequestConfig($request->all())
                                           ->createTask($request->input('text'));

        return Respond::respondWithData([
            $response
        ]);
    }

    public function result(Request $request) {

        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'azure',
                    'volcengine'
                ]),
            ],
            'task_id' => ['required', 'string'],
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
            ->fetchTaskResult($request->input('task_id'));

        return Respond::respondWithData([
            $response
        ]);
    }

    public function rsync(Request $request) {
        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'azure',
                    'volcengine'
                ]),
            ],
            'task_id' => ['required', 'string'],
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
            ->rsync($request->input('task_id'));

        return Respond::respondWithData([
            $response
        ]);
    }

    public function history(Request $request) {
        $this->validate($request, [
            'module' => ['required', 'string'],
        ]);

        $query = AiAudioHistory::where('user_id', AuthFacade::adminUser()->getId());
        if($request->input('module')){
            $query->where('module', Str::studly($request->input('module')));
        }
        $response = $query->orderByDesc('id')->get();

        return Respond::respondWithData($response);
    }

    public function train(Request $request) {
        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'azure',
                    'volcengine'
                ]),
            ],
            'speaker_id' => ['required','string'],
            'audio' => ['required'],
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
            ->train($request);

        return Respond::respondWithData([
            $response
        ]);
    }

    public function trainStatus(Request $request) {
        $this->validate($request, [
            //模型名称
            'module' => [
                'required',
                Rule::in([
                    'baidu',
                    'azure',
                    'volcengine'
                ]),
            ],
            'speaker_id' => ['required', 'string'],
        ]);

        $response = (new AiAudioTtsManager())->client($request->input('module'))
            ->trainStatus($request->input('speaker_id'));

        return Respond::respondWithData([
            $response
        ]);
    }

    public function asr(Request $request) {

//        broadcast(new TalkToTalk(base64_encode('start')));
//
//        // 识别音频内容
        $client = (new AiAudioTtsManager())->client('aliyun')
                                           ->asrToText($request->file('file')
                                                               ->getContent());
//
//        // 获取音频内容
        $resText = $client->json('result');

        // 确认默认的聊天模型
//        $defaultChatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
//                                     ->where('status', ChatModel::STATUS_VALID)
//                                     ->first();

//        $resText = $request->input('text');
        // 调用模型的聊天方法
        $body = [
            'model' => 'qwen-max',
            'input' => [
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个智能对话机器人, 所有的回复内容尽量保持聊天对话的态度, 请勿涉及政治、色情、暴力等违法违规内容。',
                    ],
                    [
                        'role' => 'user',
                        'content' => $resText,
                    ],
                ],
            ],
            'parameters' => [
                'result_format' => 'message',
                'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                'temperature' => 0.85,
                'repetition_penalty' => 1.0,
                'enable_search' => true,
                'incremental_output' => true,

            ],
        ];

//        $messageId = Str::uuid()
//                        ->getHex()
//                        ->toString();
//        $sessionId = Str::uuid()
//                        ->getHex()
//                        ->toString();
//        $taskId = Str::uuid()
//                     ->getHex()
//                     ->toString();

//        $audioClient = (new AiAudioTtsManager())->client('aliyun');

//        $wsUrl = "wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1";
//
//        // 阿里云接口的鉴权参数
//        $appKey = "wkrSZv8oQa4wsCLp"; // 请替换为你的 App Key
//        $token = "457fba6e87ac4f04b46a2d41b40d2fc0"; // 请替换为你的 Token
//
//        // 拼接 WebSocket 地址
//        $wsUrlWithParams = "$wsUrl?appkey=$appKey&token=$token";
//
//        $wsClient = new \WebSocket\Client($wsUrlWithParams, [
//            'filter' => [
//                'text',
//                'binary',
//                'ping',
//                'pong',
//                'close',
//            ],
//            'timeout' => 1,
//        ]);
//
//        $message = [
//            'header' => [
//                'message_id' => $messageId,
//                'task_id' => $taskId,
//                'session_id' => $sessionId,
//                'namespace' => 'FlowingSpeechSynthesizer',
//                'name' => 'StartSynthesis',
//                'appkey' => $appKey,
//            ],
//            'payload' => [
//                "voice" => "zhixiaobai",
//                "format" => "mp3",
//                "sample_rate" => 48000,
//                "volume" => 50,
//                "speech_rate" => 0,
//                "pitch_rate" => 0,
//                "enable_subtitle" => true,
//            ],
//        ];
//
//        $wsClient->send(json_encode($message));
//        $wsData = $wsClient->receive();
//        broadcast(new TalkToTalk(base64_encode($wsData)));


        $ttsClient = (new AiAudioTtsManager())->client('aliyun')
                                              ->setRequestConfig([
                                                  'voice' => 'zhixiaobai',
                                                  'format' => 'mp3',
                                                  'sample_rate' => 48000,
                                                  'volume' => 50,
                                                  'speech_rate' => 0,
                                                  'pitch_rate' => 0,
                                              ]);

        $tempstr = '';
        $originstr = [];
        $changedstr = [];
        $response = Http::withHeaders([
            'X-DashScope-SSE' => 'enable',
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'application/json',
            'Accept' => 'text/event-stream',
        ])
                        ->withToken(config('kai.chat.providers.aliyun.api_key'))
                        ->withOptions([
                            'curl' => [
                                CURLOPT_WRITEFUNCTION => function ($curl, $data) use ($ttsClient, &$tempstr, &$originstr, &$changedstr) {
                                    preg_match('/data:(.*)/', $data, $matches);
                                    $dataStr = $matches[1];
                                    $resData = json_decode($dataStr, true);

                                    if (isset($resData['code'])) {

                                        return strlen($data);
                                    }

                                    $messageText = $resData['output']['choices'][0]['message']['content'];
                                    if (empty($messageText)) {
                                        return strlen($data);
                                    }
                                    $originstr[] = $messageText;
                                    $tempstr .= $messageText;
                                    // 检查临时字符串是否包含句子结束标点
                                    if (preg_match("/[。？！]/u", $tempstr)) {
                                        // 分割临时字符串成完整句子
                                        $sentences = preg_split("/(?<=[。？！])\s*/u", $tempstr, -1, PREG_SPLIT_NO_EMPTY);
                                        // 保证最后一个部分可能不是完整句子，暂时保留在 $tempString
                                        if (count($sentences) > 1) {
                                            $tempstr = array_pop($sentences);
                                            // 将完整句子添加到新数组
                                            $changedstr = array_merge($changedstr, $sentences);

                                            $ttsData = $ttsClient->textToSpeechStream($sentences[0]);
//
                                            broadcast(new TalkToTalk(base64_encode($ttsData)));

                                            $fileName = 'audio_' . microtime(true) . '.mp3';
                                            $filePath = 'audio/' . $fileName;

                                            // 保存文件到存储中 (可以是本地存储，S3 等等)
                                            \Storage::disk('local')
                                                    ->put($filePath, $ttsData);


                                        } else {
                                            // 仅一个句子，保留在 $tempString
                                            $tempstr = $sentences[0];
                                        }
                                    }

//                                    $ttsData = $ttsClient->textToSpeechStream($messageText);
//
//                                    broadcast(new TalkToTalk(base64_encode($ttsData)));
//
//                                    $fileName = 'audio_' . microtime(true) . '.mp3';
//                                    $filePath = 'audio/' . $fileName;
//
//                                    // 保存文件到存储中 (可以是本地存储，S3 等等)
//                                    \Storage::disk('local')
//                                            ->put($filePath, $ttsData);
//
//                                    $message = [
//                                        'header' => [
//                                            'message_id' => $messageId,
//                                            'task_id' => $taskId,
//                                            'session_id' => $sessionId,
//                                            'namespace' => 'FlowingSpeechSynthesizer',
//                                            'name' => 'RunSynthesis',
//                                            'appkey' => $appKey,
//                                        ],
//                                        'payload' => [
//                                            'text' => $messageText,
//                                        ],
//                                    ];

//                                    $wsClient->send(json_encode($message));

//                                    try {
//                                        $wsData = $wsClient->receive();
//                                        if (isset($wsData['header']['name']) && $wsData['header']['name'] === 'SynthesisCompleted') {
//                                            return strlen($data);
//                                        }
//                                        broadcast(new TalkToTalk($wsClient->getLastOpcode()));
//                                        broadcast(new TalkToTalk(base64_encode($wsData)));
//
//                                        if ($wsClient->getLastOpcode() == 'binary') {
//                                            $fileName = 'audio_' . microtime(true) . '.mp3';
//                                            $filePath = 'audio/' . $fileName;
//
//                                            // 保存文件到存储中 (可以是本地存储，S3 等等)
//                                            \Storage::disk('local')
//                                                    ->put($filePath, $wsData);
//                                        }
//                                    } catch (\Exception $e) {
//                                        return strlen($data);
//                                    }

                                    return strlen($data);
                                },
                            ],
                        ])
                        ->post('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', $body);

        if (!empty($tempstr)) {
            $changedstr[] = $tempstr;
            $ttsData = $ttsClient->textToSpeechStream($tempstr);
//
            broadcast(new TalkToTalk(base64_encode($ttsData)));

            $fileName = 'audio_' . microtime(true) . '.mp3';
            $filePath = 'audio/' . $fileName;

            // 保存文件到存储中 (可以是本地存储，S3 等等)
            \Storage::disk('local')
                    ->put($filePath, $ttsData);

        }
//        $message = [
//            'header' => [
//                'message_id' => $messageId,
//                'task_id' => $taskId,
//                'session_id' => $sessionId,
//                'namespace' => 'FlowingSpeechSynthesizer',
//                'name' => 'StopSynthesis',
//                'appkey' => $appKey,
//            ],
//        ];
//
//        $wsClient->send(json_encode($message));
//
//        while (true) {
//            try {
//                $data = $wsClient->receive();
//                if (isset($data['header']['name']) && $data['header']['name'] === 'SynthesisCompleted') {
//                    break;
//                }
//                broadcast(new TalkToTalk($wsClient->getLastOpcode()));
//                broadcast(new TalkToTalk(base64_encode($data)));
//
//                if ($wsClient->getLastOpcode() == 'binary') {
//                    $fileName = 'audio_' . microtime(true) . '.mp3';
//                    $filePath = 'audio/' . $fileName;
//
//                    // 保存文件到存储中 (可以是本地存储，S3 等等)
//                    \Storage::disk('local')
//                            ->put($filePath, $data);
//                }
//            } catch (\Exception $e) {
//                break;
//            }
//        }

//        $wsClient->close();

        return Respond::respondWithData([
            $originstr,
            $changedstr,
        ]);

//        return 'done';
    }
}
