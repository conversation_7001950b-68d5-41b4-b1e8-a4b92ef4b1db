<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Models\MediaAiEventName;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiNlpEventnamesJob extends MediaAiBaseJob
{
    // data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"3f8b18fcb929441fab7cc67559c474dc","subDataTypes":[{"type":"nlp/eventnames","source":"索贝","version":null}],"nlp/eventnames":[{"fileId":"347a4e04-63cb-41bf-98e1-6f6783dec798","statusCode":0,"statusInfo":"success","contents":[{"eventName":"年代勇敢的心黄金剧场","eventType":"EventSpecific","confidence":1,"begin":0,"end":10},{"eventName":"走访慰问","eventType":"EventGeneral","confidence":1,"begin":130,"end":134},{"eventName":"2023威克多中国羽毛球公开赛","eventType":"EventSpecific","confidence":1,"begin":200,"end":215}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $taskType = $this->data['data']['subDataTypes'][0]['type'];
        $guid = $this->data['data']['guid'];
        $eventnames = $this->data['data']['nlp/eventnames'][0]['contents'];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();
        if (!$task) {
            return;
        }

        foreach($eventnames as $eventname){
            MediaAiEventName::updateOrCreate([
                'media_ai_file_id' => $task->media_ai_file_id,
                'name' => $eventname['eventName'],
            ], [
                'media_ai_file_id' => $task->media_ai_file_id,
                'name' => $eventname['eventName'],
                'type' => $eventname['eventType'],
            ]);
        }

        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
