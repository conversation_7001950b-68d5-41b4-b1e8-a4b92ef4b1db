<?php

namespace Kanchangzhou\AI\Services\Image\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AIImageException;
use Kanchangzhou\AI\Services\Image\Contacts\AIImageService;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\AI\Services\AiUser;

class Sd3 extends AIImageService
{
    protected $apiKey;
    protected $user;

    /**
     * @param AiUser $user
     * @param $request
     */
    public function __construct(AiUser $user, Request $request){
        parent::__construct($user, $request);
        $this->request = $request;
        $this->apiKey = config('kai.image.providers.sd3.api_key');
    }

    public function handler($action) {
        if (method_exists($this, $action)) {
            return $this->$action();
        }else{
            throw new AIException("{$this->module}不支持{$action}操作");
        }
    }

    public function txt2imgv4() {
        ChatLimit::imageCheckLimit();

        $body = [
            'prompt' => $this->request->input('prompt'),    //[ 1 .. 10000 ] characters What you wish to see in the output image. A strong, descriptive prompt that clearly defines elements, colors, and subjects will lead to better results.
            //'aspect_ratio' => $this->request->input('aspect_ratio') ?? '1:1',    //Default: 1:1 Enum: 16:9 1:1 21:9 2:3 3:2 4:5 5:4 9:16 9:21
            'mode' => $this->request->input('mode') ?? 'text-to-image',    //Default: text-to-image  Controls whether this is a text-to-image or image-to-image generation (i.e. whether or not an image parameter is required).
            'negative_prompt' => $this->request->input('negative_prompt'),    //A blurb of text describing what you do not wish to see in the output image. This is an advanced feature. <= 10000 characters
            'model' => $this->request->input('model') ?? 'sd3-turbo',    //Default: sd3 Enum: sd3 sd3-turbo The model to use for generation.
            'seed' => $this->request->input('seed') ?? 0,    //[0 .. 4294967294] A specific value that is used to guide the 'randomness' of the generation. (Omit this parameter or pass 0 to use a random seed.)
            //'output_format' => $this->request->input('output_format') ?? 'jpg',    //Default: jpg Enum: jpg png webp The format of the output image.
        ];

        $res = Http::asMultipart();
        $res = $res->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Accept' => 'image/*',
        ]);

        if($this->request->file('image') && $body['mode'] = 'image-to-image'){
            $imageFile = $this->request->file('image');
            $imagePath = $imageFile->getRealPath();
            $res = $res->attach(
                'image', file_get_contents($imagePath), $imageFile->getClientOriginalName()
            );
            $body['strength'] = $this->request->input('strength') ?? 0.35;    //Default: 0.35 How much influence the init_image has on the diffusion process. Values close to 1 will yield images very similar to the init_image while values close to 0 will yield images wildly different than the init_image. The behavior of this is meant to mirror DreamStudio's "Image Strength" slider.
        }

        $res = $res->post('https://api.stability.ai/v2beta/stable-image/generate/sd3', $body);

        if ($res->successful()) {

            /*
             * 计费
            Service	    Resolution	                                                        Price
            SD3	        Stability AI's latest state of the art image generation model       6.5
            SD3         Turbo	State of the art, and fast                                  4
            Core        The best image generation service on the market                     3
            SDXL 1.0	The standard base model for image generation                        0.2-0.6
            SD 1.6	    Flexible-resolution base model for image generation                 0.2-1.0
            */

            $price = 0;
            $point = 0;
            if($body['model'] == 'sd3'){
                $price = 6.5/100*8;
                $point = 3; // 约等于3张
            }else{
                $price = 4/100*8;
                $point = 2; // 约等于2张
            }
            ChatLimit::setProvider('Chatgpt')->setModule('txt2imgv4','AIImage')->imageIncrement($point);

            $task_id = \Str::uuid();
            $path = 'ai-images'.'/'.date('Ymd').'/'.$task_id;

            if (!\Storage::disk('oss')->exists($path)) {
                $imageContent = $res->body();
                \Storage::disk('oss')->put($path, $imageContent);
            }
            $image_local = \Storage::disk('oss')->url($path);

            /*
            return [
                'message' => 'Generation is completed.',
                'image_local' => $image_local
            ];
            */

            // 获取图像尺寸
            //$image = Image::make($imageContent);

            //历史记录
            $history = [
                'module' => Str::studly(class_basename(get_class($this))),
                'provider' => __FUNCTION__,
                'prompt' => $body['prompt'],
                'revised_prompt' => $body['negative_prompt'],
                //'width' => $image->width(),
                //'height' => $image->height(),
                'width' => '',
                'height' => '',
                'image_num' => 1,
                'progress' => 1,
                'point' => $point,
                'price' => $price,
                'status' => 'SUCCESS',  //始终为 SUCCESS 成功状态
                'log_id' => $task_id,
                'task_id' => $task_id,
                'error_code' => 0,
                'approve_conclusion' => 'pass',
            ];

            //历史图片记录
            $history['result_list'][0] = [
                'image_orgin' => '',
                'image_base64' => '',
                'image_local' => $image_local,
                'status' => 'SUCCESS',
                'progress' => 1,
                'error_code' => 0,
                'approve_conclusion' => 'pass',
            ];

            return $this->createHistoryUrl($history);
        }else{
            throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,__("{$res->json('name')}[{$res->json('errors.0')}]"));
        }
    }
    public function img2video() {
        $body = [
            //'image' => $this->request->input('image'),  //1024x576 576x1024 768x768
            'cfg_scale' => $this->request->input('cfg_scale') ?? 1.8,  //[0-10]视频与原始图像的粘合程度。使用较低的值允许模型更自由地进行更改，并使用较高的值来校正运动失真。
            'motion_bucket_id' => $this->request->input('motion_bucket_id') ?? 127,    //[1-255]较低的值通常会导致输出视频中的运动减少，而较高的值通常会导致更多的运动。该参数对应于论文中的motion_bucket_id参数。
            'seed' => $this->request->input('seed') ?? 0,    //[0 .. 4294967294]用于指导生成的“随机性”的特定值。（省略此参数或传递 0 以使用随机种子）
        ];

        $imageFile = $this->request->file('image');
        $imagePath = $imageFile->getRealPath();

        $res = Http::asMultipart()
            ->attach(
                'image', file_get_contents($imagePath), $imageFile->getClientOriginalName()
            )
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])
            ->post("https://api.stability.ai/v2beta/image-to-video", $body);

        if ($res->successful()) {
            return [
                'message' => 'Create task completed.',
                'task_id' => $res->json('id')
            ];
        }else{
            //return response()->json(['error' => "Response {$res->status()}: {$res->body()}"], 500);
            throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,__("{$res->json('name')}[{$res->json('errors.0')}]"));
        }
    }

    //百度人工智能，文生查询结果
    public function getImg2video() {
        $task_id = $this->request->input('task_id');

        $res = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Accept' => 'video/*',
            ])
            ->get("https://api.stability.ai/v2beta/image-to-video/result/{$task_id}");

        //dd($res->status());
        //dd($res->json());

        if ($res->successful()) {
            if ($res->status() === 202) {
                return [
                    'message' => 'Generation is still running, try again in 10 seconds.',
                    'video_id' =>''
                ];
            } elseif ($res->status() === 200) {
                $task_id = \Str::uuid();
                $path = 'ai-videos'.'/'.date('Ymd').'/'.$task_id.'.mp4';
                if (!\Storage::disk('oss')->exists($path)) {
                    \Storage::disk('oss')->put($path, $res->body());
                }
                $video_local = \Storage::disk('oss')->url($path);
                return [
                    'message' => 'Generation is completed.',
                    'video_id' =>$video_local
                ];
            } else {
                throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,__("{$res->json('name')}[{$res->json('errors.0')}]"));
            }
        }else{
            //return response()->json(['error' => "Response {$res->status()}: {$res->body()}"], 500);
            throw new AIImageException(AIImageException::INVALID_PARAMETER,[],400,__("{$res->json('name')}[{$res->json('errors.0')}]"));
        }
    }
}
