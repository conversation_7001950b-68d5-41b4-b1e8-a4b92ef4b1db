<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('media_ai_files', function (Blueprint $table) {
            $table->longText('nlp_text')
                  ->nullable()
                  ->comment('文本内容')
                  ->after('media_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        if (Schema::hasColumn('media_ai_files', 'nlp_text')) {
            Schema::table('media_ai_files', function (Blueprint $table) {
                $table->dropColumn('nlp_text');
            });
        }
    }
};
