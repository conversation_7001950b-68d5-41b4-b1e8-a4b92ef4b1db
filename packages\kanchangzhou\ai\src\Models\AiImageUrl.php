<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiImageUrl
 *
 * @property int $id
 * @property int|null $history_id 历史消息ID
 * @property string|null $image_orgin 原始图片
 * @property string|null $image_local 本地图片
 * @property string|null $image_base64 图像base64编码
 * @property int|null $error_code 错误消息 0:正常；501:文本黄反拦截；201:模型生图失败
 * @property string|null $approve_conclusion 图片机审结果，"block"：输出图片违规；"review": 输出图片疑似违规；"pass": 输出图片未发现问题
 * @property string|null $progress 单任务图片生成进度，进度包含2种，0为未处理完，1为处理完成
 * @property string|null $status 单风格图片状态。有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态
 * @property string|null $task_id
 * @property string|null $log_id
 * @property int|null $user_id
 * @property string $guard_name
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\AiImageHistory|null $AiImageHistory
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereApproveConclusion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereErrorCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereHistoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereImageBase64($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereImageLocal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereImageOrgin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereLogId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereProgress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImageUrl withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiImageUrl
 */
class AiImageUrl extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [

    ];

    protected $casts = [

    ];

    public function AiImageHistory() {
        return $this->belongsTo(AiImageHistory::class);
    }
}
