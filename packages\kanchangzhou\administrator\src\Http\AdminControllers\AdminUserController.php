<?php

namespace Kanchangzhou\Administrator\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Exceptions\NotHasPermissionException;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Administrator\Supports\AuthSafe;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class AdminUserController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('管理员.列表|管理员.检索');

        if (!PermissionHook::can('管理员.列表', '', false)) {
            $adminUsers = AdminUser::select([
                'id',
                'nickname',
                'true_name',
            ]);
        } else {
            $adminUsers = AdminUser::with([
                'roles',
                'adminDepartment',
            ]);
        }

        $adminUsers = $adminUsers->when($request->input('username'), function ($query, $username) {
            return $query->where('username', 'like', "%{$username}%")
                         ->orWhere('nickname', 'like', "%{$username}%")
                         ->orWhere('true_name', 'like', "%{$username}%")
                         ->orWhere('mobile', 'like', "%{$username}%");
        })
                                 ->when($request->input('admin_department_id'), function ($query, $adminDepartmentId) {
                                     return $query->where('admin_department_id', $adminDepartmentId);
                                 })
                                 ->when($request->input('role_id'), function ($query, $roleId) {
                                     return $query->whereHas('roles', function ($query) use ($roleId) {
                                         $query->where('id', $roleId);
                                     });
                                 })
                                 ->orderByDesc('id')
                                 ->paginate(20);

        return Respond::respondWithData(JsonResource::collection($adminUsers));
    }

    /**
     * @param Request $request
     * @param null|AdminUser $adminUser
     *
     * @throws ValidateFailedException
     */
    protected function _validate(Request $request, $adminUser = null) {
        try {
            $this->validate($request, [
                'username' => [
                    'required',
                    'min:0',
                    'max:50',
                    !$adminUser ? Rule::unique(AdminUser::class) : Rule::unique(AdminUser::class)
                                                                       ->ignore($adminUser->id),
                ],
                'password' => !$adminUser ? [
                    'required',
                    'min:8',
                    'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
                ] : [
                    'nullable',
                    'min:8',
                    'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
                ],
                'nickname' => [
                    'required',
                    'max:50',
                    !$adminUser ? Rule::unique(AdminUser::class) : Rule::unique(AdminUser::class)
                                                                       ->ignore($adminUser->id),
                ],
                'true_name' => 'required|min:0|max:50',
                'mobile' => [
                    'required',
                    'isMobile',
                    !$adminUser ? Rule::unique(AdminUser::class) : Rule::unique(AdminUser::class)
                                                                       ->ignore($adminUser->id),
                ],
                'status' => 'required|in:' . AdminUser::STATUS_INVALID . ',' . AdminUser::STATUS_VALID,
                'roles' => 'nullable|array',
                'roles.*' => 'exists:roles,id',
                'permissions' => 'nullable|array',
                'permissions.*' => 'exists:permissions,id',
                'admin_department_id' => 'exists:admin_departments,id'
                //                'only_permissions' => 'array',
                //                'only_permissions.*.key' => 'required',
                //                'only_permissions.*.type' => [
                //                    'required',
                //                    Rule::in(array_keys(config('kadmin.permission_models'))),
                //                ],
            ], [
                'password.regex' => '密码必须包含大小写字母,数字和特殊符号(!@#$%^&*?-_)且最少8位',
            ]);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }
    }

    public function show($id) {
        PermissionHook::can('管理员.详情', $id);

        $adminUser = AdminUser::with([
            'roles',
            'permissions',
            'adminDepartment',
        ])
                              ->where('id', $id)
                              ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($adminUser));
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidateFailedException
     */
    public function store(Request $request) {
        PermissionHook::can('管理员.新建');

        $this->_validate($request);

        $adminUser = AdminUser::create([
            'username' => $request->input('username'),
            'password' => bcrypt($request->input('password')),
            'nickname' => $request->input('nickname'),
            'mobile' => $request->input('mobile'),
            'true_name' => $request->input('true_name'),
            'avatar' => $request->input('avatar'),
            'status' => $request->input('status'),
            'admin_department_id' => $request->input('admin_department_id', 1),
        ]);

        if(in_array(1, $request->input('roles'))){
            PermissionHook::can('管理员.编辑超管');
        }

        $adminUser->syncRoles($request->input('roles'));
        $adminUser->syncPermissions($request->input('permissions'));

        return Respond::respondWithData(JsonResource::make($adminUser));
    }

    public function update(Request $request, $id) {
        PermissionHook::can('管理员.更新', $id);

        $adminUser = AdminUser::where('id', $id)
                              ->firstOrFail();

        $this->_validate($request, $adminUser);

        $adminUser->username = $request->input('username');
        if ($request->input('password')) {
            $adminUser->password = bcrypt($request->input('password'));
            AuthSafe::clearLoginFailLimit($adminUser->username);
        }
        $adminUser->nickname = $request->input('nickname');
        $adminUser->mobile = $request->input('mobile');
        $adminUser->true_name = $request->input('true_name');
        $adminUser->status = $request->input('status');
        $adminUser->avatar = $request->input('avatar');
        $adminUser->admin_department_id = $request->input('admin_department_id', 1);
        $adminUser->save();

        if(in_array(1, $request->input('roles'))){
            PermissionHook::can('管理员.编辑超管');
        }
        
        $adminUser->syncRoles($request->input('roles'));
        $adminUser->syncPermissions($request->input('permissions'));

        return Respond::respondWithData(JsonResource::make($adminUser));
    }

    public function destroy($id) {
        PermissionHook::can('管理员.删除', $id);

        $adminUser = AdminUser::where('id', $id)
                              ->firstOrFail();

        if ($adminUser->id == 1) {
            throw new NotHasPermissionException(NotHasPermissionException::CANT_DEL_ROOT);
        }

        if ($adminUser->id == AuthFacade::adminUser()
                                        ->getId()) {
            throw new NotHasPermissionException(NotHasPermissionException::CANT_DEL_SELF);
        }

        $adminUser->delete();

        return Respond::respondWithData();
    }

    public function restore($id) {
        PermissionHook::can('管理员.恢复', $id);

        $adminUser = AdminUser::onlyTrashed()
                              ->where('id', $id)
                              ->firstOrFail();

        $adminUser->restore();

        return Respond::respondWithData();
    }

    public function hasPermission(Request $request) {
        $permission = $request->input('permission');

        return Respond::respondWithData(['has' => PermissionHook::can($permission, '', false)]);
    }
}
