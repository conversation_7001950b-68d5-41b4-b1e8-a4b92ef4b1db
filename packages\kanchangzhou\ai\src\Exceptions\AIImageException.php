<?php

namespace Kanchangzhou\AI\Exceptions;

use Kanchangzhou\Kernel\Exceptions\BaseException;

class AIImageException extends BaseException
{
    const INVALID_CONFIG = 303001;
    const MISS_PARAM = 303002;
    const MISS_INTERFACE = 303003;
    const SERVICE_ERROR = 303014;
    // 内容不合规
    const HAS_SENSITIVE = 303004;
    const INVALID_CONTENT = 303020;

    const TTV_LOW_QUALITY = 303005;
    const TTV_INAPPROPRIATE = 303006;
    const TTV_SENSITIVE = 303007;
    const TTV_PROCESS_FAILED = 303008;
    const TTV_PARAMETER_ERROR = 303009;
    const TTV_EXPIRE = 303010;
    const INVALID_PARAMETER = 303011;
    const INVALID_CALLBACK = 303012;

    public static function message($code) {
        $msgArr = [
            static::INVALID_CONFIG => '无效的配置',
            static::MISS_PARAM => '缺少参数',
            static::MISS_INTERFACE => '缺少服务提供者',
            static::HAS_SENSITIVE => '根据相关法律法规和政策，无法为您提供服务',
            static::SERVICE_ERROR => '服务异常，请稍后再试',
            static::TTV_LOW_QUALITY => '图片可能存在低质、格式错误等问题，请更换图片后重新提交',
            static::TTV_INAPPROPRIATE => '内容不适合生成视频（包含大量标点、字符、文本过短等），请修改文本后重新提交',
            static::TTV_SENSITIVE => '文本内容包含涉政敏感信息，请修改文本后重新提交',
            static::TTV_PROCESS_FAILED => '任务处理失败，请重新提交任务，若持续出现此类错误，请通过工单联系技术支持',
            static::TTV_PARAMETER_ERROR => '参数校验错误，请根据提示信息纠正您的参数',
            static::TTV_EXPIRE => '内容已过期，仅支持查询七天内信息',
            static::INVALID_CONTENT => '内容提交错误，请重新提交',
            static::INVALID_PARAMETER => '参数错误',
            static::INVALID_CALLBACK => '服务器返回错误，请稍后再试',
        ];

        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function dontReport() {
        return true;
    }
}
