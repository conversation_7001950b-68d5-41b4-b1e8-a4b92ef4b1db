<?php

namespace Kanchangzhou\Advertisement\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\Kernel\Supports\Units;

class AdvertisingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "title" => $this->title,
            "is_show_title"=>$this->is_show_title,
            "advertisers_id" => $this->advertisers_id,
            "advertising_position_id" => $this->advertising_position_id,
            "status" => $this->status,
            "username" => $this->username,
            "thumb_img" =>  $this->thumb_img,
            "start_show_time"=>$this->start_show_time,
            "stop_show_time"=>$this->stop_show_time,
            "link"=>$this->link,
            "bz"=>$this->bz,
            "type"=>$this->type,
            "style"=>$this->style,
            "redirectable"=>$this->redirectable,
            "power"=>$this->power,
            "clickNum"=>$this->clickNum,
            "serve_time"=>$this->serve_time,
            "advertiser_info" => AdvertiserResource::make($this->advertiserInfo),
            "advertising_position_info" => AdvertisingPositionResource::make($this->advertisingPositionInfo),
            'created_at' => $this->merge(Units::mergeDateTimeFormat('created_at', $this->created_at)),
            "media_type"=> $this->media_type,
            "display_times"=>$this->display_times,
        ];
    }
}
