<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->renameColumn('prompt', 'system_prompt');
            $table->json('step_prompt')
                  ->nullable()
                  ->after('options');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->renameColumn('system_prompt', 'prompt');
            $table->dropColumn('step_prompt');
        });
    }
};
