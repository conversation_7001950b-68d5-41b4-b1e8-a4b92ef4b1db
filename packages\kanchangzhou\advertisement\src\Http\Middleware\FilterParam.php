<?php

namespace Kanchangzhou\Advertisement\Http\Middleware;

use Closure;

class FilterParam
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(!empty($request->all())){
            foreach($request->all() as $key=>$value){
                if(in_array($key, ["thumb_img"])){
                    continue;
                }
                $request[$key] = strip_tags($value);
            }
        }
        return $next($request);
    }
}
