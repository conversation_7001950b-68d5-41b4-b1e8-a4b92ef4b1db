<?php

namespace Kanchangzhou\Administrator\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AdminUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function toArray($request) {
        return [
            'id' => $this->id,
            'nickname' => $this->nickname ?? '',
            'avatar' => $this->avatar ?? '',
            'mobile'=>$this->mobile,
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
        ];
    }

}
