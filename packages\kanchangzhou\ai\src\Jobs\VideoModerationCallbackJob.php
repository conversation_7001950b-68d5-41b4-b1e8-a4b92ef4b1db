<?php

namespace Kanchangzhou\AI\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Kanchangzhou\AI\Models\AiVideoAudit;
use Kanchangzhou\AI\Models\AiVideoAuditLabel;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Supports\GreenContent;

class VideoModerationCallbackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use AiVideoAuditLabel;

    public $video;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($video) {
        $this->video = $video;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        // 视频审核结果
        $frameResult = GreenContent::getPlusVideoModerationResult($this->video->task_id);
        $frameLabels = [];
        $frames = collect($frameResult['FrameResult']['Frames'] ?? [])
            ->sortBy('Offset', SORT_NATURAL)
            ->values();
        foreach ($frames as $frame) {
            foreach ($frame['Results'] as $result) {
                foreach ($result['Result'] as $label) {
                    if ($label['Label'] != 'nonLabel') {
                        $frameLabels['details'][$label['Label']]['label'] = $this->videoLabels[$label['Label']] ?? $label['Label'];
                        $frameLabels['details'][$label['Label']]['results'][] = [
                            'img' => $frame['TempUrl'],
                            'confidence' => $label['Confidence'],
                            'offset' => $frame['Offset'],
                        ];
                    }
                }
            }
        }
        $frameSummary = [
            'frameNum' => $frameResult['FrameResult']['FrameNum'] ?? 0,
            'frameSummarys' => collect($frameResult['FrameResult']['FrameSummarys'] ?? [])->map(function ($item) {
                return [
                    'label' => $this->videoLabels[$item['Label']] ?? '',
                    'labelSum' => $item['LabelSum'],
                ];
            }),
        ];
        $frameLabels['summary'] = $frameSummary;

        $this->video->frame_results = $frameLabels;

        // 音频审核结果
        $voiceResult = GreenContent::getPlusVoiceModerationResult($this->video->task_id);
        $voiceSummary = [
            'sliceNum' => 0,
            'sliceSummarys' => [],
        ];
        $voiceDetail = collect($voiceResult['SliceDetails'] ?? [])
            ->sortBy('StartTime', SORT_NATURAL)
            ->values()
            ->map(function ($item) use (&$voiceSummary) {
                $labels = [];
                $words = [];
                if ($item['Labels']) {
                    $labels = explode(',', $item['Labels']);
                    $words = json_decode($item['Extend']);
                    foreach ($labels as $label) {
                        $voiceSummary['sliceNum']++;
                        $voiceSummary['sliceSummarys'][$label]['label'] = $this->voiceLabels[$label];
                        $voiceSummary['sliceSummarys'][$label]['labelSum'] = ($voiceSummary['sliceSummarys'][$label]['labelSum'] ?? 0) + 1;
                    }
                }

                return [
                    'StartTime' => $item['StartTime'],
                    'EndTime' => $item['EndTime'],
                    'Text' => $item['Text'],
                    'Labels' => $labels,
                    'Extend' => $words,
                    'Url' => $item['Url'],
                ];
            });

        $this->video->voice_results = [
            'summary' => [
                'sliceNum' => $voiceSummary['sliceNum'],
                'sliceSummarys' => collect($voiceSummary['sliceSummarys'])->values(),
            ],
            'details' => $voiceDetail,
        ];

        // AI审核结果
        $voiceText = collect($voiceResult['SliceDetails'])
            ->sortBy('StartTime')
            ->implode('Text');


        $roleUuid = 'b086635f-f802-4850-a195-e477322fdd08';
        $chatRole = ChatRole::where('role_uuid', $roleUuid)
                            ->first();

        $serviceProvider = ChatModel::SERVICE_PROVIDERS[$chatRole->chatModel->provider];

        $aiUser = new AiUser(1, 'kadmin');

        $service = new $serviceProvider([
            'prompt' => $voiceText,
            'role_id' => $roleUuid,
        ], $chatRole->chatModel, $aiUser);

        $res = $service->handle();

        $message = $res->message;
        $aiResults = [
            'label' => $chatRole->title,
            'result' => $message,
        ];

        $this->video->ai_results = [$aiResults];
        $this->video->status = AiVideoAudit::STATUS_SUCCESS;
        $this->video->save();

    }
}
