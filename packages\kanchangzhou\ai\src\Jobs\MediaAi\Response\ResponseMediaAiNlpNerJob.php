<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;
use Kanchangzhou\AI\Models\MediaAiNer;
use Kanchangzhou\AI\Models\MediaAiTask;

class ResponseMediaAiNlpNerJob extends MediaAiBaseJob
{
    // data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"940a2d35dc534b6ab408bba6a6d65e84","subDataTypes":[{"type":"nlp/ner","source":"索贝","version":null}],"nlp/ner":[{"fileId":"47f99a48-ad3c-497c-b443-63d2d0439163","statusCode":0,"statusInfo":"success","contents":[{"entity":"习近平","type":"NAME","begin":40,"end":43},{"entity":"陈金虎","type":"NAME","begin":76,"end":79},{"entity":"胜雷","type":"NAME","begin":80,"end":82}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $guid = $this->data['data']['guid'];
        $ners = $this->data['data']['nlp/ner'][0]['contents'];

        $task = MediaAiTask::where('task_id', $guid)
                           ->first();
        if (!$task) {
            return;
        }

        foreach ($ners as $ner) {
            MediaAiNer::updateOrCreate([
                'media_ai_file_id' => $task->media_ai_file_id,
                'entity' => $ner['entity'],
            ], [
                'media_ai_file_id' => $task->media_ai_file_id,
                'entity' => $ner['entity'],
                'type' => $ner['type'],
                'begin' => $ner['begin'],
                'end' => $ner['end'],
            ]);
        }

        $task->task_status = MediaAiTask::STATUS_FINISHED;
        $task->save();
    }
}
