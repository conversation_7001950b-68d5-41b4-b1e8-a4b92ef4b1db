<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\ChatHistory
 *
 * @property int $id
 * @property string $chat_uuid
 * @property string|null $chat_model_id
 * @property string|null $message
 * @property array|null $fileids 文件ID
 * @property string $type 消息类型
 * @property string $role
 * @property string $provider
 * @property string|null $module_name 模块名称
 * @property string|null $module_key 模块key
 * @property int|null $user_id
 * @property string $guard_name
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $chat_role_id
 * @property string|null $chat_model_uuid 模型uuid
 * @property int|null $is_retry 是否重试
 * @property int|null $is_like 是否点赞
 * @property int|null $total_tokens 总计token数
 * @property int|null $input_tokens 输入token数
 * @property int|null $output_tokens 输出token数
 * @property string|null $provider_request_id
 * @property string|null $role_step 角色步骤
 * @property array|null $step_input 步骤输入
 * @property-read \Kanchangzhou\AI\Models\ChatModel|null $chatModel
 * @property-read \Kanchangzhou\AI\Models\ChatRole|null $chatRole
 * @property-read mixed $is_like_str
 * @property-read mixed $is_retry_str
 * @property-read ChatHistory|null $userChat
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereChatModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereChatModelUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereChatRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereChatUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereFileids($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereInputTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereIsLike($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereIsRetry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereModuleKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereModuleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereOutputTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereProviderRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereRoleStep($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereStepInput($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereTotalTokens($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatHistory withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperChatHistory
 */
class ChatHistory extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [
        'is_retry_str',
        'is_like_str',
    ];

    protected $casts = [
        'step_input' => 'array',
        'fileids' => 'array',
    ];

    //是否重试标记,点赞,点踩
    const IS_RETRY_YES = 2;
    const IS_RETRY_NO = 1;

    const IS_RETRY_MAP = [
        self::IS_RETRY_YES => '是',
        self::IS_RETRY_NO => '否',
    ];

    const IS_LIKE_YES = 2;
    const IS_LIKE_NO = 1;

    const IS_LIKE_MAP = [
        self::IS_LIKE_YES => '喜欢',
        self::IS_LIKE_NO => '不喜欢',
    ];

    const ROLE_USER = 'user';
    const ROLE_ASSISTANT = 'assistant';

    const ROLE_MAP = [
        self::ROLE_USER => '用户',
        self::ROLE_ASSISTANT => 'AI',
    ];

    const TYPE_CHAT = ChatModel::MODEL_TYPE_CHAT;
    const TYPE_IMAGE = ChatModel::MODEL_TYPE_IMAGE;
    const TYPE_VIDEO = ChatModel::MODEL_TYPE_VIDEO;
    const TYPE_AUDIO = ChatModel::MODEL_TYPE_AUDIO;

    const TYPE_MAP = ChatModel::MODEL_TYPE_MAP;


    public function getIsRetryStrAttribute() {
        return self::IS_RETRY_MAP[$this->is_retry] ?? '';
    }

    public function getIsLikeStrAttribute() {
        return self::IS_LIKE_MAP[$this->is_like] ?? '';
    }

    public function chatModel() {
        return $this->belongsTo(ChatModel::class, 'chat_model_uuid', 'model_uuid');
    }

    public function chatRole() {
        return $this->belongsTo(ChatRole::class, 'chat_role_id', 'role_uuid');
    }

    public function userChat() {
        return $this->hasOne(ChatHistory::class, 'chat_uuid', 'chat_uuid');
    }
}
