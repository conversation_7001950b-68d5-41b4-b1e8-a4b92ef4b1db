<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('ai_video_histories', function (Blueprint $table) {
            $table->string('guard_name')
                ->default('kadmin')
                ->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('ai_video_histories', function (Blueprint $table) {
            $table->dropColumn('guard_name');
        });
    }
};
