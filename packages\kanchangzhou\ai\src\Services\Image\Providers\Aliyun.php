<?php

namespace Kanchangzhou\AI\Services\Image\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Services\Image\Contacts\AIImageService;

use AlibabaCloud\SDK\Imageenhan\*********\Imageenhan;
use AlibabaCloud\SDK\Imageseg\*********\Imageseg;

use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

use AlibabaCloud\SDK\Imageenhan\*********\Models\GenerateImageWithTextRequest;
use AlibabaCloud\SDK\Imageenhan\*********\Models\GenerateSuperResolutionImageRequest;
use AlibabaCloud\SDK\Imageenhan\*********\Models\MakeSuperResolutionImageRequest;
use AlibabaCloud\SDK\Imageenhan\*********\Models\GetAsyncJobResultRequest as GetImageenhanAsyncJobResultRequest;

use AlibabaCloud\SDK\Imageseg\*********\Models\SegmentCommonImageRequest;
use AlibabaCloud\SDK\Imageseg\*********\Models\GetAsyncJobResultRequest as GetImagesegAsyncJobResultRequest;


class Aliyun extends AIImageService
{

    public function handler($action) {
        if (method_exists($this, $action)) {
            return $this->$action();
        }else{
            throw new AIException("{$this->module}不支持{$action}操作");
        }
    }

    public function txt2img() {
        $body = [
            'model' => 'wanx2.1-t2i-turbo',
            'input' => [
                'prompt' => $this->request->input('text'),  //文本内容，支持中英文，中文不超过75个字，英文不超过75个单词，超过部分会自动截断。
                'negative_prompt' => $this->request->input('negative_text') ?? '',   //不生成的prompt信息
            ],
            'parameters' => [
                'size' => $this->request->input('resolution')  ?: '1024*1024',
                'n' => $this->request->input('num') ? intval($this->request->input('num')) : 1,
                'seed' => $this->request->input('seed') ? intval($this->request->input('seed')) : null,
                'prompt_extend' => $this->request->input('prompt_extend') ?? 'true',
            ],
        ];

        $res = Http::asJson()
            ->withHeaders(['X-DashScope-Async' => 'enable'])
            ->withToken(config('kai.chat.providers.aliyun.api_key'))
            ->post("https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis", $body);

        if ($res->successful()) {
            return [
                'task_id' => $res->json('output.task_id'),
                'task_status' => $res->json('output.task_status'),
                'log_id' => $res->json('request_id'),
            ];
        }else{
            throw new \Exception($res->json('message'));
        }
    }

    public function getImg() {

        $res = Http::acceptJson()
            ->withToken(config('kai.chat.providers.aliyun.api_key'))
            ->get('https://dashscope.aliyuncs.com/api/v1/tasks/' . $this->request->input('task_id'));

        if ($res->successful()  && $res->json('output.task_status') != 'FAILED') {

            $images = [];
            $imgUrls = $res->json('output.results');
            if($imgUrls && is_array($imgUrls)){
                foreach($imgUrls as $k=>$v){
                    $images[] = $v['url'];
                }
            }

            return [
                'images' => $images,
                //'waiting' => $res->json('data.waiting'),
                'status' => $res->json('output.task_status'),   //RUNNING | SUCCEEDED | FAILED
                'log_id' => $res->json('request_id'),
            ];
        }else{
            throw new \Exception($res->json('output.message'));
        }
    }

    //阿里人工智能，文生图高级版
    public function txt2imgv2() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，文生图高级版查询结果
    public function getImgv2() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，文图生成视频
    public function ttv() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，文图生成视频查询结果
    public function getTtv() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，图像修复
    public function inpainting() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，图片无损放大
    public function imageQualityEnhance() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，文档图片去底纹
    public function docRepair() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，图像去噪
    public function denoise() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，logo识别-检索
    public function logoSearch() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，logo识别-入库
    public function logoAdd() {
        throw new \Exception('暂不支持');
    }

    //阿里人工智能，logo识别-删除
    public function logoDelete() {
        throw new \Exception('暂不支持');
    }
}
