<?php

return [
    'default' => env('CLOUD_DISK', 'oss'),

    'disks' => [
        'oss' => [
            'driver' => 'oss',
            'access_key' => env('OSS_ACCESS_KEY'),
            'secret_key' => env('OSS_SECRET_KEY'),
            'endpoint' => env('OSS_ENDPOINT'),
            'internal_endpoint' => env('OSS_INTERNAL_ENDPOINT', ''),
            'bucket' => env('OSS_BUCKET'),
            'cdndomain' => env('OSS_CDNDOMAIN', ''),
            'ssl' => env('OSS_SSL', false),
            'debug' => env('OSS_DEBUG', false),
        ],
        'cos' => [
            'driver' => 'cos',
            'access_key' => env('COS_ACCESS_KEY'),
            'secret_key' => env('COS_SECRET_KEY'),
            'region' => env('COS_REGION'),
            'bucket' => env('COS_BUCKET'),
            'cdndomain' => env('COS_CDNDOMAIN', ''),
            'ssl' => env('COS_SSL', false),
        ],
        'obs' => [
            'driver' => 'obs',
            'access_key' => env('OBS_ACCESS_KEY'),
            'secret_key' => env('OBS_SECRET_KEY'),
            'endpoint' => env('OBS_ENDPOINT'),
            'bucket' => env('OBS_BUCKET'),
            'cdndomain' => env('OBS_CDNDOMAIN', ''),
            'ssl' => env('OBS_SSL', false),
        ],
        'qiniu' => [
            'driver' => 'qiniu',
            'access_key' => env('QINIU_ACCESS_KEY'),
            'secret_key' => env('QINIU_SECRET_KEY'),
            'bucket' => env('QINIU_BUCKET'),
            'domain' => env('QINIU_DOMAIN'),
        ],
    ],
];
