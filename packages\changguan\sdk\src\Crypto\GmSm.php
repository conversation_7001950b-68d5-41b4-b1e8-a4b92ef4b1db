<?php

namespace <PERSON><PERSON><PERSON>\SDK\Crypto;

use Changguan\SDK\Exceptions\CryptoException;
use <PERSON><PERSON><PERSON>\Ecc\Serializer\PrivateKey\DerPrivateKeySerializer;
use <PERSON><PERSON><PERSON>\Ecc\Serializer\PrivateKey\PemPrivateKeySerializer;
use <PERSON><PERSON>er\Ecc\Serializer\PublicKey\DerPublicKeySerializer;
use <PERSON><PERSON><PERSON>\Ecc\Serializer\PublicKey\PemPublicKeySerializer;
use Rtgm\ecc\RtEccFactory;
use Rtgm\sm\RtSm2;
use Rtgm\sm\RtSm3;
use Rtgm\util\MyAsn1;
use RuntimeException;

class GmSm
{

    private static ?string $publicKey = null;
    private static ?string $privateKey = null;
    private static ?string $publicKeyPath = null;
    private static ?string $privateKeyPath = null;

    private static ?string $cachedPublicKey = null;
    private static ?string $cachedPrivateKey = null;
    private static ?string $cachedPublicKeyPath = null;
    private static ?string $cachedPrivateKeyPath = null;

    /**
     * 配置 SM2
     *
     * @param string|null $publicKey 公钥字符串
     * @param string|null $privateKey 私钥字符串
     * @param string|null $publicKeyPath 公钥文件路径
     * @param string|null $privateKeyPath 私钥文件路径
     */
    public static function configure(?string $publicKey = null, ?string $privateKey = null, ?string $publicKeyPath = null, ?string $privateKeyPath = null): void {
        if (self::$publicKey !== $publicKey || self::$privateKey !== $privateKey || self::$publicKeyPath !== $publicKeyPath || self::$privateKeyPath !== $privateKeyPath) {
            self::clearCache();
        }

        self::$publicKey = $publicKey;
        self::$privateKey = $privateKey;
        self::$publicKeyPath = $publicKeyPath;
        self::$privateKeyPath = $privateKeyPath;
    }

    /**
     * 清除密钥缓存
     */
    public static function clearCache(): void {
        self::$cachedPublicKey = null;
        self::$cachedPrivateKey = null;
        self::$cachedPublicKeyPath = null;
        self::$cachedPrivateKeyPath = null;
    }

    /**
     * 将当前配置的密钥转换为 PEM 格式的密钥文本
     *
     * @return array 返回转换后的密钥文本 ['publicKey' => string|null, 'privateKey' => string|null]
     * @throws RuntimeException
     */
    public static function exportKeyContent(): array {
        try {
            $result = [];
            $adapter = RtEccFactory::getAdapter();
            $generator = RtEccFactory::getSmCurves()
                                     ->generatorSm2();

            // 处理私钥
            if (self::$privateKey) {
                try {
                    // 将十六进制私钥转换为 GMP
                    $secret = gmp_init(self::$privateKey, 16);
                    $privateKey = new \Mdanter\Ecc\Crypto\Key\PrivateKey($adapter, $generator, $secret);

                    // 创建私钥序列化器
                    $derPrivKeySerializer = new DerPrivateKeySerializer($adapter);
                    $pemPrivKeySerializer = new PemPrivateKeySerializer($derPrivKeySerializer);

                    // 序列化私钥
                    $result['privateKey'] = $pemPrivKeySerializer->serialize($privateKey);
                } catch (\Exception $e) {
                    throw new CryptoException("私钥编码失败: " . $e->getMessage(), CryptoException::ERROR_EXPORT_FAILED);
                }
            }

            // 处理公钥
            if (self::$publicKey) {
                try {
                    // 解析公钥点坐标
                    $pubKey = self::$publicKey;
                    if (strlen($pubKey) == 130 && str_starts_with($pubKey, '04')) {
                        $pubKey = substr($pubKey, 2);
                    }
                    $pubKeyX = substr($pubKey, 0, 64);
                    $pubKeyY = substr($pubKey, -64);

                    // 创建公钥点
                    $point = new \Mdanter\Ecc\Primitives\Point($adapter, $generator->getCurve(), gmp_init($pubKeyX, 16), gmp_init($pubKeyY, 16));

                    // 创建公钥对象
                    $publicKey = new \Mdanter\Ecc\Crypto\Key\PublicKey($adapter, $generator, $point);

                    // 创建公钥序列化器
                    $derPubKeySerializer = new DerPublicKeySerializer($adapter);
                    $pemPubKeySerializer = new PemPublicKeySerializer($derPubKeySerializer);

                    // 序列化公钥
                    $result['publicKey'] = $pemPubKeySerializer->serialize($publicKey);
                } catch (\Exception $e) {
                    throw new CryptoException("公钥编码失败: " . $e->getMessage(), CryptoException::ERROR_EXPORT_FAILED);
                }
            }

            if (empty($result)) {
                throw new CryptoException("没有可导出的密钥", CryptoException::ERROR_NO_KEY);
            }

            return $result;
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException("密钥导出失败: " . $e->getMessage(), CryptoException::ERROR_EXPORT_FAILED);
        }
    }

    /**
     * 将密钥内容写入文件
     *
     * @param string|null $publicKeyPath 公钥保存路径
     * @param string|null $privateKeyPath 私钥保存路径
     * @param bool $updateConfig 是否更新当前配置以使用新生成的文件
     *
     * @return array 返回生成的文件路径
     * @throws RuntimeException
     */
    public static function saveKeyToFile(?string $publicKeyPath = null, ?string $privateKeyPath = null, bool $updateConfig = false): array {
        try {
            $keyContents = self::exportKeyContent();
            $result = [];

            // 保存公钥文件
            if (isset($keyContents['publicKey']) && $publicKeyPath) {
                $directory = dirname($publicKeyPath);
                if (!is_dir($directory)) {
                    if (!mkdir($directory, 0755, true)) {
                        throw new CryptoException("无法创建目录: $directory", CryptoException::ERROR_FILE_OPERATION);
                    }
                }

                if (file_put_contents($publicKeyPath, $keyContents['publicKey']) === false) {
                    throw new CryptoException("无法写入公钥文件: $publicKeyPath", CryptoException::ERROR_FILE_OPERATION);
                }

                $result['publicKeyPath'] = $publicKeyPath;

                if ($updateConfig) {
                    self::$publicKeyPath = $publicKeyPath;
                    self::clearCache();
                }
            }

            // 保存私钥文件
            if (isset($keyContents['privateKey']) && $privateKeyPath) {
                $directory = dirname($privateKeyPath);
                if (!is_dir($directory)) {
                    if (!mkdir($directory, 0755, true)) {
                        throw new CryptoException("无法创建目录: $directory", CryptoException::ERROR_FILE_OPERATION);
                    }
                }

                if (file_put_contents($privateKeyPath, $keyContents['privateKey']) === false) {
                    throw new CryptoException("无法写入私钥文件: $privateKeyPath", CryptoException::ERROR_FILE_OPERATION);
                }

                $result['privateKeyPath'] = $privateKeyPath;

                if ($updateConfig) {
                    self::$privateKeyPath = $privateKeyPath;
                    self::clearCache();
                }
            }

            return $result;
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException("文件操作失败: " . $e->getMessage(), CryptoException::ERROR_FILE_OPERATION);
        }

    }

    /**
     * 验证公钥配置
     *
     * @throws CryptoException
     */
    private static function validatePublicKey(): void {
        if (empty(self::$publicKey)) {
            throw new CryptoException('公钥未配置', CryptoException::ERROR_NO_KEY);
        }

        // 验证公钥格式
        if (strlen(self::$publicKey) != 130 && strlen(self::$publicKey) != 128) {
            throw new CryptoException('无效的公钥格式', CryptoException::ERROR_INVALID_KEY);
        }
    }

    /**
     * 验证私钥配置
     *
     * @throws CryptoException
     */
    private static function validatePrivateKey(): void {
        if (empty(self::$privateKey)) {
            throw new CryptoException('私钥未配置', CryptoException::ERROR_NO_KEY);
        }

        // 验证私钥长度
        if (strlen(self::$privateKey) != 64) {
            throw new CryptoException('无效的私钥格式', CryptoException::ERROR_INVALID_KEY);
        }
    }

    /**
     * 验证密钥配置
     *
     * @param bool $requirePublic 是否需要公钥
     * @param bool $requirePrivate 是否需要私钥
     *
     * @throws CryptoException
     */
    private static function validateKeyConfiguration(bool $requirePublic = true, bool $requirePrivate = true): void {
        if ($requirePublic) {
            self::validatePublicKey();
        }
        if ($requirePrivate) {
            self::validatePrivateKey();
        }
    }

    /**
     * 获取有效的密钥，优先使用缓存
     *
     * @param bool $requirePublic 是否需要公钥
     * @param bool $requirePrivate 是否需要私钥
     *
     * @return array [publicKey, privateKey]
     */
    private static function getEffectiveKeys(bool $requirePublic = true, bool $requirePrivate = true): array {
        try {
            $publicKey = self::$publicKey;
            $privateKey = self::$privateKey;

            // 处理公钥文件
            if ($requirePublic && self::$publicKeyPath) {
                if (self::$cachedPublicKey !== null && self::$cachedPublicKeyPath === self::$publicKeyPath) {
                    $publicKey = self::$cachedPublicKey;
                } else {
                    try {
                        if (!file_exists(self::$publicKeyPath)) {
                            throw new CryptoException('公钥文件不存在: ' . self::$publicKeyPath, CryptoException::ERROR_FILE_OPERATION);
                        }
                        $decodedPubKey = MyAsn1::decode_file(self::$publicKeyPath);
                        $publicKey = $decodedPubKey[1];
                        self::$cachedPublicKey = $publicKey;
                        self::$cachedPublicKeyPath = self::$publicKeyPath;
                    } catch (\Exception $e) {
                        throw new CryptoException('从文件载入公钥失败: ' . $e->getMessage(), CryptoException::ERROR_FILE_OPERATION);
                    }
                }
            }

            // 处理私钥文件
            if ($requirePrivate && self::$privateKeyPath) {
                if (self::$cachedPrivateKey !== null && self::$cachedPrivateKeyPath === self::$privateKeyPath) {
                    $privateKey = self::$cachedPrivateKey;
                } else {
                    try {
                        if (!file_exists(self::$privateKeyPath)) {
                            throw new CryptoException('私钥文件不存在: ' . self::$privateKeyPath, CryptoException::ERROR_FILE_OPERATION);
                        }
                        $decodedPrivKey = MyAsn1::decode_file(self::$privateKeyPath);
                        $privateKey = $decodedPrivKey[1];
                        self::$cachedPrivateKey = $privateKey;
                        self::$cachedPrivateKeyPath = self::$privateKeyPath;
                    } catch (\Exception $e) {
                        throw new CryptoException('从文件载入私钥失败: ' . $e->getMessage(), CryptoException::ERROR_FILE_OPERATION);
                    }
                }
            }

            if ($requirePublic && !$publicKey) {
                throw new CryptoException('未能获取有效的公钥', CryptoException::ERROR_NO_KEY);
            }
            if ($requirePrivate && !$privateKey) {
                throw new CryptoException('未能获取有效的私钥', CryptoException::ERROR_NO_KEY);
            }

            return [
                $publicKey,
                $privateKey,
            ];
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException('获取密钥失败: ' . $e->getMessage(), CryptoException::ERROR_KEY_GENERATION);
        }
    }

    /**
     * 使用 SM2 公钥加密数据
     *
     * @param string $data 要加密的数据
     *
     * @return string 加密后的数据
     * @throws CryptoException
     */
    public static function sm2Encrypt(string $data): string {
        try {
            self::validateKeyConfiguration(true, false);
            [$publicKey,] = self::getEffectiveKeys(true, false);
            $sm2 = new RtSm2();

            return $sm2->doEncrypt($data, $publicKey);
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException('加密失败：' . $e->getMessage(), CryptoException::ERROR_ENCRYPT_FAILED);
        }
    }

    /**
     * 使用 SM2 私钥解密数据
     *
     * @param string $encryptedData 要解密的数据
     *
     * @return string 解密后的数据
     */
    public static function sm2Decrypt(string $encryptedData): string {
        try {
            self::validateKeyConfiguration(false, true);
            [, $privateKey] = self::getEffectiveKeys(false, true);
            $sm2 = new RtSm2();

            return $sm2->doDecrypt($encryptedData, $privateKey);
        } catch (\Exception $e) {
            throw new CryptoException('解密失败：' . $e->getMessage(), CryptoException::ERROR_DECRYPT_FAILED);
        }
    }

    /**
     * 使用 SM2 私钥签名数据
     *
     * @param string $data 要签名的数据
     * @param string|null $userId 可选的用户ID用于签名
     * @param string $format 输出格式 ('hex' 或 'base64')
     *
     * @return string Signature
     */
    public static function sm2Sign(string $data, ?string $userId = null, string $format = 'hex'): string {
        try {
            if (!in_array($format, ['hex', 'base64'])) {
                throw new CryptoException('不支持的输出格式，仅支持 hex 或 base64', CryptoException::ERROR_INVALID_FORMAT);
            }

            self::validateKeyConfiguration(false, true);
            [, $privateKey] = self::getEffectiveKeys(false, true);
            $sm2 = new RtSm2($format);

            $result = $sm2->doSign($data, $privateKey, $userId);

            //            if (empty($result)) {
            //                throw new CryptoException('签名生成失败', CryptoException::ERROR_SIGN_FAILED);
            //            }

            return $result;
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException('签名失败：' . $e->getMessage(), CryptoException::ERROR_SIGN_FAILED);
        }
    }

    /**
     * 使用 SM2 公钥验证签名
     *
     * @param string $data 原始数据
     * @param string $signature 要验证的签名
     * @param string|null $userId 可选的用户ID用于签名
     * @param string $format 输入格式 ('hex' 或 'base64')
     *
     * @return bool 验证结果
     */
    public static function sm2VerifySign(string $data, string $signature, ?string $userId = null, string $format = 'hex'): bool {
        try {
            if (!in_array($format, ['hex', 'base64'])) {
                throw new CryptoException('不支持的输入格式，仅支持 hex 或 base64', CryptoException::ERROR_INVALID_FORMAT);
            }

            self::validateKeyConfiguration(true, false);
            [$publicKey,] = self::getEffectiveKeys(true, false);
            $sm2 = new RtSm2($format);

            return $sm2->verifySign($data, $signature, $publicKey, $userId);
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException('验签失败：' . $e->getMessage(), CryptoException::ERROR_VERIFY_FAILED);
        }
    }

    /**
     * Generate SM3 digest
     *
     * @param string $data Data to digest
     * @param bool $binary Whether to return raw binary
     *
     * @return string SM3 digest
     */
    public static function sm3Digest(string $data): string {
        try {
            $sm3 = new RtSm3();
            $result = $sm3->digest($data, true);
            //
            //            if (empty($result)) {
            //                throw new CryptoException('SM3摘要生成失败', CryptoException::ERROR_ENCRYPT_FAILED);
            //            }

            return $result;
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException('SM3摘要生成失败：' . $e->getMessage(), CryptoException::ERROR_ENCRYPT_FAILED);
        }
    }

    /**
     * 获取公钥key和私钥key
     * @return array
     */
    public static function getKeys(): array {
        try {
            // 检查是否配置了任何密钥
            if (!self::$publicKey && !self::$publicKeyPath && !self::$privateKey && !self::$privateKeyPath) {
                throw new CryptoException('未配置任何密钥', CryptoException::ERROR_NO_KEY);
            }

            $result = [];

            // 获取公钥（如果已配置）
            if (self::$publicKey || self::$publicKeyPath) {
                [$publicKey,] = self::getEffectiveKeys(true, false);
                $result['publicKey'] = $publicKey;
            }

            // 获取私钥（如果已配置）
            if (self::$privateKey || self::$privateKeyPath) {
                [, $privateKey] = self::getEffectiveKeys(false, true);
                $result['privateKey'] = $privateKey;
            }

            return $result;
        } catch (CryptoException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new CryptoException('获取密钥失败: ' . $e->getMessage(), CryptoException::ERROR_KEY_GENERATION);
        }
    }
}
