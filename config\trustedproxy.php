<?php

return [

    /*
     * Set trusted proxy IP addresses.
     *
     * Both IPv4 and IPv6 addresses are
     * supported, along with CIDR notation.
     *
     * The "*" character is syntactic sugar
     * within TrustedProxy to trust any proxy
     * that connects directly to your server,
     * a requirement when you cannot know the address
     * of your proxy (e.g. if using ELB or similar).
     *
     */
    'proxies' => [
        '*************/24',
        '*************/27',
        '*************/24',
        '************/24',
        '************/24',
        '************/24',
        '************/24',
        '************/24',
        '***********/24',
        '************/25',
        '*************/26',
        '***********/24',
        '************/24',
        '*************/27',
        '**************/26',
        '**************/26',
        '***********/24',
        '***********/24',
        '***********/24',
        '************/27',
        '************/25',
        '***********/26',
        '***********/24',
        '*************/26',
        '************/24',
        '************/27',
        '**********/25',
        '**********/24',
        '*************/27',
        '*************/27',
        '2408:400a:3c:4800::/56',
        '2408:4005:1:be00::/56',
        '2408:4003:10d4:2e00::/56',
    ],
    // [<ip addresses>,], ' * ', ' < ip addresses >,'

    /*
     * To trust one or more specific proxies that connect
     * directly to your server, use an array or a string separated by comma of IP addresses:
     */
    // 'proxies' => ['***********'],
    // 'proxies' => '***********, ***********',

    /*
     * Or, to trust all proxies that connect
     * directly to your server, use a "*"
     */
    // 'proxies' => ' * ',

    /*
     * Which headers to use to detect proxy related data (For, Host, Proto, Port)
     *
     * Options include:
     *
     * - Illuminate\Http\Request::HEADER_X_FORWARDED_ALL (use all x-forwarded-* headers to establish trust)
     * - Illuminate\Http\Request::HEADER_FORWARDED (use the FORWARDED header to establish trust)
     * - Illuminate\Http\Request::HEADER_X_FORWARDED_AWS_ELB (If you are using AWS Elastic Load Balancer)
     *
     * - 'HEADER_X_FORWARDED_ALL' (use all x-forwarded-* headers to establish trust)
     * - 'HEADER_FORWARDED' (use the FORWARDED header to establish trust)
     * - 'HEADER_X_FORWARDED_AWS_ELB' (If you are using AWS Elastic Load Balancer)
     *
     * @link https://symfony.com/doc/current/deployment/proxies.html
     */
    'headers' => Illuminate\Http\Request::HEADER_X_FORWARDED_ALL,

];
