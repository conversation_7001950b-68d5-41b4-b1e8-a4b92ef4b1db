<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ai_video_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->string('prompt')->nullable()->comment('关键词');
            $table->string('module')->nullable()->comment('模型名称');
            $table->string('provider')->nullable()->comment('服务提供者');
			
			$table->string('video_origin')->nullable()->comment('原始图片路径');
			$table->string('video_local')->nullable()->comment('本地视频路径');
		
            $table->double('width')->nullable()->comment('视频宽度');
            $table->double('height')->nullable()->comment('视频高度');
			
			
			$table->string('image_base')->nullable()->comment('参考图完整URL');
			$table->string('style')->nullable()->comment('视频风格');
			$table->string('ratio')->nullable()->comment('比例');
			$table->string('motion_strength')->nullable()->comment('运动强度');
			$table->string('motion_scale')->nullable()->comment('运动模式 json格式');
			$table->string('asset_id')->nullable()->comment('角色ID pixverse专用');
			$table->string('seed')->nullable()->comment('选填,种子,不传则随机');
			
			$table->string('price')->nullable()->comment('视频价格');
			$table->string('point')->nullable()->comment('视频点数');
			
			$table->string('failed_reason')->nullable()->comment('失败原因');
			
            $table->integer('progress')->nullable()->comment('图片生成总进度，进度包含2种，0为未处理完，1为处理完成');
            $table->string('status')->nullable()->comment('有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态');
            $table->string('task_id')->nullable()->index('task_id')->comment('任务ID');
            $table->integer('user_id')->nullable()->comment('用户ID');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_video_histories');
    }
};
