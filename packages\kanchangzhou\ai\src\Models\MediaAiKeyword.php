<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiKeyword
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $keyword 关键词
 * @property float|null $probalility 概率
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword whereKeyword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword whereProbalility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiKeyword whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiKeyword
 */
class MediaAiKeyword extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }
}
