<?php
namespace Kanchangzhou\AI\Services\Audio\Providers\Azure;

use GuzzleHttp\Client;

class Auth
{
    private $accessToken;

    public function __construct($accessKeyId, $accessKeySecret) {
        $this->setAccessToken($accessKeyId, $accessKeySecret);
    }

    public function setAccessToken($accessKeyId, $accessKeySecret, $isForce = true) {
        $this->accessToken = cache()->get('azure_api_access_token');

        if (!$this->accessToken || $isForce) {
            try {
                $fetchTokenUrl = 'https://eastus.api.cognitive.microsoft.com/sts/v1.0/issueToken';
                $headers = [
                    'Ocp-Apim-Subscription-Key' => $accessKeySecret,
                ];

                $client = new Client();
                $response = $client->post($fetchTokenUrl, [
                    'headers' => $headers,
                ]);

                $this->accessToken = $response->getBody()->getContents();

                //10分钟有效
                cache()->put('azure_api_access_token', $this->accessToken, 10);
            } catch (ClientException|ServerException $e) {
                throw new \Exception($e->getMessage());
            }
        }
    }

    public function getAccessToken(){
        return $this->accessToken;
    }
}
