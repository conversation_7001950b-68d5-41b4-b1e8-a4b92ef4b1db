<?php
namespace Kanchangzhou\AI\Services\Audio\Providers\Volcengine\Config;

use Kanchangzhou\AI\Services\Audio\Contacts\AiTtsConfigInterface;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\AI\Services\AiUser;

class TextConfig implements AiTtsConfigInterface
{
    private $appid;
    private $token;
    private $cluster;
    private $uid;
    private $voice_type;
    private $encoding;
    private $speed_ratio;
    private $reqid;
    private $text;
    private $operation;

    public function __construct($config = []) {

        $aiUser = new AiUser(AuthFacade::adminUser()
            ->getId(), 'kadmin');

        //app
        $this->appid = config('kai.tts.providers.volcengine.appid');
        $this->token = \Str::uuid()->toString();
        $this->cluster = 'volcano_mega';

        //user
        $this->uid = $aiUser->getUserId();

        //audio
        $this->voice_type =  $config['voice_type'] ?? 'BV001_streaming';
        $this->encoding = $config['encoding'] ?? 'mp3';
        $this->compression_rate = $config['compression_rate'] ?? 1;
        $this->rate = $config['rate'] ?? 24000;
        $this->speed_ratio = $config['speed_ratio'] ?? '1.0';
        $this->volume_ratio = $config['volume_ratio'] ?? '1.0';
        $this->pitch_ratio = $config['pitch_ratio'] ?? '1.0';
        $this->emotion = $config['emotion'] ?? '';
        $this->language = $config['language'] ?? 'cn';

        //request
        $this->reqid = $config['req_id'] ?? \Str::uuid()->toString();
        $this->text = $config['text'];
        $this->text_type = $config['text_type'] ?? 'plain';
        $this->silence_duration = $config['silence_duration'] ?? 125;
        $this->operation = $config['operation'] ?? 'query';
        $this->with_frontend = $config['operation'] ?? '';
        $this->frontend_type = $config['frontend_type'] ?? '';
        $this->with_timestamp = $config['with_timestamp'] ?? '';
        $this->split_sentence = $config['split_sentence'] ?? '';
        $this->pure_english_opt = $config['pure_english_opt'] ?? '';
    }

    public function toArray() {
        $array = [
            'app' => [
                'appid'=>$this->appid,
                'token'=>$this->token,
                'cluster'=>$this->cluster,

            ],
            'user' => [
                'uid'=>$this->uid,
            ],


            'audio' => [
                'voice_type'=>$this->voice_type,
                'encoding'=>$this->encoding,
                'compression_rate'=>$this->compression_rate,
                'rate'=>$this->rate,
                'speed_ratio'=>$this->speed_ratio,
                'volume_ratio'=>$this->volume_ratio,
                'pitch_ratio'=>$this->pitch_ratio,
                'emotion'=>$this->emotion,
                'language'=>$this->language,
            ],

            'request' => [
                'reqid'=>$this->reqid,
                'text'=>$this->text,
                'text_type'=>$this->text_type,
                'silence_duration'=>$this->silence_duration,
                'operation'=>$this->operation,
                'with_frontend'=>$this->with_frontend,
                'frontend_type'=>$this->frontend_type,
                'with_timestamp'=>$this->with_timestamp,
                'split_sentence'=>$this->split_sentence,
                'pure_english_opt'=>$this->pure_english_opt,
            ],
        ];

        return $array;
    }

    public function toJson() {
        return json_encode($this->toArray());
    }
}
