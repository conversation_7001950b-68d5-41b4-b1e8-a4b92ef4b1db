<?php

namespace Kanchangzhou\AI\Http\Middlewares;

use Closure;
use Illuminate\Http\Request;
use Kanchangzhou\AI\Models\ChatApiKey;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\AuthFailException;
use Kanchangzhou\User\Facades\UserFacade;
use Illuminate\Support\Facades\Auth;

class AiApiMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next) {

        $key = $request->header('XW-Api-Key');
        $user = ChatApiKey::where('key', $key)
                          ->first();

        if (!$user) {
            throw new AuthFailException(AuthFailException::UNAUTHENTICATED, [], 401);
        }

        if (!Auth::guard($user->guard_name)
                 ->byId($user->user_id)) {
            throw new AuthFailException(AuthFailException::UNAUTHENTICATED, [], 401);
        }

        return $next($request);
    }
}
