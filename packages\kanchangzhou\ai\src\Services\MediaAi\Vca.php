<?php

namespace Kanchangzhou\AI\Services\MediaAi;

use Illuminate\Support\Str;

class Vca extends BaseInterface
{
    protected $taskType = 'vca';

    public function handleFiles($files) {
        // 校验并处理文件
        $preparedFiles = [];

        foreach ($files as $file) {
            // 判断file的path是否为url, 如果是url, 则调用remoteUpload方法上传文件
            // 判断file的path地址为 livehls.cztv.tv,则使用 remoteupload 方法上传文件

            $file['path'] = $this->remoteUpload($file['path']);

            $preparedFile = [
                'id' => Str::uuid()->toString(),
                'type' => 'video',
                'path' => $file['path'],
            ];
            $preparedFiles[] = $preparedFile;
        }

        return $preparedFiles;
    }

    public function handleResult($result) {
        return $result['data'];
    }

    public function vca($files, $options = []) {
        return $this->dispatchTask($files, $options);
    }

    public function vcaSynch($files, $options = []) {
        return $this->dispatchTask($files, ['callPattern' => 'Synch']);
    }
}
