<?php

namespace Kanchangzhou\AI\Commands;

use AlibabaCloud\SDK\Live\*********\Live;
use AlibabaCloud\SDK\Live\*********\Models\ForbidLiveStreamRequest;
use AlibabaCloud\SDK\Live\*********\Models\ResumeLiveStreamRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Carbon\Carbon;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\Livechannel\Models\Backend\Channel;
use Kanchangzhou\Livechannel\Models\Backend\Programme;
use Kanchangzhou\Livechannel\Models\Backend\ProgramTemplate;

class LiveChannelShieldCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audit:live-channel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '直播频道屏蔽';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $channels = Channel::where('status', 1)
                           ->where('category_id', 2)
//                           ->where('id', 14)
                           ->get();

        foreach ($channels as $channel) {
            $programme = Programme::where('channel_id', $channel->id)
                                  ->where('status', 1)
                                  ->first();

            $shieldWeek = Carbon::now()->subMinutes(5)->weekday()?:7;
            // 屏蔽时间点
            $program = ProgramTemplate::where('channel_id', $channel->id)
                                      ->where('programme_id', $programme->id)
                                      ->where('status', 1)
                                      ->where('is_shield', 1)
                                      ->where('weeks', $shieldWeek - 1)
                                      ->where('program_time', Carbon::now()
                                                                    ->subMinutes(5)
                                                                    ->toTimeString('minute'))
                                      ->orderBy('program_time')
                                      ->first();

            if ($program) {
                // 调用禁推
                // TODO 阿里云禁推

                if (\Cache::tags(['shield_channel'])
                          ->get($channel->app_name . '_' . $channel->stream_name) == 'offline') {
                    info('shield', [
                        'operate' => '维持禁推',
                        'channel' => $channel->app_name,
                        'stream' => $channel->stream_name,
                        'program' => $program->program_name,
                        'program_time' => $program->program_time,
                        'is_shield' => $program->is_shield,
                    ]);
                } else {
                    [
                        $hour,
                        $minute,
                    ] = explode(':', $program->program_time);

                    $nextProgramWeek = Carbon::createFromTime($hour, $minute)
                                                                         ->addMinute()
                                                                         ->weekday()?:7;

                    $nextProgram = ProgramTemplate::where('channel_id', $channel->id)
                                                  ->where('programme_id', $programme->id)
                                                  ->where('status', 1)
                                                  ->where('is_shield', 0)
                                                  ->where('weeks', $nextProgramWeek - 1)
                                                  ->where('program_time', '>', Carbon::createFromTime($hour, $minute)
                                                                                     ->addMinute()
                                                                                     ->toTimeString('minute'))
                                                  ->orderBy('program_time')
                                                  ->first();

                    $date = Carbon::createFromTime($hour, $minute)
                                  ->addMinute()
                                  ->toDateString();

                    if (!$nextProgram) {
                        $nextProgramWeek = Carbon::create($hour, $minute)->addDay()->weekday()?:7;
                        $nextProgram = ProgramTemplate::where('channel_id', $channel->id)
                                                      ->where('programme_id', $programme->id)
                                                      ->where('status', 1)
                                                      ->where('is_shield', 0)
                                                      ->where('weeks', $nextProgramWeek - 1)
                                                      ->where('program_time', '>', '00:00')
                                                      ->orderBy('program_time')
                                                      ->first();
                        $date = Carbon::createFromTime($hour, $minute)
                                      ->addDay()
                                      ->toDateString();
                    }

                    [
                        $nexHour,
                        $nextMinute,
                    ] = explode(':', $nextProgram->program_time);

                    $resumeTime = Carbon::create($date . ' ' . $nextProgram->program_time);
//dd($resumeTime);
                    // 跨度大约15分钟再执行禁推屏蔽
                    if (Carbon::createFromTime($hour, $minute)
                              ->diffInMinutes($resumeTime) > 15) {

                        Http::post('http://monitor.xiao5.cn/api/forbid-state', [
                            'status' => 'block',
                            'app_name' => $channel->app_name,
                            'stream_name' => $channel->stream_name,
                        ]);

                        $config = new Config([
                            'accessKeyId' => config('kai.other.aliyun.shield.key'),
                            'accessKeySecret' => config('kai.other.aliyun.shield.secret'),
                        ]);
                        $config->endpoint = 'live.aliyuncs.com';

                        $client = new Live($config);
                        $forbidLiveStreamRequest = new ForbidLiveStreamRequest([
                            'domainName' => 'livekcz.cztv.tv',
                            'appName' => $channel->app_name,
                            'streamName' => $channel->stream_name,
                            "liveStreamType" => "publisher",
                            'resumeTime' => $resumeTime->subMinutes(4)
                                                       ->utc()
                                                       ->format("Y-m-d\TH:i:s\Z"),
                        ]);

//                        dd($forbidLiveStreamRequest->toMap());

                        $runtime = new RuntimeOptions([]);
                        $client->forbidLiveStreamWithOptions($forbidLiveStreamRequest, $runtime);

                        info('shield', [
                            'operate' => '开始禁推',
                            'channel' => $channel->app_name,
                            'stream' => $channel->stream_name,
                            'program' => $program->program_name,
                            'program_time' => $program->program_time,
                            'is_shield' => $program->is_shield,
                            'recover_time' => $nextProgram->program_time,
                        ]);

                        \Cache::tags(['shield_channel'])
                              ->put($channel->app_name . '_' . $channel->stream_name, 'offline');

                    } else {
                        info('shield', [
                            'operate' => '间隔过短不禁推',
                            'channel' => $channel->app_name,
                            'stream' => $channel->stream_name,
                            'program' => $program->program_name,
                            'program_time' => $program->program_time,
                            'is_shield' => $program->is_shield,
                            'recover_time' => $nextProgram->program_time,
                        ]);
                    }
                }
            }

            // 恢复推流
            $recoverProgramWeek = Carbon::now()->addMinutes(5)->weekday()?:7;
            $recoverProgram = ProgramTemplate::where('channel_id', $channel->id)
                                             ->where('programme_id', $programme->id)
                                             ->where('status', 1)
                                             ->where('is_shield', 0)
                                             ->where('weeks', $recoverProgramWeek - 1)
                                             ->where('program_time', Carbon::now()
                                                                           ->addMinutes(5)
                                                                           ->toTimeString('minute'))
                                             ->orderBy('date')
                                             ->orderBy('program_time')
                                             ->first();

            if ($recoverProgram) {
                // 恢复推流
                // TODO 阿里云恢复推流

                if (\Cache::tags(['shield_channel'])
                          ->get($channel->app_name . '_' . $channel->stream_name) == 'online') {
                    info('recover', [
                        'operate' => '维持推流',
                        'channel' => $channel->app_name,
                        'stream' => $channel->stream_name,
                        'program' => $recoverProgram->program_name,
                        'program_time' => $recoverProgram->program_time,
                        'is_shield' => $recoverProgram->is_shield,
                    ]);
                } else {
                    // 通知监控不需要发信息
                    Http::post('http://monitor.xiao5.cn/api/forbid-state', [
                        'status' => 'unblock',
                        'app_name' => $channel->app_name,
                        'stream_name' => $channel->stream_name,
                    ]);

                    $config = new Config([
                        'accessKeyId' => config('kai.other.aliyun.shield.key'),
                        'accessKeySecret' => config('kai.other.aliyun.shield.secret'),
                    ]);
                    $config->endpoint = 'live.aliyuncs.com';

                    $client = new Live($config);

                    $resumeLiveStreamRequest = new ResumeLiveStreamRequest([
                        "domainName" => "livekcz.cztv.tv",
                        "liveStreamType" => "publisher",
                        'appName' => $channel->app_name,
                        'streamName' => $channel->stream_name,
                    ]);

                    $runtime = new RuntimeOptions([]);
                    $client->resumeLiveStreamWithOptions($resumeLiveStreamRequest, $runtime);

                    info('recover', [
                        'operate' => '恢复推流',
                        'channel' => $channel->app_name,
                        'stream' => $channel->stream_name,
                        'program' => $recoverProgram->program_name,
                        'program_time' => $recoverProgram->program_time,
                        'is_shield' => $recoverProgram->is_shield,
                    ]);

                    \Cache::tags(['shield_channel'])
                          ->put($channel->app_name . '_' . $channel->stream_name, 'online');
                }
            }
        }

        return true;
    }
}
