<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->uuid('role_uuid')
                  ->after('id')
                  ->comment('角色UUID');
            $table->tinyInteger('enable_context')
                  ->nullable()
                  ->default(1)
                  ->after('examples')
                  ->comment('是否启用上下文');
            $table->tinyInteger('context_count')
                  ->nullable()
                  ->default(10)
                  ->after('enable_context')
                  ->comment('上下文数量');

            $table->integer('owner_id')
                  ->nullable()
                  ->default(0)
                  ->after('context_count')
                  ->comment('所有者ID');
            $table->tinyInteger('is_published')
                  ->default(1)
                  ->after('owner_id')
                  ->comment('是否发布');
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->dropColumn('role_uuid');
            $table->dropColumn('enable_context');
            $table->dropColumn('context_count');
            $table->dropColumn('owner_id');
            $table->dropColumn('is_published');
            $table->dropSoftDeletes();
        });
    }
};
