<?php

namespace Kanchangzhou\Advertisement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\Advertisement\Models\AdvertisingPositionType
 *
 * @property int $id
 * @property string $position_type_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\Advertisement\Models\AdvertisingPosition> $positionInfo
 * @property-read int|null $position_info_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType wherePositionTypeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPositionType withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdvertisingPositionType
 */
class AdvertisingPositionType extends Model
{
    use SoftDeletes;
    //
    protected $table = 'advertising_position_type';

    public function positionInfo(){
        return $this->hasMany("Kanchangzhou\Advertisement\Models\AdvertisingPosition", "position_type_id", "id");
    }
}
