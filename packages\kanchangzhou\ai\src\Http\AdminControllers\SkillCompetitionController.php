<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Kanchangzhou\AI\Exceptions\SkillCompetitionExcetion;
use Kanchangzhou\AI\Http\Resources\SkillCompetitionProjectResource;
use Kanchangzhou\AI\Jobs\SkillCompetitionAiScoreJob;
use Kanchangzhou\AI\Models\SkillCompetitionScore;
use Kanchangzhou\AI\Models\SkillCompetition;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class SkillCompetitionController extends BaseController
{
    // 作品提交
    public function saveProject(Request $request) {
        PermissionHook::can('技能大赛.参赛');

        $this->validate($request, [
            'title' => 'required|max:255',
            'content' => 'required|array|min:1',
            'content.*.hua_mian' => '',
            'content.*.jing_bie' => '',
            'content.*.chang_jing' => '',
            'content.*.nei_rong' => '',
            'content.*.wen_an' => '',
            'content.*.bei_zhu' => '',
            'content.*.shi_pin' => '',
            'content.*.yu_yin' => '',
            'background_music' => 'nullable|max:1000',
        ], [
            'title.required' => '标题不能为空',
            'title.max' => '标题不能超过255个字符',
            'content.required' => '分镜头数据不能为空',
            'content.array' => '分镜头数据形式不正确, 必须是数组',
            'content.min' => '分镜头数据最少需要一个分镜',
            'background_music.max' => '背景音乐不能超过1000个字符',
        ]);

        $project = SkillCompetition::where('user_id', AuthFacade::adminUser()
                                                                ->getId())
                                   ->first();

        if ($project && $project->status == SkillCompetition::STATUS_FINISHED) {
            throw new SkillCompetitionExcetion(SkillCompetitionExcetion::HAS_BEEN_FINISHED);
        }

        $project = SkillCompetition::updateOrCreate([
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
        ], [
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'background_music' => $request->input('background_music'),
            'status' => SkillCompetition::STATUS_SAVED,
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'name' => AuthFacade::adminUser()
                                ->getName(),

        ]);

        return Respond::respondWithData();
    }

    public function finishProject(Request $request) {
        PermissionHook::can('技能大赛.参赛');

        $project = SkillCompetition::where('user_id', AuthFacade::adminUser()
                                                                ->getId())
                                   ->first();

        if ($project && $project->status == SkillCompetition::STATUS_FINISHED) {
            throw new SkillCompetitionExcetion(SkillCompetitionExcetion::HAS_BEEN_FINISHED);
        }

        $this->validate($request, [
            'title' => 'required|max:255',
            'content' => 'required|array|min:1',
            'content.*.hua_mian' => '',
            'content.*.jing_bie' => 'required',
            'content.*.chang_jing' => 'required',
            'content.*.nei_rong' => 'required',
            'content.*.wen_an' => 'required',
            'content.*.bei_zhu' => '',
            'content.*.shi_pin' => '',
            'content.*.yu_yin' => '',
            'background_music' => 'nullable|max:1000',
        ], [
            'title.required' => '标题不能为空',
            'title.max' => '标题不能超过255个字符',
            'content.required' => '分镜头数据不能为空',
            'content.array' => '分镜头数据形式不正确, 必须是数组',
            'content.min' => '分镜头数据最少需要一个分镜',
            'content.*.jing_bie.required' => '景别不能为空',
            'content.*.chang_jing.required' => '场景不能为空',
            'content.*.nei_rong.required' => '内容不能为空',
            'content.*.wen_an.required' => '文案不能为空',
            'background_music.max' => '背景音乐不能超过1000个字符',
        ]);

        $project = SkillCompetition::updateOrCreate([
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
        ], [
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'background_music' => $request->input('background_music'),
            'status' => SkillCompetition::STATUS_FINISHED,
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'name' => AuthFacade::adminUser()
                                ->getName(),

        ]);

        dispatch_sync(new SkillCompetitionAiScoreJob($project));

        return Respond::respondWithData();
    }

    // 评分
    public function scoreProject(Request $request, $projectId) {
        PermissionHook::can('技能大赛.评委');

        $project = SkillCompetition::where('id', $projectId)
                                   ->firstOrFail();

        if ($project->status == SkillCompetition::STATUS_SAVED) {
            throw new SkillCompetitionExcetion(SkillCompetitionExcetion::NOT_FINISHED);
        }

        $this->validate($request, [
            'wen_an_chuang_yi' => 'required|numeric|between:0,15',
            'wen_an_du_te_xing' => 'required|numeric|between:0,15',
            'zhu_ti_ming_que' => 'required|numeric|between:0,10',
            'nei_rong_jia_zhi' => 'required|numeric|between:0,15',
            'nei_rong_ke_xin' => 'required|numeric|between:0,5',
            'nei_rong_shen_du' => 'required|numeric|between:0,5',
            'jiao_ben_luo_ji' => 'required|numeric|between:0,5',
            'shi_jue_xiao_guo' => 'required|numeric|between:0,10',
        ], [
            'wen_an_chuang_yi.required' => '文案创意分不能为空',
            'wen_an_chuang_yi.numeric' => '文案创意分必须是数字',
            'wen_an_chuang_yi.between' => '文案创意分必须在0-15之间',
            'wen_an_du_te_xing.required' => '文案独特性分不能为空',
            'wen_an_du_te_xing.numeric' => '文案独特性分必须是数字',
            'wen_an_du_te_xing.between' => '文案独特性分必须在0-15之间',
            'zhu_ti_ming_que.required' => '主题明确分不能为空',
            'zhu_ti_ming_que.numeric' => '主题明确分必须是数字',
            'zhu_ti_ming_que.between' => '主题明确分必须在0-10之间',
            'nei_rong_jia_zhi.required' => '内容价值分不能为空',
            'nei_rong_jia_zhi.numeric' => '内容价值分必须是数字',
            'nei_rong_jia_zhi.between' => '内容价值分必须在0-15之间',
            'nei_rong_ke_xin.required' => '内容可信分不能为空',
            'nei_rong_ke_xin.numeric' => '内容可信分必须是数字',
            'nei_rong_ke_xin.between' => '内容可信分必须在0-5之间',
            'nei_rong_shen_du.required' => '内容深度分不能为空',
            'nei_rong_shen_du.numeric' => '内容深度分必须是数字',
            'nei_rong_shen_du.between' => '内容深度分必须在0-5之间',
            'jiao_ben_luo_ji.required' => '脚本逻辑分不能为空',
            'jiao_ben_luo_ji.numeric' => '脚本逻辑分必须是数字',
            'jiao_ben_luo_ji.between' => '脚本逻辑分必须在0-5之间',
            'shi_jue_xiao_guo.required' => '视觉效果分不能为空',
            'shi_jue_xiao_guo.numeric' => '视觉效果分必须是数字',
            'shi_jue_xiao_guo.between' => '视觉效果分必须在0-10之间',
        ]);

        SkillCompetitionScore::updateOrCreate([
            'skill_competition_id' => $projectId,
            'judge_id' => AuthFacade::adminUser()
                                    ->getId(),
        ], [
            'skill_competition_id' => $projectId,
            'judge_id' => AuthFacade::adminUser()
                                    ->getId(),
            'judge_name' => AuthFacade::adminUser()
                                      ->getName(),
            'score' => [
                'wen_an_chuang_yi' => $request->input('wen_an_chuang_yi'),
                'wen_an_du_te_xing' => $request->input('wen_an_du_te_xing'),
                'zhu_ti_ming_que' => $request->input('zhu_ti_ming_que'),
                'nei_rong_jia_zhi' => $request->input('nei_rong_jia_zhi'),
                'nei_rong_ke_xin' => $request->input('nei_rong_ke_xin'),
                'nei_rong_shen_du' => $request->input('nei_rong_shen_du'),
                'jiao_ben_luo_ji' => $request->input('jiao_ben_luo_ji'),
                'shi_jue_xiao_guo' => $request->input('shi_jue_xiao_guo'),
            ],
        ]);

        return Respond::respondWithData();
    }

    public function getProject($id) {
        PermissionHook::can('技能大赛.参赛|技能大赛.评委|技能大赛.管理');

        $project = SkillCompetition::with([
            'scores' => function ($query) {
                $query->when(PermissionHook::can('技能大赛.评委|技能大赛.管理', '', false), function ($query) {
                    if (!PermissionHook::can('技能大赛.管理', '', false)) {
                        $query->where('judge_id', AuthFacade::adminUser()
                                                            ->getId());
                    }
                })
                      ->orderBy('judge_id');
            },
        ])
                                   ->when($id, function ($query, $id) {
                                       $query->when(PermissionHook::can('技能大赛.管理|技能大赛.评委', '', false), function ($query) use ($id) {
                                           $query->where('id', $id);
                                       }, function ($query) {
                                           $query->where('user_id', AuthFacade::adminUser()
                                                                              ->getId());
                                       });
                                   }, function ($query) {
                                       $query->where('user_id', AuthFacade::adminUser()
                                                                          ->getId());
                                   })
                                   ->firstOrNew();

        return Respond::respondWithData(SkillCompetitionProjectResource::make($project));
    }

    public function projects() {
        PermissionHook::can('技能大赛.管理|技能大赛.评委');

        $projects = SkillCompetition::with([
            'scores' => function ($query) {
                $query->when(PermissionHook::can('技能大赛.评委|技能大赛.管理', '', false), function ($query) {
                    if (!PermissionHook::can('技能大赛.管理', '', false)) {
                        $query->where('judge_id', AuthFacade::adminUser()
                                                            ->getId());
                    }
                })
                      ->orderBy('judge_id');
            },
        ])
                                    ->orderBy('id')
                                    ->paginate();

        return Respond::respondWithData(SkillCompetitionProjectResource::collection($projects));
    }

    public function getUserPermissions() {
        return [
            'permission' => PermissionHook::hasRoles('超级管理员') ? '管理' : (PermissionHook::can('技能大赛.参赛', '', false) ? '参赛' : (PermissionHook::can('技能大赛.评委', '', false) ? '评委' : (PermissionHook::can('技能大赛.管理', '', false) ? '管理' : ''))),
            'create_btn' => boolval(PermissionHook::can('技能大赛.参赛', '', false)),
            'score_btn' => boolval(PermissionHook::can('技能大赛.评委|技能大赛.管理', '', false)),
        ];
    }

    public function aiScore(Request $request) {
        PermissionHook::can('技能大赛.管理');

        $project = SkillCompetition::where('id', $request->input('project_id'))
                                   ->firstOrFail();

        dispatch_sync(new SkillCompetitionAiScoreJob($project));

        return Respond::respondWithData();
    }
}
