<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;

class ResponseMediaAiOcrGeneralJob extends MediaAiBaseJob
{
// data : {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"95628aa0265c498f901f8f47f0349ad3","subDataTypes":[],"ocr/general":[{"fileId":"47f99a48-ad3c-497c-b443-63d2d0439163","statusCode":0,"statusInfo":"success","contents":[{"offset":0,"words":"《勇敢的心》","location":{"left":1367,"top":91,"width":346,"height":76},"confidence":1},{"offset":0,"words":"新闻综合","location":{"left":200,"top":135,"width":184,"height":42},"confidence":1}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {

    }
}
