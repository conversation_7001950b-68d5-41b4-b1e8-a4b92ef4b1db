<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\AiImage
 *
 * @property int $id
 * @property string|null $module_name 模型名
 * @property string|null $module_key 模型KEY
 * @property string|null $provider 服务
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $type
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage query()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereModuleKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereModuleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AiImage withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAiImage
 */
class AiImage extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $appends = [

    ];

    protected $casts = [

    ];

    const ROLE_MAP = [

    ];

    protected $typeMapping = [
        1 => 'txt2img',
        2 => 'txt2imgv2',
        // 可以添加更多的映射
    ];

    public function getTypeAttribute()
    {
        return $this->typeMapping[$this->type] ?? 2;
    }

}
