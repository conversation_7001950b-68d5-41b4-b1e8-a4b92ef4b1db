<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Http\Resources\ChatHistoryResource;
use Kanchangzhou\AI\Http\Resources\ChatRoleResource;
use Kanchangzhou\AI\Http\Resources\AgentsRoleResource;
use Kanchangzhou\AI\Http\Resources\ChatUsedRoleResource;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Models\ChatRoleUserFav;
use Kanchangzhou\AI\Models\ChatUsedRole;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class AgentsController extends BaseController
{
    public function index(Request $request) {
        PermissionHook::can('智能体.列表');

        $roles = ChatRole::when($request->input('my'), function ($query) {
            $query->where('owner_id', AuthFacade::adminUser()
                                                ->getId());
        }, function ($query) {
            $query->where('owner_id', '<>', 0);
        })
                         ->when($request->input('title'), function ($query, $title) {
                             $query->where('title', 'like', "%{$title}%");
                         })
                         ->when(!PermissionHook::can('智能场景.场景审核', '', false), function ($query) {
                             $query->where('is_published', ChatRole::IS_PUBLISHED_YES);
                         })
                         ->orderByDesc('is_published')
                         ->orderByDesc('id')
                         ->paginate(12);

        return Respond::respondWithData(AgentsRoleResource::collection($roles));
    }

    public function show($uuid) {
        PermissionHook::can('智能体.详情');

        $role = ChatRole::where('role_uuid', $uuid)
                        ->where('owner_id', '<>', 0)
                        ->firstOrFail();

        return Respond::respondWithData(AgentsRoleResource::make($role));

    }

    public function formOptions() {
        $defaultChatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                                     ->first();

        $temperature = ChatModel::TEMPERATURE_RANGE[$defaultChatModel->provider];
        $penalty = ChatModel::PENALTY_RANGE[$defaultChatModel->provider];

        return Respond::respondWithData(compact('temperature', 'penalty'));
    }

    public function store(Request $request) {
        PermissionHook::can('智能体.创建');

        $defaultChatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                                     ->first();

        $this->validate($request, [
            'title' => 'required',
            'prompt' => 'required',
            'temperature' => 'nullable|numeric|between:' . ChatModel::TEMPERATURE_RANGE[$defaultChatModel->provider]['min'] . ',' . ChatModel::TEMPERATURE_RANGE[$defaultChatModel->provider]['max'],
            'penalty' => 'nullable|numeric|between:' . ChatModel::PENALTY_RANGE[$defaultChatModel->provider]['min'] . ',' . ChatModel::PENALTY_RANGE[$defaultChatModel->provider]['max'],
            'examples' => 'nullable|array',
            'description' => '',
            'icon' => 'required',
        ]);

        $chatRole = ChatRole::create([
            'role_uuid' => Str::uuid(),
            'title' => $request->input('title'),
            'system_prompt' => $request->input('prompt'),
            'temperature' => $request->input('temperature', $defaultChatModel->temperature),
            'penalty' => $request->input('penalty', $defaultChatModel->penalty),
            'examples' => $request->input('examples', []),
            'description' => $request->input('description', ''),
            'owner_id' => AuthFacade::adminUser()
                                    ->getId(),
            'owner_nickname' => AuthFacade::adminUser()
                                          ->getNickname(),
            'type' => ChatRole::TYPE_NORMAL,
            'module_key' => $defaultChatModel->model_uuid,
            'is_published' => ChatRole::IS_PUBLISHED_YES,
            'enable_context' => ChatRole::ENABLE_CONTEXT_NO,
            'context_count' => 5,
            'chat_role_category_id' => 0,
            'is_hot' => ChatRole::IS_HOT_NO,
            'is_show' => ChatRole::IS_SHOW_YES,
            'icon' => $request->input('icon', ''),
            'sort' => 50,
        ]);

        return Respond::respondWithData();
    }

    public function update(Request $request, $uuid) {
        PermissionHook::can('智能体.更新');

        $chatRole = ChatRole::where('role_uuid', $uuid)
                            ->where('owner_id', '<>', 0)
                            ->first();

        if (!PermissionHook::can('智能场景.场景审核', '', false) && $chatRole->owner_id != AuthFacade::adminUser()
                                                                                                     ->getId()) {
            throw new AIException(AIException::NO_PERMISSION);
        }

        $defaultChatModel = ChatModel::where('is_default', ChatModel::IS_DEFAULT_YES)
                                     ->first();

        $this->validate($request, [
            'title' => 'required',
            'prompt' => 'required',
            'temperature' => 'nullable|numeric|between:' . ChatModel::TEMPERATURE_RANGE[$defaultChatModel->provider]['min'] . ',' . ChatModel::TEMPERATURE_RANGE[$defaultChatModel->provider]['max'],
            'penalty' => 'nullable|numeric|between:' . ChatModel::PENALTY_RANGE[$defaultChatModel->provider]['min'] . ',' . ChatModel::PENALTY_RANGE[$defaultChatModel->provider]['max'],
            'examples' => 'nullable|array',
            'description' => '',
            'icon' => 'required',
        ]);

        $chatRole->update([
            'title' => $request->input('title'),
            'system_prompt' => $request->input('prompt'),
            'temperature' => $request->input('temperature', $chatRole->temperature),
            'penalty' => $request->input('penalty', $chatRole->penalty),
            'examples' => $request->input('examples', []),
            'description' => $request->input('description', ''),
            'is_published' => ChatRole::IS_PUBLISHED_YES,
            'icon' => $request->input('icon', ''),
        ]);

        return Respond::respondWithData();
    }

    public function destroy($uuid) {
        PermissionHook::can('智能体.删除');

        $chatRole = ChatRole::where('role_uuid', $uuid)
                            ->where('owner_id', '<>', 0)
                            ->first();

        if ($chatRole->owner_id != AuthFacade::adminUser()
                                             ->getId() && !PermissionHook::can('智能场景.场景审核', false)) {
            throw new AIException(AIException::NO_PERMISSION);
        }

        $chatRole->delete();

        return Respond::respondWithData();
    }

    public function fav($uuid) {
        PermissionHook::can('智能体.场景收藏');

        $chatRole = ChatRole::where('role_uuid', $uuid)
                            ->where('owner_id', 0)
                            ->where('is_show', ChatRole::IS_SHOW_YES)
                            ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                            ->firstOrFail();

        ChatRoleUserFav::create([
            'chat_role_id' => $chatRole->id,
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
        ]);

        return Respond::respondWithData();
    }

    public function unfav($uuid) {
        PermissionHook::can('智能体.取消收藏');

        $chatRole = ChatRole::where('role_uuid', $uuid)
                            ->where('owner_id', 0)
                            ->where('is_show', ChatRole::IS_SHOW_YES)
                            ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                            ->firstOrFail();

        ChatRoleUserFav::where('chat_role_id', $chatRole->id)
                       ->where('user_id', AuthFacade::adminUser()
                                                    ->getId())
                       ->delete();

        return Respond::respondWithData();
    }

    public function favs(Request $request) {
        PermissionHook::can('智能体.场景收藏');

        $favs = ChatRole::whereHas('favUsers', function ($query) {
            $query->where('user_id', AuthFacade::adminUser()
                                               ->getId());
        })
                        ->where('is_show', ChatRole::IS_SHOW_YES)
                        ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                        ->when($request->input('title'), function ($query, $title) {
                            $query->where('title', 'like', '%' . $title . '%');
                        })
                        ->when($request->input('type'), function ($query, $type) {
                            if ($type == 'creator') {
                                $query->where('type', ChatRole::TYPE_STEP);
                            } elseif ($type == 'assistant') {
                                $query->where('type', ChatRole::TYPE_NORMAL);
                            }
                        })
                        ->orderByDesc('id')
                        ->paginate(12);

        return Respond::respondWithData(ChatRoleResource::collection($favs));
    }

    public function usedAgents() {
        PermissionHook::can('智能体.对话');

        $roles = ChatUsedRole::with('role')
                             ->where('user_id', AuthFacade::adminUser()
                                                          ->getId())
                             ->whereHas('role', function ($query) {
                                 $query->where('is_published', ChatRole::IS_PUBLISHED_YES);
                             })
                             ->orderByDesc('updated_at')
                             ->paginate(10);

        return Respond::respondWithData(ChatUsedRoleResource::collection($roles));
    }

    public function updateUsedAgents($roleUuid) {
        PermissionHook::can('智能体.对话');

        $role = ChatRole::where('role_uuid', $roleUuid)
                        ->where('owner_id', '<>', 0)
                        ->where('is_published', ChatRole::IS_PUBLISHED_YES)
                        ->firstOrFail();

        ChatUsedRole::updateOrCreate([
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'role_uuid' => $role->role_uuid,
        ], [
            'user_id' => AuthFacade::adminUser()
                                   ->getId(),
            'role_uuid' => $role->role_uuid,
            'updated_at' => Carbon::now(),
        ]);

        return Respond::respondWithData();
    }

    public function delUsedAgent($roleUuid) {
        PermissionHook::can('智能体.对话');

        ChatUsedRole::where('user_id', AuthFacade::adminUser()
                                                 ->getId())
                    ->where('role_uuid', $roleUuid)
                    ->delete();

        return Respond::respondWithData();
    }

    public function agentChatHistories(Request $request, $roleUuid) {
        PermissionHook::can('智能体.对话');

        $chatHistories = ChatHistory::where('chat_role_id', $roleUuid)
                                    ->where('user_id', AuthFacade::adminUser()
                                                                 ->getId())
                                    ->when($request->input('last_id'), function ($query, $lastId) {
                                        $query->where('id', '<', $lastId);
                                    })
                                    ->where('is_retry', ChatHistory::IS_RETRY_NO)
                                    ->orderByDesc('id')
                                    ->take(16)
                                    ->get()
                                    ->sortBy('id');

        return Respond::respondWithData(ChatHistoryResource::collection($chatHistories));
    }

    public function agentChats() {
        PermissionHook::can('智能体.对话列表');
        $chats = ChatHistory::with('chatRole')
                            ->where('user_id', AuthFacade::adminUser()
                                                         ->getId())
                            ->where('role', ChatHistory::ROLE_USER)
                            ->whereHas('chatRole', function ($query) {
                                $query->where('owner_id', '<>', 0)
                                      ->where('is_published', ChatRole::IS_PUBLISHED_YES);
                            })
                            ->groupBy('chat_uuid')
                            ->orderByDesc('id')
                            ->paginate(12);

//        $roles = ChatRole::where('owner_id', AuthFacade::adminUser()
//                                                       ->getId())
//                         ->where('is_published', ChatRole::IS_PUBLISHED_YES)
//                         ->has('chatHistories')
//                         ->paginate(15);

        return Respond::respondWithData(ChatHistoryResource::collection($chats));
    }

    public function delAgentChat($chatUuid) {
        PermissionHook::can('智能体.删除对话');

        ChatHistory::where('chat_uuid', $chatUuid)
                   ->where('user_id', AuthFacade::adminUser()
                                                ->getId())
                   ->delete();

        return Respond::respondWithData();
    }
}
