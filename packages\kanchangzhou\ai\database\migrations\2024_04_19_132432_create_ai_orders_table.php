<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('ai_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_no')
                  ->comment('订单号');
            $table->unsignedBigInteger('user_id');
            $table->decimal('total', 10, 6)
                  ->comment('总价');
            $table->unsignedTinyInteger('status')
                  ->default(1)
                  ->comment('状态 1:待支付 2:已支付 3:已取消');
            $table->string('order_type')
                  ->comment('订单类型');
            $table->string('payment_no')
                  ->nullable();
            $table->string('payment_method')
                  ->nullable()
                  ->comment('支付方式');
            $table->dateTime('paid_at')
                  ->nullable()
                  ->comment('支付时间');
            $table->dateTime('canceled_at')
                  ->nullable();
            $table->dateTime('closed_at')
                  ->nullable();
            $table->dateTime('refunded_at')
                  ->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('ai_orders');
    }
};
