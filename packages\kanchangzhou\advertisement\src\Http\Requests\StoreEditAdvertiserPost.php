<?php

namespace Kanchangzhou\Advertisement\Http\Requests;

use Kanchangzhou\Advertisement\Exceptions\AdException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;


/**
 * Class StoreEditAdvertiserPost
 * @package App\Http\Requests
 * <AUTHOR>
 * @bz 新增编辑广告商表单验证
 */
class StoreEditAdvertiserPost extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "advertiser_name"=>"required|max:50|min:5",
            "phone"=>"required|max:13|min:8",
            "email"=>"",
            "addr"=>"",
            "start_coperation"=>"required|date_format:Y-m-d H:i:s",
            "stop_coperation"=>"required|date_format:Y-m-d H:i:s",
            "bz"=>"",
            "salesman"=>""
        ];
    }

    public function messages()
    {
       return [
           "advertiser_name.required"=>"广告商名称必填",
           "advertiser_name.max"=>"广告商名称最长为50个字符",
           "advertiser_name.min"=>"广告商名称最短为5个字符",
           "phone.required"=>"联系方式必填",
           "phone.min"=>"联系方式最短为8个字符",
           "phone.max"=>"联系方式最长为13个字符",
//           "email.required"=>"广告商邮箱必填",
//           "email.email"=>"广告商邮箱格式错误",
//           "addr.required"=>"广告商地址必填",
//           "addr.max"=>"广告商地址最长为50个字符",
           "start_coperation.required"=>"开始合作时间必填",
           "start_coperation.date_format"=>"开始合作时间必须符合'年-月-日 时:分:秒'格式",
           "stop_coperation.required"=>"结束合作时间必填",
           "stop_coperation.date_format"=>"结束合作时间必须符合'年-月-日 时:分:秒'格式",
//           "bz.required"=>"备注信息必填",
//           "salesman.min"=>"业务员姓名最少为2个字",
//           "salesman.max"=>"业务员姓名最多为10个字"
       ];
    }

    public function failedValidation(Validator $validator)
    {
//        throw (new HttpResponseException(response()->json([
//            'errcode' => AdException::VALIDATE_FAIL,
//            'data' => $validator->errors()->first(),
//        ], 200)));

        throw(new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $validator->errors()));
    }
}
