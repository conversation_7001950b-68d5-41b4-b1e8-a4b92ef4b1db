<?php

namespace Kanchangzhou\AI\Services\Chat\Providers;

use AlibabaCloud\SDK\Live\V20161101\Models\GetMultiRateConfigListResponseBody\groupInfo\info;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatService;
use Kanchangzhou\AI\Services\Chat\Contacts\AIChatResponse;
use React\EventLoop\Loop;
use React\EventLoop\LoopInterface;

class AliBase extends AIChatService
{
    protected $serviceProvider = '阿里云';
    protected $uri = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
    protected $hasSystemRole = true;

    public function handler(): AIChatResponse {
        $body = [
            'model' => $this->chatModel?->model_id ?? 'qwen-max',
            'input' => [
                'messages' => $this->messageWithHistory(),
            ],
            'parameters' => [
                'result_format' => 'message',
                'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                'temperature' => $this->chatModel?->temperature ?? 0.85,
                'repetition_penalty' => $this->chatModel?->penalty ?? 1.0,
                'enable_search' => true
            ],
        ];


        $res = Http::acceptJson()
                   ->withToken(config('kai.chat.providers.aliyun.api_key'))
                   ->post($this->chatModel?->api_gateway ?? $this->uri, $body);

        if ($res->successful()) {
            return new AIChatResponse([
                'message' => $res->json('output.choices.0.message.content'),
                'chat_uuid' => $this->chatUuid,
                'total_tokens' => $res->json('usage.total_tokens', 0),
                'input_tokens' => $res->json('usage.input_tokens', 0),
                'output_tokens' => $res->json('usage.output_tokens', 0),
                'source_id' => $res->json('request_id'),
            ]);
        }

        throw new AIException(AIException::NETWORK_ERROR, [], 400, '请求失败(' . __('ai::ai.' . $res->json('message')) . ')');
    }

    public function streamHandler() {
        $body = [
            'model' => $this->chatModel?->model_id ?? 'qwen-max',
            'input' => [
                'messages' => $this->messageWithHistory(),
            ],
            'parameters' => [
                'result_format' => 'message',
                'seed' => rand(0, 0x7FFFFFFF) * rand(0, 0x7FFFFFFF),
                'temperature' => $this->chatModel?->temperature ?? 0.85,
                'repetition_penalty' => $this->chatModel?->penalty ?? 1.0,
                'enable_search' => true,
                'incremental_output' => true,
            ],
        ];

        $returnResponse = [
            'message' => '',
            'chat_uuid' => $this->chatUuid,
            'total_tokens' => 0,
            'input_tokens' => 0,
            'output_tokens' => 0,
            'source_id' => '',
        ];

        $response = Http::withHeaders([
            'X-DashScope-SSE' => 'enable',
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'application/json',
            'Accept' => 'text/event-stream',
        ])
                        ->withToken(config('kai.chat.providers.aliyun.api_key'))
                        ->withOptions([
                            'curl' => [
                                CURLOPT_WRITEFUNCTION => function ($curl, $data) use (&$returnResponse) {
                                    preg_match('/data:(.*)/', $data, $matches);
                                    $dataStr = $matches[1];
                                    $resData = json_decode($dataStr, true);

                                    echo "id:" . (microtime(true) * 10000) . PHP_EOL;
                                    if (isset($resData['code'])) {
                                        echo "event:error" . PHP_EOL;
                                        echo "data:" . json_encode([
                                                'time' => microtime(true),
                                                'message' => __('ai::ai.' . $resData['message']),
                                                'code' => $resData['code'],
                                            ]) . PHP_EOL . PHP_EOL;
                                        flush();

                                        $returnResponse['message'] = __('ai::ai.' . $resData['message']);
                                        $returnResponse['is_error'] = true;

                                        return strlen($data);
                                    }

                                    echo "event:message" . PHP_EOL;
                                    echo "data:" . json_encode([
                                            'time' => microtime(true),
                                            'message' => $resData['output']['choices'][0]['message']['content'],
                                            'finish_reason' => $resData['output']['choices'][0]['finish_reason'],
                                        ]) . PHP_EOL . PHP_EOL;
                                    flush();
                                    $returnResponse['message'] .= $resData['output']['choices'][0]['message']['content'];

                                    if ($resData['output']['choices'][0]['finish_reason'] === 'stop') {
                                        $returnResponse['total_tokens'] = $resData['usage']['total_tokens'] ?? 0;
                                        $returnResponse['input_tokens'] = $resData['usage']['input_tokens'] ?? 0;
                                        $returnResponse['output_tokens'] = $resData['usage']['output_tokens'] ?? 0;
                                        $returnResponse['source_id'] = $resData['request_id'] ?? '';
                                    }

                                    return strlen($data);
                                },
                            ],
                        ])
                        ->post($this->chatModel?->api_gateway ?? $this->uri, $body);

        return new AIChatResponse($returnResponse);
    }

    public function uploadFile(Request $request) {
        // TODO: Implement uploadFile() method.
    }
}
