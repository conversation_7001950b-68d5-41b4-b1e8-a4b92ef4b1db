<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Kanchangzhou\AI\Models\ChatRole
 *
 * @property int $id
 * @property string|null $role_uuid
 * @property string $title
 * @property string|null $system_prompt
 * @property string $type 类型
 * @property float|null $temperature 温度
 * @property float|null $penalty
 * @property array|null $options 选项
 * @property array|null $step_prompt
 * @property array|null $examples 引导示例
 * @property int|null $enable_context 是否启用上下文
 * @property int|null $context_count 上下文数量
 * @property int|null $owner_id 所有者ID
 * @property string $guard_name
 * @property string $owner_nickname
 * @property int $is_published 是否发布
 * @property string|null $chat_role_category_id 分组
 * @property int|null $is_show 是否显示
 * @property int|null $is_hot 是否热门
 * @property int|null $show_history 是否显示历史记录
 * @property string|null $module_key 使用模型
 * @property string|null $description 描述
 * @property array|null $icon 图标
 * @property int|null $sort
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\ChatRoleCategory|null $category
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\ChatHistory> $chatHistories
 * @property-read int|null $chat_histories_count
 * @property-read \Kanchangzhou\AI\Models\ChatModel|null $chatModel
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\ChatRoleUserFav> $favUsers
 * @property-read int|null $fav_users_count
 * @property-read mixed $enable_context_str
 * @property-read mixed $is_hot_str
 * @property-read mixed $is_published_str
 * @property-read mixed $is_show_str
 * @property-read mixed $show_history_str
 * @property-read mixed $type_str
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\ChatUsedRole> $usedUsers
 * @property-read int|null $used_users_count
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereChatRoleCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereContextCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereEnableContext($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereExamples($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereIsHot($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereIsPublished($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereIsShow($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereModuleKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereOwnerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereOwnerNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole wherePenalty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereRoleUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereShowHistory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereStepPrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereSystemPrompt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereTemperature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatRole withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperChatRole
 */
class ChatRole extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'options' => 'array',
        'step_prompt' => 'array',
        'icon' => 'array',
        'examples' => 'array',
    ];

    protected $appends = [
        'type_str',
        'is_show_str',
        'is_hot_str',
        'show_history_str',
        'enable_context_str',
        'is_published_str',
    ];

    const TYPE_NORMAL = 'normal';
    const TYPE_STEP = 'step';

    const TYPE_MAP = [
        self::TYPE_NORMAL => '普通',
        self::TYPE_STEP => '条件',
    ];
    const IS_SHOW_NO = 1;
    const IS_SHOW_YES = 2;

    const IS_SHOW_MAP = [
        self::IS_SHOW_NO => '否',
        self::IS_SHOW_YES => '是',
    ];

    const IS_HOT_NO = 1;
    const IS_HOT_YES = 2;

    const IS_HOT_MAP = [
        self::IS_HOT_NO => '否',
        self::IS_HOT_YES => '是',
    ];

    const SHOW_HISTORY_NO = 1;
    const SHOW_HISTORY_YES = 2;

    const SHOW_HISTORY_MAP = [
        self::SHOW_HISTORY_NO => '否',
        self::SHOW_HISTORY_YES => '是',
    ];

    const ENABLE_CONTEXT_NO = ChatModel::ENABLE_CONTEXT_NO;
    const ENABLE_CONTEXT_YES = ChatModel::ENABLE_CONTEXT_YES;

    const ENABLE_CONTEXT_MAP = ChatModel::ENABLE_CONTEXT_MAP;

    const IS_PUBLISHED_NO = 1;
    const IS_PUBLISHED_YES = 2;

    const IS_PUBLISHED_MAP = [
        self::IS_PUBLISHED_NO => '否',
        self::IS_PUBLISHED_YES => '是',
    ];

    public function getIsPublishedStrAttribute() {
        return self::IS_PUBLISHED_MAP[$this->is_published] ?? '';
    }

    public function getEnableContextStrAttribute() {
        return self::ENABLE_CONTEXT_MAP[$this->enable_context] ?? '';
    }

    public function getTypeStrAttribute() {
        return self::TYPE_MAP[$this->type] ?? '';
    }

    public function getIsShowStrAttribute() {
        return self::IS_SHOW_MAP[$this->is_show] ?? '';
    }

    public function getIsHotStrAttribute() {
        return self::IS_HOT_MAP[$this->is_hot] ?? '';
    }

    public function getShowHistoryStrAttribute() {
        return self::SHOW_HISTORY_MAP[$this->show_history] ?? '';
    }

    public function category() {
        return $this->belongsTo(ChatRoleCategory::class, 'chat_role_category_id', 'id');
    }

    public function chatModel() {
        return $this->belongsTo(ChatModel::class, 'module_key', 'model_uuid');
    }

    public function favUsers() {
        return $this->hasMany(ChatRoleUserFav::class, 'chat_role_id', 'id');
    }

    public function chatHistories() {
        return $this->hasMany(ChatHistory::class, 'chat_role_id', 'role_uuid');
    }

    public function usedUsers() {
        return $this->hasMany(ChatUsedRole::class, 'role_uuid', 'role_uuid');
    }
}
