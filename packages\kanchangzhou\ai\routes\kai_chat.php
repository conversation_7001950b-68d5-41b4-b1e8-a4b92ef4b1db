<?php

use Kanchangzhou\AI\Http\AdminControllers\{AIChatController,
    ChatHistoryController,
    AgentsController,
    AiPPTController,
    MediaAiController,
    ChatSettingController,
    TtsControler,
    AiResourcePackageController,
    AiModelPriceController,
    AiOrderController,
    SkillCompetitionController,
    VideoController,
};

Route::prefix('api/v1/admin/ai')
     ->middleware(config('kauth.auth_middleware.admin'))
     ->as('ai.admin.')
     ->group(function () {

         Route::prefix('gpt')
              ->group(function () {
                  Route::post('chat-new', [
                      AIChatController::class,
                      'index',
                  ]);

                  Route::post('chat-new-stream', [
                      AIChatController::class,
                      'streamIndex',
                  ]);

                  Route::post('chat-new/upload-files', [
                      AIChatController::class,
                      'uploadFiles',
                  ]);

                  Route::post('chat-new-audio', [
                      AIChatController::class,
                      'audioChat',
                  ]);

                  Route::get('chat-new/modules', [
                      AIChatController::class,
                      'chatModules',
                  ]);

                  Route::get('chat-new/creator-roles', [
                      AIChatController::class,
                      'creatorRoles',
                  ]);

                  Route::get('chat-new/creator-role-categories', [
                      AIChatController::class,
                      'creatorRoleCategory',
                  ]);

                  Route::get('chat-new/assistant-roles', [
                      AIChatController::class,
                      'assistantRoles',
                  ]);

                  Route::get('chat-new/roles/favs', [
                      AgentsController::class,
                      'favs',
                  ]);

                  Route::get('chat-new/roles/{uuid}', [
                      AIChatController::class,
                      'roleDetail',
                  ]);

                  Route::post('chat-new/roles/{uuid}/fav', [
                      AgentsController::class,
                      'fav',
                  ]);

                  Route::delete('chat-new/roles/{uuid}/unfav', [
                      AgentsController::class,
                      'unFav',
                  ]);

                  Route::get('chat-new/dashboard-data', [
                      AIChatController::class,
                      'statistic',
                  ]);

                  Route::get('chat-new/my-left-tokens', [
                      AIChatController::class,
                      'myLeftTokens',
                  ]);

                  Route::post('chat-new/like-this', [
                      ChatHistoryController::class,
                      'likeThis',
                  ]);

                  Route::post('chat-new/dont-like-this', [
                      ChatHistoryController::class,
                      'dontLikeThis',
                  ]);

                  Route::get('chat-new/my-created', [
                      ChatHistoryController::class,
                      'myCreated',
                  ]);

                  Route::get('chat-new/history', [
                      ChatHistoryController::class,
                      'chat',
                  ]);

                  Route::get('chat-new/history/{uuid}', [
                      ChatHistoryController::class,
                      'show',
                  ]);

                  Route::delete('chat-new/history/{uuid}', [
                      ChatHistoryController::class,
                      'destroy',
                  ]);

                  Route::get('chat-new/agent-chats', [
                      AgentsController::class,
                      'usedAgents',
                  ]);

                  Route::post('chat-new/agent-chats/{roleUuid}', [
                      AgentsController::class,
                      'updateUsedAgents',
                  ]);

                  Route::delete('chat-new/agent-chats/{roleUuid}', [
                      AgentsController::class,
                      'delUsedAgent',
                  ]);

                  Route::get('chat-new/agent-chats/{roleUuid}/history', [
                      AgentsController::class,
                      'agentChatHistories',
                  ]);
              });


//         Route::prefix('ppt')
//              ->group(function () {
//                  Route::get('themes', [
//                      AiPPTController::class,
//                      'getThemes',
//                  ]);
//
//                  Route::post('create-outline', [
//                      AiPPTController::class,
//                      'createOutline',
//                  ]);
//              });


         Route::prefix('tts')
              ->group(function () {

                  Route::post('asr', [
                      TtsControler::class,
                      'asr',
                  ]);

                  Route::post('text-to-speech', [
                      TtsControler::class,
                      'textToSpeech',
                  ]);

                  Route::post('voices', [
                      TtsControler::class,
                      'voicesList',
                  ]);

                  Route::post('history', [
                      TtsControler::class,
                      'history',
                  ]);

                  Route::post('task', [
                      TtsControler::class,
                      'task',
                  ]);

                  Route::post('result', [
                      TtsControler::class,
                      'result',
                  ]);

                  Route::post('train', [
                      TtsControler::class,
                      'train',
                  ]);

                  Route::post('train-status', [
                      TtsControler::class,
                      'trainStatus',
                  ]);
              });


         Route::prefix('media-ai')
              ->group(function () {
                  Route::post('/', [
                      MediaAiController::class,
                      'index',
                  ]);

                  Route::get('query-task', [
                      MediaAiController::class,
                      'queryTask',
                  ]);

                  Route::post('article-ai/{articleId}', [
                      MediaAiController::class,
                      'articleMediaAiJob',
                  ]);
                  Route::get('article-ai/{articleId}/data', [
                      MediaAiController::class,
                      'articleAiData',
                  ]);

                  Route::post('video-ai/{videoId}', [
                      MediaAiController::class,
                      'videoMediaAiJob',
                  ]);
                  Route::get('video-ai/{videoId}/data', [
                      MediaAiController::class,
                      'videoAiData',
                  ]);

                  Route::get('search', [
                      MediaAiController::class,
                      'search',
                  ]);

                  Route::get('form-options', [
                      MediaAiController::class,
                      'formOptions',
                  ]);
              });

         Route::prefix('settings')
              ->group(function () {
                  Route::get('/', [
                      ChatSettingController::class,
                      'index',
                  ]);

                  Route::get('form-options', [
                      ChatSettingController::class,
                      'formOptions',
                  ]);

                  Route::get('user-tokens/{userId}', [
                      ChatSettingController::class,
                      'getUserTokens',
                  ]);

                  Route::post('user-tokens/{userId}/add', [
                      ChatSettingController::class,
                      'userAddTokens',
                  ]);

                  Route::put('{key}', [
                      ChatSettingController::class,
                      'update',
                  ]);
              });

         Route::prefix('payment')
              ->group(function () {
                  Route::get('model-price/form-options', [
                      AiModelPriceController::class,
                      'formOptions',
                  ]);
                  Route::resource('model-price', AiModelPriceController::class);

                  Route::get('resource-package/form-options', [
                      AiResourcePackageController::class,
                      'formOptions',
                  ]);
                  Route::post('resource-package/get-free', [
                      AiResourcePackageController::class,
                      'getFreeResourcePackage',
                  ]);

                  Route::get('resource-package/my', [
                      AiResourcePackageController::class,
                      'myPackages',
                  ]);
                  Route::resource('resource-package', AiResourcePackageController::class);

                  Route::get('order/form-options', [
                      AiOrderController::class,
                      'formOptions',
                  ]);

                  Route::get('order/my', [
                      AiOrderController::class,
                      'myOrders',
                  ]);

                  Route::get('order/my/{orderSn}', [
                      AiOrderController::class,
                      'myOrderShow',
                  ]);

                  Route::get('order', [
                      AiOrderController::class,
                      'index',
                  ]);

                  Route::get('order/{orderSn}', [
                      AiOrderController::class,
                      'show',
                  ]);
              });

         Route::prefix('video')
             ->group(function () {
                 Route::post('task', [
                     VideoController::class,
                     'task',
                 ]);
                 Route::post('result', [
                     VideoController::class,
                     'result',
                 ]);
                 Route::post('rsync', [
                     VideoController::class,
                     'rsync',
                 ]);
                 Route::post('history', [
                     VideoController::class,
                     'history',
                 ]);
             });

     });


Route::prefix('api/v1/admin/skill-competition')
     ->middleware(config('kauth.auth_middleware.admin'))
     ->as('skill-competition.admin.')
     ->group(function () {
         Route::post('save-project', [
             SkillCompetitionController::class,
             'saveProject',
         ]);

         Route::post('finish-project', [
             SkillCompetitionController::class,
             'finishProject',
         ]);

         Route::post('score-project/{projectId}', [
             SkillCompetitionController::class,
             'scoreProject',
         ]);

         Route::get('projects', [
             SkillCompetitionController::class,
             'projects',
         ]);

         Route::get('projects/{projectId}', [
             SkillCompetitionController::class,
             'getProject',
         ]);

         Route::get('user-permissions', [
             SkillCompetitionController::class,
             'getUserPermissions',
         ]);

         Route::post('ai-score', [
             SkillCompetitionController::class,
             'aiScore',
         ]);
     });


