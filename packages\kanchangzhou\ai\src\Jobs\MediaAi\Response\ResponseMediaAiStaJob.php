<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;

class ResponseMediaAiStaJob extends MediaAiBaseJob
{
// data: {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"6e15ed2e457a40a2b565afc829f08bac","subDataTypes":[{"type":"sta","source":"索贝","version":null}],"sta":[{"fileId":"f87f8c8f-2327-4b7f-8410-94bbffe181d9","statusCode":0,"statusInfo":"success","contents":[{"angle":{"en_name":"front","zh_name":"平视","confidence":1},"motion":["stable"],"motion_confidence":[0.6992857456207275],"property":{"offset":9215200000,"result":[{"class":"other","confidence":0.9,"level":1},{"class":"non-face","confidence":1,"level":2}],"bboxes":[]},"sence":{"offset":9215200000,"shape":[1080,1920,3],"view":"close shot","ratio":-1,"head_num":0},"shot":{"offset":0,"shape":[1080,1920,3],"is_null_shot":false}}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {

    }
}
