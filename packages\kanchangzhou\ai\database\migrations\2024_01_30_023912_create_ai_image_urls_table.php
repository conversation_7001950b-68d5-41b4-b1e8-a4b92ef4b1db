<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ai_image_urls', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('history_id')->nullable()->index('history_id')->comment('历史消息ID');
            $table->string('image_orgin')->nullable()->index('image_orgin')->comment('原始图片');
            $table->string('image_local')->nullable()->comment('本地图片');
            $table->integer('error_code')->nullable()->comment('错误消息 0:正常；501:文本黄反拦截；201:模型生图失败');
            $table->string('approve_conclusion')->nullable()->comment('图片机审结果，"block"：输出图片违规；"review": 输出图片疑似违规；"pass": 输出图片未发现问题');
            $table->integer('progress')->nullable()->comment('单任务图片生成进度，进度包含2种，0为未处理完，1为处理完成');
            $table->string('status')->nullable()->comment('单风格图片状态。有 INIT（初始化），WAIT（排队中）, RUNNING（生成中）, FAILED（失败）, SUCCESS（成功）四种状态，只有 SUCCESS 为成功状态');
            $table->string('task_id')->nullable();
            $table->string('log_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_image_urls');
    }
};
