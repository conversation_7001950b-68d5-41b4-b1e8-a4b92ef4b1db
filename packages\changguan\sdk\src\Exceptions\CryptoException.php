<?php

namespace Changgua<PERSON>\SDK\Exceptions;

/**
 * 加密异常类
 * 用于处理SM2、SM3等加密相关的异常
 */
class CryptoException extends \Exception
{
    /**
     * 密钥未配置
     */
    public const ERROR_NO_KEY = 2001;

    /**
     * 无效的密钥格式
     */
    public const ERROR_INVALID_KEY = 2002;

    /**
     * 解密操作失败
     */
    public const ERROR_DECRYPT_FAILED = 2003;

    /**
     * 加密操作失败
     */
    public const ERROR_ENCRYPT_FAILED = 2004;

    /**
     * 签名操作失败
     */
    public const ERROR_SIGN_FAILED = 2005;

    /**
     * 签名验证失败
     */
    public const ERROR_VERIFY_FAILED = 2006;

    /**
     * 密钥导出失败
     */
    public const ERROR_EXPORT_FAILED = 2007;

    /**
     * 文件操作失败
     */
    public const ERROR_FILE_OPERATION = 2008;

    /**
     * 密钥生成失败
     */
    public const ERROR_KEY_GENERATION = 2009;

    /**
     * 无效的格式
     */
    public const ERROR_INVALID_FORMAT = 2010;

    /**
     * 配置无效
     */
    public const ERROR_CONFIG_INVALID = 2011;
}
