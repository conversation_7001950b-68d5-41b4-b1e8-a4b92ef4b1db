<?php

namespace Kanchangzhou\AI\Jobs\MediaAi\Response;

use Kanchangzhou\AI\Jobs\MediaAi\MediaAiBaseJob;

class ResponseMediaAiShotDetectionJob extends MediaAiBaseJob
{
//data : {"code":0,"msg":"ok","data":{"progress":100,"userdata":null,"guid":"0399fb85263e495a9dd850c36e7eb782","subDataTypes":[{"type":"shot_detection","source":"索贝","version":null}],"shot_detection":[{"fileId":"47f99a48-ad3c-497c-b443-63d2d0439163","statusCode":0,"statusInfo":"success","contents":[{"inpoint":0,"outpoint":14000000},{"inpoint":14000000,"outpoint":28000000}]}]}}
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data) {
        $this->data = $data;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {

    }
}
