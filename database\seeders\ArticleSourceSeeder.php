<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ArticleSourceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Extract unique departments
        $departments = $this->getDepartments();
        
        // Seed departments table
        $this->seedDepartments($departments);
        
        // Seed users
        $this->seedUsers();
        
        // Seed user-department relationships
        $this->seedUserDepartments();
    }
    
    /**
     * Get unique departments from the data array
     *
     * @return array
     */
    private function getDepartments()
    {
        $departmentData = $this->getData();
        $departments = [];
        
        foreach ($departmentData as $item) {
            if (!isset($departments[$item['department']])) {
                $departments[$item['department']] = [
                    'name' => $item['department'],
                    'code' => $this->generateDepartmentCode($item['department']),
                    'level' => 0,
                    'parent_id' => 0
                ];
            }
        }
        
        return $departments;
    }
    
    /**
     * Generate a simple department code from name
     *
     * @param string $name
     * @return string
     */
    private function generateDepartmentCode($name)
    {
        // Convert department name to pinyin or simple code
        // For simplicity, just using a hash function here
        $code = substr(md5($name), 0, 8);
        return $code;
    }
    
    /**
     * Seed departments table
     *
     * @param array $departments
     * @return void
     */
    private function seedDepartments($departments)
    {
        DB::table('article_source_departments')->truncate();
        
        foreach ($departments as $department) {
            DB::table('article_source_departments')->insert([
                'name' => $department['name'],
                'code' => $department['code'],
                'level' => $department['level'],
                'parent_id' => $department['parent_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
    
    /**
     * Seed users table
     *
     * @return void
     */
    private function seedUsers()
    {
        $userData = $this->getData();
        DB::table('article_source_users')->truncate();
        
        $insertedUsers = [];
        
        foreach ($userData as $item) {
            // Skip if this user has already been inserted
            // (There are some duplicate names in the data)
            if (in_array($item['name'], $insertedUsers)) {
                continue;
            }
            
            DB::table('article_source_users')->insert([
                'name' => $item['name'],
                'display_name' => $item['name'],
                'avatar' => null,
                'mobile' => null,
                'open_id' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $insertedUsers[] = $item['name'];
        }
    }
    
    /**
     * Seed user-department relationships
     *
     * @return void
     */
    private function seedUserDepartments()
    {
        $userData = $this->getData();
        DB::table('article_source_user_departments')->truncate();
        
        $processedRelations = [];
        
        foreach ($userData as $item) {
            // Get user ID and department ID
            $user = DB::table('article_source_users')
                ->where('name', $item['name'])
                ->first();
                
            $department = DB::table('article_source_departments')
                ->where('name', $item['department'])
                ->first();
                
            if ($user && $department) {
                // Create a unique key to avoid duplicate entries
                $relationKey = $user->id . '-' . $department->id;
                
                if (!in_array($relationKey, $processedRelations)) {
                    DB::table('article_source_user_departments')->insert([
                        'article_source_user_id' => $user->id,
                        'article_source_department_id' => $department->id,
                    ]);
                    
                    $processedRelations[] = $relationKey;
                }
            }
        }
    }
    
    /**
     * Get user data array
     *
     * @return array
     */
    private function getData()
    {
        return [
            [
                "department" => "综合新闻部",
                "name"       => "魏溪莹",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "崔奕",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "刘毅",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "黄洁璐",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "储冠",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "唐秋冰",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "郑雨露",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "董心悦",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "于董艳",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "张琛",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "徐杨",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "刘燕涛",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "龚励",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "张盈盈",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "姜赟",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "邵长健",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "彭晔",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "秦璐",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "张飞",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "潘洁",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "尤琳娜",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "仇松强",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "李晴",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "董心悦",
            ],
            [
                "department" => "综合新闻部",
                "name"       => "郑雨露",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "江明辉",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "舒翼",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "赵文炜",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "唐欢",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "陈征",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "吕洪涛",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "吕亦菲",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "庄奕",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "何嫄",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "汪磊",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "王新",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "吴昕",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "吴琰",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "陈凡",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "蒋寅琦",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "刘品昊",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "伍洲",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "张西杨",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "范国星",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "章云琦",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "陈楠",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "蒋丽娜",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "顾艳",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "陆文强",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "张德锋",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "黄波",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "孙波",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "毛淑雅",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "肖砚君",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "施展",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "周萌",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "朱弘熙",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "盛况",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "宗璐",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "蒋戟",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "于翔宇",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "汤嘉璐",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "吕悦",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "戴威",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "王明星",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "唐宁",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "郑雷",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "钱艳青",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "杨慧",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "芮泽炜",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "毛辰",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "朱春莉",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "戴丽萍",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "朱峰",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "李頔",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "陈云子",
            ],
            [
                "department" => "民生新闻部",
                "name"       => "陆源",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "虞岭",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "陈暐",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "胡平",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "高岷",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "尹丹蓓",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "朱臻",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "夏晨希",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "王锐",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "王栋栋",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "徐洋",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "陆士卿",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "蔡龙嘉",
            ],
            [
                "department" => "图片视觉部",
                "name"       => "董淑德",
            ],
            [
                "department" => "武进融媒部",
                "name"       => "马浩剑",
            ],
            [
                "department" => "武进融媒部",
                "name"       => "殷雯馨",
            ],
            [
                "department" => "武进融媒部",
                "name"       => "宋婧",
            ],
            [
                "department" => "武进融媒部",
                "name"       => "徐姗",
            ],
            [
                "department" => "武进融媒部",
                "name"       => "潘丽霞",
            ],
            [
                "department" => "武进融媒部",
                "name"       => "黄若云",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "周瑾亮",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "吴燕翎",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "谈必行",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "尹梦真",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "姚婕",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "吴文静",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "小袁媛",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "沈杰",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "徐潇",
            ],
            [
                "department" => "新北融媒部",
                "name"       => "周怡艳",
            ],
            [
                "department" => "天宁融媒部",
                "name"       => "凃贤平",
            ],
            [
                "department" => "天宁融媒部",
                "name"       => "于远航",
            ],
            [
                "department" => "天宁融媒部",
                "name"       => "董逸",
            ],
            [
                "department" => "天宁融媒部",
                "name"       => "汤怡晨",
            ],
            [
                "department" => "天宁融媒部",
                "name"       => "倪灵菲",
            ],
            [
                "department" => "天宁融媒部",
                "name"       => "刘洋",
            ],
            [
                "department" => "钟楼融媒部",
                "name"       => "童华岗",
            ],
            [
                "department" => "钟楼融媒部",
                "name"       => "王淑君",
            ],
            [
                "department" => "钟楼融媒部",
                "name"       => "李垚",
            ],
            [
                "department" => "钟楼融媒部",
                "name"       => "吴凌浩",
            ],
            [
                "department" => "钟楼融媒部",
                "name"       => "汝云",
            ],
            [
                "department" => "钟楼融媒部",
                "name"       => "张文勋",
            ],
            [
                "department" => "经开融媒部",
                "name"       => "徐尧",
            ],
            [
                "department" => "经开融媒部",
                "name"       => "徐蕾",
            ],
            [
                "department" => "经开融媒部",
                "name"       => "何奕欣",
            ],
            [
                "department" => "经开融媒部",
                "name"       => "孙婕",
            ],
            [
                "department" => "经开融媒部",
                "name"       => "何一智",
            ],
            [
                "department" => "经开融媒部",
                "name"       => "王硕",
            ],
            [
                "department" => "溧阳融媒部",
                "name"       => "陶新峰",
            ],
            [
                "department" => "溧阳融媒部",
                "name"       => "郏燕波",
            ],
            [
                "department" => "溧阳融媒部",
                "name"       => "吴蓝",
            ],
            [
                "department" => "溧阳融媒部",
                "name"       => "凡德华",
            ],
            [
                "department" => "金坛融媒部",
                "name"       => "朱卓晓",
            ],
            [
                "department" => "金坛融媒部",
                "name"       => "杨成武",
            ],
            [
                "department" => "金坛融媒部",
                "name"       => "陆洋吉",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "王芳",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "尤佳",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "许愿",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "毛翠娥",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "高剑英",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "刘晓燕",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "邹智媛",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "钱华",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "冯艳",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "胡迪",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "於思成",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "王莹",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "方有林",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "陶天赋",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "周玮玮",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "梅志刚",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "陶林娜",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "汪洋",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "顾琳",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "赵熹",
            ],
            [
                "department" => "教育培训工作室",
                "name"       => "石笑",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "沈芸",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "赵霅煜",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "李青",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "高志恒",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "夏敏",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "徐翊冬",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "袁华燕",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "谢思思",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "耿铭泽",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "徐慧芳",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "汪轶凡",
            ],
            [
                "department" => "健康医卫工作室",
                "name"       => "刘进",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "楼益华",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "唐洪",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "蒋丹怡",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "潘一丹",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "徐寅正",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "罗曼",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "张一杨",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "谢孟添",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "焦家钰",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "金佳琳",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "常丽杰",
            ],
            [
                "department" => "楼益华工作室",
                "name"       => "裴莉莉",
            ],
            [
                "department" => "寻味龙城工作室",
                "name"       => "秦雯",
            ],
            [
                "department" => "寻味龙城工作室",
                "name"       => "钱焱",
            ],
            [
                "department" => "寻味龙城工作室",
                "name"       => "陈诗婷",
            ],
            [
                "department" => "寻味龙城工作室",
                "name"       => "刘琳",
            ],
            [
                "department" => "寻味龙城工作室",
                "name"       => "陈娟",
            ],
            [
                "department" => "寻味龙城工作室",
                "name"       => "曹燕菲",
            ],
            [
                "department" => "文化创意工作室",
                "name"       => "钱月航",
            ],
            [
                "department" => "文化创意工作室",
                "name"       => "谢韵",
            ],
            [
                "department" => "文化创意工作室",
                "name"       => "原金典公司8人",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "潘建炜",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "刘懿",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "周静",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "蒋子云",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "徐晗杰",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "伊宏辉",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "余祥祥",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "梁汝清",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "潘振",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "宋春红",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "王鑫",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "胡杰",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "夏筱娴",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "杨恺",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "李响",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "毕心宇",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "张自强",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "吴兆帅",
            ],
            [
                "department" => "龙锦1590工作室",
                "name"       => "朱心怡",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "叶俊",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "陈新宇",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "盛栖",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "潘阳",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "周连吉",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "周枫",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "张鑫",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "张晋松",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "姚晗",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "薛菲",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "夏小勇",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "于保生",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "莫燕芬",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "陆佳怡",
            ],
            [
                "department" => "视觉设计工作室",
                "name"       => "蒋乐萍",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "吕敏",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "王磊",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "杜倩倩",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "承禹骁",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "刘思远",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "刘家豪",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "李引霈",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "陆晔",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "牟家明",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "陈华兴",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "朱崇武",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "安华梁",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "冯旭",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "金玺",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "沈毅",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "吴启明",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "夏杰",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "杨浩",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "尹路",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "袁岭",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "江斌",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "周浩",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "赵新平",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "缪凌",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "陈浩",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "华伟",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "陈泽华",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "米先华",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "何阳",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "沙立",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "高承宇",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "金中尧",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "史俊华",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "王成",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "杨博宇",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "程文皓",
            ],
            [
                "department" => "看见视频工作室",
                "name"       => "周奕",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "芮伟芬",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "舒泉清",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "韩晖",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "唐文竹",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "孙东青",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "唐新卫",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "姜小莉",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "朱雅萍",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "周洁",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "谢维娜",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "颜乃禾",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "刘瑞",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "丁宁",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "刘家",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "管晨晨",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "夏莹",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "金玮",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "余本新",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "殷宏",
            ],
            [
                "department" => "时政新闻部",
                "name"       => "张品阳",
            ],
        ];
    }
}