<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->string('fileids', 1000)
                  ->nullable()
                  ->comment('文件ID')
                  ->after('message');
        });

        Schema::table('chat_models', function (Blueprint $table) {
            $table->unsignedTinyInteger('can_files')
                  ->nullable()
                  ->default(1)
                  ->comment('是否支持文件上传')
                  ->after('model_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('fileids');
        });

        Schema::table('chat_models', function (Blueprint $table) {
            $table->dropColumn('api_gateway');
        });
    }
};
