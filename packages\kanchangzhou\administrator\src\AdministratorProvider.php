<?php

namespace Kanchangzhou\Administrator;

use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use Kanchangzhou\Administrator\Http\Middlewares\ThirdSignature;
use Kanchangzhou\Administrator\Http\Middlewares\AuthSafeMiddleware;
use Kanchangzhou\Administrator\Http\Middlewares\checkPermissions;
use Kanchangzhou\Administrator\Http\Middlewares\checkRoles;
use Kanchangzhou\Administrator\Listeners\CheckPermissionListener;
use Kanchangzhou\Administrator\Listeners\CheckRolesListener;
use Kanchangzhou\Administrator\Listeners\ModelPermissionListener;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Administrator\Observers\AdminUserObserver;
use Kanchangzhou\Kernel\Events\CheckPermissionEvent;
use Kanchangzhou\Kernel\Events\CheckRoleEvent;
use Kanchangzhou\Kernel\Events\ModelPermissionEvent;

class AdministratorProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register() {
        // 注册配置
        $this->registerUserAuthConfig();
        // 注册发布
        $this->registerPublishing();
        // 注册数据库转移
        $this->registerMigrates();
        // 注册事件监听
        $this->registerEventListener();
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot() {
        // 加载路由
        $this->bootLoadRoutes();
        // 注册新的Guard
//        $this->bootExtendAuthGuard();
        $this->bootObservers();
        // 注册路由中间件
        $this->registerRouteMiddleware();
    }

    private function registerUserAuthConfig() {
        $this->mergeConfigFrom(__DIR__ . '/../config/kadmin.php', 'kadmin');

//        config(\Arr::dot(config('kadmin.auth', []), 'auth.'));
    }

    private function registerPublishing() {
        if ($this->app->runningInConsole()) {
            // 发布配置文件
            $this->publishes([__DIR__ . '/../config' => config_path()], 'kanchangzhou-administrator-config');

            // 发布迁移文件
            $this->publishes([__DIR__ . '/../database/migrations' => database_path('migrations')], 'kanchangzhou-administrator-migrations');

        }
    }


    private function registerMigrates() {
        if ($this->app->runningInConsole()) {
            $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        }
    }

    private function registerRouteMiddleware() {
        app('router')->aliasMiddleware('kadmin.permission', CheckPermissions::class);
        app('router')->aliasMiddleware('kadmin.role', CheckRoles::class);

        app('router')->aliasMiddleware('third.signature', ThirdSignature::class);

        app('router')->pushMiddlewareToGroup('auth.kadmin', AuthSafeMiddleware::class);

    }

    /**
     * Extend Laravel's Auth.
     *
     * @return void
     */
    private function bootExtendAuthGuard() {
    }

    private function bootLoadRoutes() {
        $this->loadRoutesFrom(__DIR__ . '/../routes/kadmin.php');
    }

    private function registerEventListener() {
        $this->booting(function () {
            Event::listen(CheckPermissionEvent::class, CheckPermissionListener::class);
            Event::listen(CheckRoleEvent::class, CheckRolesListener::class);
            Event::listen(ModelPermissionEvent::class, ModelPermissionListener::class);
        });

    }

    private function bootObservers() {
        AdminUser::observe(AdminUserObserver::class);
    }
}
