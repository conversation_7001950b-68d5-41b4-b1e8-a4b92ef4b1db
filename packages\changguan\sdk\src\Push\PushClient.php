<?php

namespace <PERSON><PERSON><PERSON>\SDK\Push;

use Changguan\SDK\Http\HttpClient;
use Changguan\SDK\Navigation\RedirectToBuilder;
use Changguan\SDK\Exceptions\PushException;

class PushClient
{
    /**
     * API路径定义
     */
    private const PATH_PUSH_TO_USER = '/api/push/service/to-user';
    private const PATH_BROADCAST = '/api/push/service/broadcast';
    private const PATH_CREATE_SUBSCRIPTION = '/api/push/service/create-subscription';
    private const PATH_SUBSCRIBE_USER = '/api/push/service/subscribe-user';
    private const PATH_TRIGGER_SUBSCRIPTION = '/api/push/service/subscription';

    /**
     * @var HttpClient
     */
    private HttpClient $httpClient;

    /**
     * @var string 消息模板代码
     */
    private string $templateCode;

    /**
     * @var string 目标用户OpenID
     */
    private string $toOpenId;

    /**
     * @var array 模板参数
     */
    private array $templateParams = [];

    /**
     * @var array 扩展参数
     */
    private array $extendParams = [];

    /**
     * @var string|null 跳转链接
     */
    private ?string $redirectTo = null;

    /**
     * @var string 订阅代码
     */
    private string $subscriptionCode;

    /**
     * @var string 订阅名称
     */
    private string $subscriptionName;

    /**
     * @var string|null 订阅描述
     */
    private ?string $subscriptionDescription = null;

    /**
     * PushClient constructor.
     *
     * @param HttpClient $httpClient
     */
    public function __construct(HttpClient $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    /**
     * 设置目标用户
     *
     * @param string $openId 用户OpenID
     * @return self
     * @throws PushException 当OpenID为空时抛出
     */
    public function withUser(string $openId): self {
        if (empty($openId)) {
            throw new PushException('用户OpenID不能为空', PushException::ERROR_INVALID_PARAMS);
        }
        $this->toOpenId = $openId;
        return $this;
    }

    /**
     * 设置消息模板
     *
     * @param string $code 模板代码
     * @return self
     * @throws PushException 当模板代码为空时抛出
     */
    public function withTemplate(string $code): self {
        if (empty($code)) {
            throw new PushException('模板代码不能为空', PushException::ERROR_INVALID_PARAMS);
        }
        $this->templateCode = $code;
        return $this;
    }

    /**
     * 设置模板参数
     *
     * @param array $params 参数数组
     * @return self
     */
    public function withParams(array $params): self {
        $this->templateParams = $params;
        return $this;
    }

    /**
     * 设置扩展参数
     *
     * @param array $params 扩展参数数组
     * @return self
     */
    public function withExtend(array $params): self {
        $this->extendParams = $params;
        return $this;
    }

    /**
     * 设置跳转链接
     *
     * @param string $url 跳转URL
     * @return self
     * @throws PushException 当URL格式无效时抛出
     */
    public function withRedirect(string $url): self {
        if (!empty($url) && !filter_var($url, FILTER_VALIDATE_URL) && !str_starts_with($url, 'changguan://')) {
            throw new PushException('无效的跳转URL格式', PushException::ERROR_INVALID_PARAMS);
        }
        $this->redirectTo = $url;
        return $this;
    }

    /**
     * 设置订阅代码
     *
     * @param string $code 订阅代码
     * @return self
     * @throws PushException 当订阅代码为空时抛出
     */
    public function withSubscription(string $code): self {
        if (empty($code)) {
            throw new PushException('订阅代码不能为空', PushException::ERROR_INVALID_PARAMS);
        }
        $this->subscriptionCode = $code;
        return $this;
    }

    /**
     * 设置订阅名称
     *
     * @param string $name 订阅名称
     * @return self
     * @throws PushException 当订阅名称为空时抛出
     */
    public function withSubscriptionName(string $name): self {
        if (empty($name)) {
            throw new PushException('订阅名称不能为空', PushException::ERROR_INVALID_PARAMS);
        }
        $this->subscriptionName = $name;
        return $this;
    }

    /**
     * 设置订阅描述
     *
     * @param string|null $description 订阅描述
     * @return self
     */
    public function withSubscriptionDescription(?string $description): self {
        $this->subscriptionDescription = $description;
        return $this;
    }

    /**
     * 向单个用户推送消息
     *
     * @return array 推送结果
     * @throws PushException 当必要参数缺失或请求失败时抛出
     */
    public function pushToUser(): array {
        try {
            $this->validatePushParams();
            $params = [
                'template_code' => $this->templateCode,
                'to_open_id' => $this->toOpenId,
                'template_params' => $this->templateParams,
                'extend_params' => $this->extendParams,
                'redirect_to' => $this->redirectTo,
            ];

            return $this->httpClient->request('POST', self::PATH_PUSH_TO_USER, $params);
        } catch (\Exception $e) {
            throw new PushException('推送失败: ' . $e->getMessage(), PushException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 广播消息
     *
     * @return array 广播结果
     * @throws PushException 当必要参数缺失或请求失败时抛出
     */
    public function broadcast(): array {
        try {
            $this->validateTemplateParams();
            $params = [
                'template_code' => $this->templateCode,
                'template_params' => $this->templateParams,
                'extend_params' => $this->extendParams,
                'redirect_to' => $this->redirectTo,
            ];

            return $this->httpClient->request('POST', self::PATH_BROADCAST, $params);
        } catch (\Exception $e) {
            throw new PushException('广播失败: ' . $e->getMessage(), PushException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 创建订阅
     *
     * @return array 创建结果
     * @throws PushException 当必要参数缺失或请求失败时抛出
     */
    public function createSubscription(): array {
        try {
            $this->validateSubscriptionParams();
            $params = [
                'name' => $this->subscriptionName,
                'template_code' => $this->templateCode,
                'description' => $this->subscriptionDescription,
            ];

            return $this->httpClient->request('POST', self::PATH_CREATE_SUBSCRIPTION, $params);
        } catch (\Exception $e) {
            throw new PushException('创建订阅失败: ' . $e->getMessage(), PushException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 用户订阅
     *
     * @return array 订阅结果
     * @throws PushException 当必要参数缺失或请求失败时抛出
     */
    public function subscribeUser(): array {
        try {
            if (empty($this->subscriptionCode) || empty($this->toOpenId)) {
                throw new PushException('订阅代码和用户ID不能为空', PushException::ERROR_INVALID_PARAMS);
            }

            $params = [
                'subscription_code' => $this->subscriptionCode,
                'to_open_id' => $this->toOpenId,
            ];

            return $this->httpClient->request('POST', self::PATH_SUBSCRIBE_USER, $params);
        } catch (\Exception $e) {
            throw new PushException('用户订阅失败: ' . $e->getMessage(), PushException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 触发订阅推送
     *
     * @return array 推送结果
     * @throws PushException 当必要参数缺失或请求失败时抛出
     */
    public function triggerSubscription(): array {
        try {
            if (empty($this->subscriptionCode)) {
                throw new PushException('订阅代码不能为空', PushException::ERROR_INVALID_PARAMS);
            }

            $params = [
                'subscription_code' => $this->subscriptionCode,
                'template_params' => $this->templateParams,
                'extend_params' => $this->extendParams,
                'redirect_to' => $this->redirectTo,
            ];

            return $this->httpClient->request('POST', self::PATH_TRIGGER_SUBSCRIPTION, $params);
        } catch (\Exception $e) {
            throw new PushException('触发订阅推送失败: ' . $e->getMessage(), PushException::ERROR_REQUEST_FAILED);
        }
    }

    /**
     * 验证推送参数
     *
     * @throws PushException 当必要参数缺失时抛出
     */
    private function validatePushParams(): void {
        if (empty($this->templateCode) || empty($this->toOpenId)) {
            throw new PushException('模板代码和用户ID不能为空', PushException::ERROR_INVALID_PARAMS);
        }
    }

    /**
     * 验证模板参数
     *
     * @throws PushException 当必要参数缺失时抛出
     */
    private function validateTemplateParams(): void {
        if (empty($this->templateCode)) {
            throw new PushException('模板代码不能为空', PushException::ERROR_INVALID_PARAMS);
        }
    }

    /**
     * 验证订阅参数
     *
     * @throws PushException 当必要参数缺失时抛出
     */
    private function validateSubscriptionParams(): void {
        if (empty($this->subscriptionName) || empty($this->templateCode)) {
            throw new PushException('订阅名称和模板代码不能为空', PushException::ERROR_INVALID_PARAMS);
        }
    }

    /**
     * 获取跳转链接构建器实例
     *
     * @return RedirectToBuilder
     */
    public function redirectBuilder(): RedirectToBuilder {
        return new RedirectToBuilder();
    }
}
