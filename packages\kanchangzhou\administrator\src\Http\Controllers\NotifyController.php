<?php

namespace Kanchangzhou\Administrator\Http\Controllers;

use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Changguan\LaravelSDK\Facades\Changguan;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Kernel\Supports\Respond;


class NotifyController extends BaseController
{
    public function changguanNotify(Request $request)
    {
        // info('changguanNotify', $request->all());
        $signatureData = [
            'client_id' => Changguan::tools()->getClientId(),
            'nonce'     => Changguan::tools()->getNonce(),
            'timestamp' => Changguan::tools()->getTimestamp(),
            'signature' => Changguan::tools()->getSignature(),
        ] + $request->input();

        $signatureValidator = Changguan::project('admin')->signature();

        if (!$signatureValidator->verifySignature($signatureData)) {
            info('changguanNotify verifySignature failed', [$signatureData, $signatureValidator->getSignatureStr()]);
            return Respond::respondWithData([], 1, 401);
        }

        $event = $request->input('event');

        switch ($event) {
            case 'adminuser_info_modified':
                // info('adminuser_info_modified', $request->input());
                $this->handleUserInfoModified($request->input());
                break;
            case 'adminuser_status_changed':
                // info('adminuser_status_changed', $request->input());
                $this->handleUserStatusChanged($request->input());
                break;
            case 'adminuser_push':
                // info('adminuser_push', $request->input());
                break;
            case 'admin_department_modified':
                // info('admin_department_modified', $request->input());
                break;
        }

        return Respond::respondWithData();
    }

    private function handleUserInfoModified($data)
    {
        switch ($data['field']) {
            case 'avatar':
                AdminUser::where('uc_openid', $data['to_user'])
                    ->update(['avatar' => $data['value']]);
                break;
            case 'true_name':
                AdminUser::where('uc_openid', $data['to_user'])
                    ->update(['true_name' => $data['value']]);
                break;
            case 'display_name':
                // AdminUser::where('uc_openid', $data['to_user'])
                // ->update(['nickname' => $data['value']]);
                break;
            case 'mobile':
                AdminUser::where('uc_openid', $data['to_user'])
                    ->update(['mobile' => $data['value']]);
                break;
            case 'shot_mobile':
                // AdminUser::where('uc_openid', $data['to_user'])
                // ->update(['shot_mobile' => $data['value']]);
                break;
        }
    }

    private function handleUserStatusChanged($data)
    {
        switch ($data['status']) {
            case 'enable':
                AdminUser::where('uc_openid', $data['to_user'])
                    ->update(['status' => AdminUser::STATUS_VALID]);
                break;
            case 'disable':
                AdminUser::where('uc_openid', $data['to_user'])
                    ->update(['status' => AdminUser::STATUS_INVALID]);
                break;
        }
    }
}
