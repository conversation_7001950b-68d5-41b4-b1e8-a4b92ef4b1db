<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiEventName
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $name 事件名称
 * @property string|null $type 事件类型
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiEventName whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiEventName
 */
class MediaAiEventName extends Model
{
    use HasFactory;

    protected $guarded = [];


    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }
}
