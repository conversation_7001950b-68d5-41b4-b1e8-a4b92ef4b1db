<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiFace
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $name 姓名
 * @property string|null $tag tag
 * @property int $is_known 是否已知
 * @property string|null $person_id 人物ID
 * @property string|null $cluster_id 游标ID
 * @property string|null $kind 情绪
 * @property float|null $kind_confidence 情绪概率
 * @property string|null $angle 偏移角度
 * @property array|null $keyframe 坐标高宽
 * @property string|null $begin 起始时间
 * @property string|null $end 结束时间
 * @property string|null $offset_time 时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $is_known_name
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereAngle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereBegin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereClusterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereIsKnown($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereKeyframe($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereKind($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereKindConfidence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereOffsetTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace wherePersonId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiFace whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiFace
 */
class MediaAiFace extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'keyframe' => 'json',
    ];

    protected $appends = [
        'is_known_name',
    ];

    // 是否已知
    const IS_KNOWN_NO = 1;
    const IS_KNOWN_YES = 2;

    const IS_KNOWN_MAP = [
        self::IS_KNOWN_NO => '未知',
        self::IS_KNOWN_YES => '已知',
    ];

    public function getIsKnownNameAttribute() {
        return self::IS_KNOWN_MAP[$this->is_known] ?? '';
    }

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }
}
