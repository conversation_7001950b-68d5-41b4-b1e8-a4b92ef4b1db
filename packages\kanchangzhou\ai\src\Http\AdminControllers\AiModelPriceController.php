<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Kanchangzhou\AI\Models\AiModelPrice;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class AiModelPriceController extends BaseController
{
    public function index(Request $request) {

        $prices = AiModelPrice::when($request->input('model_type'), function ($query, $modelType) {
            $query->where('model_type', $modelType);
        })
                              ->when($request->input('provider'), function ($query, $provider) {
                                  $query->where('provider', $provider);
                              })
                              ->when($request->input('model_id'), function ($query, $modelId) {
                                  $query->where('model_id', $modelId);
                              })
                              ->when($request->input('is_show'), function ($query, $isShow) {
                                  $query->where('is_show', $isShow);
                              })
                              ->get();

        return Respond::respondWithData(JsonResource::collection($prices));
    }

    public function formOptions() {
        $providers = AiModelPrice::PROVIDER_MAP;
        $modelTypes = AiModelPrice::MODEL_TYPE_MAP;
        $isShow = AiModelPrice::IS_SHOW_MAP;

        return Respond::respondWithData(compact('providers', 'modelTypes', 'isShow'));
    }

    public function show($id) {
        $price = AiModelPrice::findOrFail($id);

        return Respond::respondWithData(JsonResource::make($price));
    }

    public function store(Request $request) {
        $this->validate($request, [
            'chat_model_id' => 'required',
            'price' => 'required',
            'unit_count' => 'required',
            'is_show' => 'required',
        ], []);

        $chatModel = ChatModel::where('id', $request->input('chat_model_id'))
                              ->firstOrFail();

        $price = AiModelPrice::create([
            'provider' => $chatModel->provider,
            'model_id' => $chatModel->model_id,
            'model_type' => $chatModel->model_type,
            'price' => $request->input('price'),
            'unit_count' => $request->input('unit_count'),
            'is_show' => $request->input('is_show'),
        ]);

        return Respond::respondWithData(JsonResource::make($price));
    }

    public function update(Request $request, $id) {
        $this->validate($request, [
            'price' => 'required',
            'unit_count' => 'required',
            'is_show' => 'required',
        ], []);

        $price = AiModelPrice::findOrFail($id);
        $price->update([
            'price' => $request->input('price'),
            'unit_count' => $request->input('unit_count'),
            'is_show' => $request->input('is_show'),
        ]);

        return Respond::respondWithData(JsonResource::make($price));
    }

    public function destroy($id) {
        $price = AiModelPrice::findOrFail($id);

        $price->delete();

        return Respond::respondWithData();
    }
}
