<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Kanchangzhou\AI\Models\ChatHistory;
use Kanchangzhou\AI\Models\ChatSetting;
use Kanchangzhou\AI\Supports\ChatLimit;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class ChatSettingController extends BaseController
{
    public function index() {
        PermissionHook::can('智能设置.列表');

        $settings = ChatSetting::all();

        return Respond::respondWithData($settings);
    }

    public function store(Request $request) {
        PermissionHook::can('智能设置.新增');

        $settings = ChatSetting::all();


        return Respond::respondWithData($settings);
    }

    public function update(Request $request, $key) {
        PermissionHook::can('智能设置.编辑');

        $this->validate($request, [
            'value' => 'required',
        ]);

        $setting = ChatSetting::where('key', $key)
                              ->firstOrFail();

        $setting->value = $request->input('value');
        $setting->value_type = is_array($request->input('value')) ? 'array' : 'string';
        $setting->save();

        Cache::tags(['chat_limit'])
             ->forget($key);

        return Respond::respondWithData();
    }

    public function formOptions() {
        $types = ChatHistory::TYPE_MAP;

        return Respond::respondWithData(compact('types'));
    }

    public function userAddTokens(Request $request, $userId) {
        PermissionHook::can('智能设置.用户Tokens管理', $userId);

        $this->validate($request, [
            'type' => 'required|in:' . implode(',', array_keys(ChatHistory::TYPE_MAP)),
            'tokens' => 'required|integer',
        ]);

        ChatLimit::setType($request->input('type'))
                 ->userIncrement(0 - $request->input('tokens'), $userId);

        return Respond::respondWithData([
            'user_token' => Cache::tags(['chat_limit'])
                                 ->remember($request->input('type') . '_daily_limit_tokens', 7200, function () use ($request) {
                                     return ChatSetting::where('key', $request->input('type') . '_daily_limit_tokens')
                                                       ->value('value') ?? 0;
                                 }) - ChatLimit::getDailyTokens($request->input('type'), '', '', $userId),
        ]);
    }

    public function getUserTokens($userId) {
        PermissionHook::can('智能设置.用户Tokens管理');

        return Respond::respondWithData([
            ChatHistory::TYPE_CHAT => Cache::tags(['chat_limit'])
                                           ->remember('chat_daily_limit_tokens', 7200, function () {
                                               return ChatSetting::where('key', 'chat_daily_limit_tokens')
                                                                 ->value('value') ?? 0;
                                           }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_CHAT, '', '', $userId),
            ChatHistory::TYPE_IMAGE => Cache::tags(['chat_limit'])
                                            ->remember('image_daily_limit_tokens', 7200, function () {
                                                return ChatSetting::where('key', 'image_daily_limit_tokens')
                                                                  ->value('value') ?? 0;
                                            }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_IMAGE, '', '', $userId),
            ChatHistory::TYPE_VIDEO => Cache::tags(['chat_limit'])
                                            ->remember('video_daily_limit_tokens', 7200, function () {
                                                return ChatSetting::where('key', 'video_daily_limit_tokens')
                                                                  ->value('value') ?? 0;
                                            }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_VIDEO, '', '', $userId),
            ChatHistory::TYPE_AUDIO => Cache::tags(['chat_limit'])
                                            ->remember('audio_daily_limit_tokens', 7200, function () {
                                                return ChatSetting::where('key', 'audio_daily_limit_tokens')
                                                                  ->value('value') ?? 0;
                                            }) - ChatLimit::getDailyTokens(ChatHistory::TYPE_AUDIO, '', '', $userId),
        ]);
    }
}
