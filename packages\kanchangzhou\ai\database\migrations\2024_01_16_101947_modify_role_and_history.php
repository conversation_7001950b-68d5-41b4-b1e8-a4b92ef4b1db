<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->tinyInteger('show_history')
                  ->nullable()
                  ->after('is_hot')
                  ->default(1)
                  ->comment('是否显示历史记录');

            $table->string('icon', 1000)
                  ->nullable()
                  ->after('description')
                  ->comment('图标');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->dropColumn('show_history');
            $table->dropColumn('icon');
        });
    }
};
