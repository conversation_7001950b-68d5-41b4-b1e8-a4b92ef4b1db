<?php

namespace Kanchangzhou\Administrator\Http\Middlewares;


use Closure;
use Kanchangzhou\Administrator\Exceptions\NotHasPermissionException;
use Kanchangzhou\Administrator\Traits\HasPermissions;
use Kanchangzhou\Kernel\Exceptions\AuthFailException;

class CheckRoles
{
    use HasPermissions;

    /**
     * @param $request
     * @param Closure $next
     * @param $role
     *
     * @return mixed
     * @throws AuthFailException
     * @throws NotHasPermissionException
     */
    public function handle($request, Closure $next, $role) {
        if (app('auth')->guest()) {
            throw new AuthFailException(AuthFailException::UNAUTHENTICATED,[],401);
        }

        $roles = is_array($role) ? $role : explode('|', $role);

        if (!$this->hasAnyRole($roles)) {
            throw new NotHasPermissionException(NotHasPermissionException::HAS_NO_ROLE);
        }

        return $next($request);
    }
}