<?php

namespace Kanchangzhou\Administrator\Http\AdminControllers;

use Kanchangzhou\Administrator\Models\AdminDepartment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class DepartmentController extends BaseController
{
    public function index(Request $request) {

        PermissionHook::can('部门管理.列表');

        $departments = AdminDepartment::when($request->input('title'), function ($query, $title) {
            $query->where('title', 'like', "%{$title}%");
        })
                                      ->orderByDesc('id')
                                      ->paginate(20);

        return Respond::respondWithData(JsonResource::collection($departments));
    }

    public function show($id) {
        PermissionHook::can('部门管理.详情');

        $department = AdminDepartment::where('id', $id)
                                     ->firstOrFail();

        return Respond::respondWithData(JsonResource::make($department));
    }

    public function store(Request $request) {
        PermissionHook::can('部门管理.新增');

        try {
            $this->validate($request, ['title' => 'required|max:30'], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        $department = AdminDepartment::create([
            'title' => $request->input('title'),
        ]);

        return Respond::respondWithData(JsonResource::make($department));
    }

    public function update(Request $request, $id) {
        PermissionHook::can('部门管理.更新');

        $department = AdminDepartment::where('id', $id)
                                     ->firstOrFail();

        try {
            $this->validate($request, ['title' => 'required|max:30'], []);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        $department->title = $request->input('title');
        $department->save();

        return Respond::respondWithData(JsonResource::make($department));
    }

    public function destroy($id) {
        PermissionHook::can('部门管理.删除');

        $department = AdminDepartment::where('id', $id)
                                     ->firstOrFail();

        $department->delete();

        return Respond::respondWithData();
    }
}
