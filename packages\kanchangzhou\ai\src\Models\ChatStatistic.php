<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\ChatStatistic
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ChatStatistic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatStatistic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ChatStatistic query()
 * @mixin \Eloquent
 * @mixin IdeHelperChatStatistic
 */
class ChatStatistic extends Model
{
    use HasFactory;
}
