<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRoutePermissionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('admin_routes', function (Blueprint $table) {
            $table->id('id');
            $table->string('title');
            $table->string('route_str')
                  ->nullable();
            $table->integer('parent_id')
                  ->nullable()
                  ->default(0);
            $table->integer('sort')
                  ->nullable()
                  ->default(0);
            $table->timestamps();
        });

//        Schema::create('admin_route_role', function (Blueprint $table) {
//            $table->unsignedBigInteger('id');
//            $table->integer('admin_route_id');
//            $table->integer('role_id');
//        });
//
//        Schema::create('admin_route_permission', function (Blueprint $table) {
//            $table->unsignedBigInteger('id');
//            $table->integer('admin_route_id');
//            $table->integer('permission_id');
//        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('admin_routes');
//        Schema::dropIfExists('admin_route_role');
//        Schema::dropIfExists('admin_route_permission');
    }
}
