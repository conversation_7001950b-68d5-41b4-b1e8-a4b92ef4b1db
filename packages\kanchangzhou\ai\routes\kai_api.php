<?php

use Kanchangzhou\AI\Http\Controllers\ChatController;
use Kanchangzhou\AI\Http\Controllers\NoticeCallBackController;

Route::prefix('api/v1/ai')
     ->middleware(config('kauth.auth_middleware.user'))
     ->as('ai.')
     ->group(function () {
         Route::post('chat/stream', [
             ChatController::class,
             'streamChat',
         ])
              ->name('chat.stream');

         Route::post('audio-chat', [
             ChatController::class,
             'audioChat',
         ])
              ->name('chat.audio_chat');

     });


Route::prefix('api/v1/ai')
     ->middleware(config('kauth.auth_middleware.no_auth'))
     ->as('ai.')
     ->group(function () {
         Route::post('media-ai/notice', [
             NoticeCallBackController::class,
             'mediaAiCallback',
         ])
              ->name('media_ai.notice');

         Route::post('image/mj-callback', [
             NoticeCallBackController::class,
             'mjCallback',
         ])
              ->name('image.mj-callback');

         Route::post('music/duomi-callback/{id}', [
             NoticeCallBackController::class,
             'DuomiCallback',
         ])
              ->name('music.duomi-callback');

         Route::post('video/pixverse-callback', [
             NoticeCallBackController::class,
             'pixverseCallback',
         ])
              ->name('video.pixverse-callback');

         Route::post('audio/volcengine-callback', [
             NoticeCallBackController::class,
             'volcengineCallback',
         ])
              ->name('audio.volcengine-callback');

         Route::any('oss/callback', [
             NoticeCallBackController::class,
             'ossUploadCallback',
         ])->name('oss.upload.callback');

         Route::post('moderation/callback', [
             NoticeCallBackController::class,
             'moderationCallback',
         ])
              ->name('moderation.callback');
     });


Route::prefix('api/v1/ai')
     ->middleware([
         'api',
         'ai.api',
     ])
     ->as('ai.api.')
     ->group(function () {
         Route::post('chat', [
             ChatController::class,
             'chat',
         ]);
     });
