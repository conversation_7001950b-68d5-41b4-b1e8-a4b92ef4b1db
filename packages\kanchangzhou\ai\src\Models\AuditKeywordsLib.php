<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\AuditKeywordsLib
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\AI\Models\AuditKeyword> $keywords
 * @property-read int|null $keywords_count
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeywordsLib newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeywordsLib newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditKeywordsLib query()
 * @mixin \Eloquent
 * @mixin IdeHelperAuditKeywordsLib
 */
class AuditKeywordsLib extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function keywords() {
        return $this->hasMany(AuditKeyword::class, 'lib_id', 'lib_id');
    }
}
