<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('admin_routes', function (Blueprint $table) {
            $table->string('platform')
                  ->nullable()
                  ->default('admin')
                  ->comment('平台');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('admin_routes', function (Blueprint $table) {
            $table->dropColumn('platform');
        });
    }
};
