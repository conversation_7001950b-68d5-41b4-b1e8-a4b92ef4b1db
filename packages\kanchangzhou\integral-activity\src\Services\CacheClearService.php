<?php

namespace Kanchangzhou\IntegralActivity\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Kanchangzhou\IntegralActivity\Models\IntegralLottery;

class CacheClearService
{
    /**
     * 安全使用缓存标签
     * 
     * @param array|string $tags
     * @return \Illuminate\Contracts\Cache\Repository
     */
    private static function safeTag($tags)
    {
        try {
            return Cache::tags($tags);
        } catch (\Exception $e) {
            // 如果不支持缓存标签，记录日志
            Log::warning('Cache driver does not support tags', [
                'tags' => $tags,
                'error' => $e->getMessage()
            ]);
            return Cache::driver();
        }
    }
    
    /**
     * 清除产品相关的所有缓存
     *
     * @param int $productId
     * @return void
     */
    public static function clearProductCache($productId)
    {
        try {
            // 清除产品详情缓存
            Cache::forget('exchange_product_detail_' . $productId);
            
            // 尝试使用缓存标签清除相关缓存
            try {
                Cache::tags(['exchange', 'product_' . $productId])->flush();
            } catch (\Exception $e) {
                // 标签不支持时忽略错误
            }
            
            // 清除产品列表缓存（所有页）
            self::clearPaginatedCache('exchange_products_page_');
        } catch (\Exception $e) {
            Log::warning('Failed to clear product cache', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清除抽奖活动相关的所有缓存
     *
     * @param int|string $lotteryId
     * @return void
     */
    public static function clearLotteryCache($lotteryId)
    {
        try {
            // 清除抽奖活动详情缓存
            Cache::forget('lottery_detail_' . $lotteryId);
            
            // 尝试使用缓存标签清除相关缓存
            try {
                Cache::tags(['lottery', 'lottery_' . $lotteryId])->flush();
            } catch (\Exception $e) {
                // 标签不支持时忽略错误
            }
            
            // 清除抽奖奖品列表缓存
            self::clearLotteryItemsCache($lotteryId);
            
            // 清除中奖名单相关缓存（所有页）
            self::clearPaginatedCache('lottery_lucky_list_' . $lotteryId . '_page_');
            
            // 清除抽奖活动列表缓存（所有页）
            self::clearPaginatedCache('lottery_list_page_');
        } catch (\Exception $e) {
            Log::warning('Failed to clear lottery cache', [
                'lottery_id' => $lotteryId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清除抽奖活动奖品列表缓存
     *
     * @param int|string $lotteryId 抽奖活动ID
     * @return void
     */
    public static function clearLotteryItemsCache($lotteryId)
    {
        // 清除抽奖奖品列表缓存
        try {
            // 尝试使用标签清除
            try {
                Cache::tags(['lottery'])->forget("lottery_{$lotteryId}_items");
            } catch (\Exception $e) {
                // 标签不支持时忽略错误
            }
            
            // 直接清除具体的缓存键
            Cache::forget("lottery_{$lotteryId}_items");
        } catch (\Exception $e) {
            Log::warning('Failed to clear lottery items cache', [
                'lottery_id' => $lotteryId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清除用户相关的积分活动缓存
     * 注意：此方法只清除积分活动相关的用户缓存，不会影响用户登录状态等其他系统缓存
     *
     * @param int $userId
     * @return void
     */
    public static function clearUserCache($userId)
    {
        try {
            // 只清除积分活动相关的用户缓存标签，避免影响登录状态
            try {
                Cache::tags(['integral_user_' . $userId])->flush();
                Cache::tags(['exchange_user_' . $userId])->flush();
                Cache::tags(['lottery_user_' . $userId])->flush();
            } catch (\Exception $e) {
                // 标签不支持时忽略错误
                Log::debug('Cache tags not supported for user cache clearing', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
            }

            // 清除用户积分日志缓存（所有页）
            self::clearPaginatedCache('user_' . $userId . '_integral_logs_page_');

            // 清除用户奖品列表缓存（所有页）
            self::clearPaginatedCache('user_' . $userId . '_awards_page_');

            // 清除用户订单列表缓存（所有状态，所有页）
            $statuses = ['all', 'pending', 'completed', 'cancelled']; // 根据实际状态值调整
            foreach ($statuses as $status) {
                self::clearPaginatedCache('user_' . $userId . '_orders_status_' . $status . '_page_');
            }

            // 清除用户抽奖统计数据的缓存键
            $keys = Cache::get('user_' . $userId . '_lottery_cache_keys', []);
            foreach ($keys as $key) {
                Cache::forget($key);
            }

            // 清除用户在积分活动中的具体缓存项
            $integralUserCacheKeys = [
                'user_' . $userId . '_integral_balance',
                'user_' . $userId . '_exchange_count',
                'user_' . $userId . '_lottery_count',
                'user_' . $userId . '_today_lottery_count',
                'user_' . $userId . '_month_lottery_count'
            ];

            foreach ($integralUserCacheKeys as $key) {
                Cache::forget($key);
            }

            Log::debug('User integral activity cache cleared', ['user_id' => $userId]);
        } catch (\Exception $e) {
            Log::warning('Failed to clear user integral activity cache', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }
    

    /**
     * 清除指定前缀的分页缓存
     *
     * @param string $prefix 缓存键前缀
     * @return void
     */
    public static function clearPaginatedCache($prefix)
    {
        // 使用安全的方式清除分页缓存，避免误删其他缓存
        // 清除前50页的缓存（增加范围以确保覆盖更多可能的分页）
        try {
            for ($i = 1; $i <= 50; $i++) {
                Cache::forget($prefix . $i);
            }

            // 同时清除一些常见的分页参数组合
            $commonParams = ['', '_10', '_20', '_50'];
            foreach ($commonParams as $param) {
                for ($i = 1; $i <= 20; $i++) {
                    Cache::forget($prefix . $i . $param);
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to clear paginated cache', [
                'prefix' => $prefix,
                'error' => $e->getMessage()
            ]);
        }
    }

    
    /**
     * 清除积分活动相关的所有缓存
     * 注意：此方法只清除积分活动相关的缓存，不会影响用户登录状态等其他系统缓存
     *
     * @return void
     */
    public static function clearAllCache()
    {
        try {
            // 尝试使用缓存标签清除（如果支持）
            try {
                Cache::tags(['exchange', 'lottery', 'integral'])->flush();
            } catch (\Exception $e) {
                // 标签不支持时忽略
                Log::info('Cache tags not supported, using manual cache clearing', [
                    'error' => $e->getMessage()
                ]);
            }

            // 清除积分活动相关的分页缓存
            self::clearPaginatedCache('exchange_products_page_');
            self::clearPaginatedCache('lottery_list_page_');

            // 清除其他积分活动相关的缓存前缀
            $integralCachePrefixes = [
                'lottery_detail_',
                'exchange_product_detail_',
                'lottery_lucky_list_',
                'integral_logs_page_',
                'awards_page_',
                'orders_status_'
            ];

            foreach ($integralCachePrefixes as $prefix) {
                self::clearPaginatedCache($prefix);
            }

            Log::info('Integral activity caches cleared successfully');
        } catch (\Exception $e) {
            Log::error('Failed to clear integral activity caches', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 注册缓存键到用户缓存键列表，方便统一清理
     * 
     * @param int $userId
     * @param string $cacheKey
     * @return void
     */
    public static function registerUserCacheKey($userId, $cacheKey)
    {
        try {
            $keys = Cache::get('user_' . $userId . '_lottery_cache_keys', []);
            $keys[] = $cacheKey;
            $keys = array_unique($keys);
            Cache::put('user_' . $userId . '_lottery_cache_keys', $keys, now()->addDays(1));
        } catch (\Exception $e) {
            // 记录但不阻止程序执行
            Log::warning('Failed to register user cache key', [
                'user_id' => $userId,
                'key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
        }
    }
} 