<?php

namespace Kanchangzhou\IntegralActivity\Services;

use Illuminate\Support\Facades\Cache;

class CacheClearService
{
    /**
     * 安全清除缓存标签
     *
     * @param array|string $tags
     * @return void
     */
    private static function safeFlushTags($tags)
    {
        try {
            Cache::tags($tags)->flush();
        } catch (\Exception) {
            // 缓存驱动不支持标签时忽略
        }
    }
    
    /**
     * 清除产品相关的所有缓存
     *
     * @param int $productId
     * @return void
     */
    public static function clearProductCache($productId)
    {
        // 清除产品详情缓存
        Cache::forget('exchange_product_detail_' . $productId);

        // 尝试使用缓存标签清除相关缓存
        self::safeFlushTags(['exchange', 'product_' . $productId]);

        // 清除产品列表缓存（所有页）
        self::clearPaginatedCache('exchange_products_page_');
    }

    /**
     * 清除抽奖活动相关的所有缓存
     *
     * @param int|string $lotteryId
     * @return void
     */
    public static function clearLotteryCache($lotteryId)
    {
        // 清除抽奖活动详情缓存
        Cache::forget('lottery_detail_' . $lotteryId);

        // 尝试使用缓存标签清除相关缓存
        self::safeFlushTags(['lottery', 'lottery_' . $lotteryId]);

        // 清除抽奖奖品列表缓存
        self::clearLotteryItemsCache($lotteryId);

        // 清除中奖名单相关缓存（所有页）
        self::clearPaginatedCache('lottery_lucky_list_' . $lotteryId . '_page_');

        // 清除抽奖活动列表缓存（所有页）
        self::clearPaginatedCache('lottery_list_page_');
    }
    
    /**
     * 清除抽奖活动奖品列表缓存
     *
     * @param int|string $lotteryId 抽奖活动ID
     * @return void
     */
    public static function clearLotteryItemsCache($lotteryId)
    {
        // 尝试使用标签清除
        try {
            Cache::tags(['lottery'])->forget("lottery_{$lotteryId}_items");
        } catch (\Exception) {
            // 标签不支持时忽略错误
        }

        // 直接清除具体的缓存键
        Cache::forget("lottery_{$lotteryId}_items");
    }
    
    /**
     * 清除用户相关的积分活动缓存
     * 注意：此方法只清除积分活动相关的用户缓存，不会影响用户登录状态等其他系统缓存
     *
     * @param int $userId
     * @return void
     */
    public static function clearUserCache($userId)
    {
        // 只清除积分活动相关的用户缓存标签，避免影响登录状态
        self::safeFlushTags(['integral_user_' . $userId]);
        self::safeFlushTags(['exchange_user_' . $userId]);
        self::safeFlushTags(['lottery_user_' . $userId]);

        // 清除用户积分日志缓存（所有页）
        self::clearPaginatedCache('user_' . $userId . '_integral_logs_page_');

        // 清除用户奖品列表缓存（所有页）
        self::clearPaginatedCache('user_' . $userId . '_awards_page_');

        // 清除用户订单列表缓存（所有状态，所有页）
        $statuses = ['all', 'pending', 'completed', 'cancelled'];
        foreach ($statuses as $status) {
            self::clearPaginatedCache('user_' . $userId . '_orders_status_' . $status . '_page_');
        }

        // 清除用户抽奖统计数据的缓存键
        $keys = Cache::get('user_' . $userId . '_lottery_cache_keys', []);
        foreach ($keys as $key) {
            Cache::forget($key);
        }

        // 清除用户在积分活动中的具体缓存项
        $integralUserCacheKeys = [
            'user_' . $userId . '_integral_balance',
            'user_' . $userId . '_exchange_count',
            'user_' . $userId . '_lottery_count',
            'user_' . $userId . '_today_lottery_count',
            'user_' . $userId . '_month_lottery_count'
        ];

        foreach ($integralUserCacheKeys as $key) {
            Cache::forget($key);
        }
    }
    

    /**
     * 清除指定前缀的分页缓存
     *
     * @param string $prefix 缓存键前缀
     * @return void
     */
    public static function clearPaginatedCache($prefix)
    {
        // 清除前50页的缓存
        for ($i = 1; $i <= 50; $i++) {
            Cache::forget($prefix . $i);
        }

        // 清除常见的分页参数组合
        $commonParams = ['', '_10', '_20', '_50'];
        foreach ($commonParams as $param) {
            for ($i = 1; $i <= 20; $i++) {
                Cache::forget($prefix . $i . $param);
            }
        }
    }

    
    /**
     * 清除积分活动相关的所有缓存
     * 注意：此方法只清除积分活动相关的缓存，不会影响用户登录状态等其他系统缓存
     *
     * @return void
     */
    public static function clearAllCache()
    {
        // 尝试使用缓存标签清除（如果支持）
        self::safeFlushTags(['exchange', 'lottery', 'integral']);

        // 清除积分活动相关的分页缓存
        self::clearPaginatedCache('exchange_products_page_');
        self::clearPaginatedCache('lottery_list_page_');

        // 清除其他积分活动相关的缓存前缀
        $integralCachePrefixes = [
            'lottery_detail_',
            'exchange_product_detail_',
            'lottery_lucky_list_',
            'integral_logs_page_',
            'awards_page_',
            'orders_status_'
        ];

        foreach ($integralCachePrefixes as $prefix) {
            self::clearPaginatedCache($prefix);
        }
    }
}