<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Http\Resources\Json\JsonResource;
use Kanchangzhou\AI\Exceptions\AIMusicException;
use Kanchangzhou\AI\Models\AiMusicHistory;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Hooks\SensitiveWordsHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Auth\Facades\AuthFacade;


class AIMusicController extends BaseController
{
    public function generate(Request $request) {
        //PermissionHook::can('智能多模态.生成音乐');
        $this->validate($request, [
            'module' => ['required', Rule::in(['duomi'])],
            'prompt' => Rule::requiredIf(function () use ($request) {
                return !($request->custom_mode == 1 && $request->make_instrumental == 1);
             }),
        ]);

        if (!SensitiveWordsHook::isLegal($request->input('prompt'), SensitiveWordsHook::SCENES_ALL, false)) {
            throw new AIMusicException(AIMusicException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'duomi'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Music\Providers\\' . $serviceProviderName)) {
            throw new AIMusicException(AIMusicException::MISS_INTERFACE);
        }
        // 增加多用户体系, 增加guard_name字段
        $aiUser =  new AiUser(AuthFacade::adminUser()->getId(), 'kadmin');
        $task_code = uniqid() . $aiUser->getUserId();

        $music = AiMusicHistory::create([
                'user_id' => $aiUser->getUserId(),
                'task_code' => $task_code,
                'module' => Str::studly(class_basename(get_class($this))),
                'provider' => $serviceProviderName,
                'action' => $action ?? 'generate',
                'prompt' => $request->input('prompt'),
                'state' => 0, // 0未开始 1已生成 2生成失败 3生成中
        ]);
        $data = $request;
        $data['task_code'] = $task_code;
        $service = new $serviceProvider($aiUser, $data);
        $res = $service->handler(__FUNCTION__);
        $task_id = $res['data'][0]['task_id'] ?? '';
        if (!empty($task_id)) {
            AiMusicHistory::where('id', $music->id)
                ->update([
                    'task_id' => $task_id ?? ''
                ]);
        }
        $music = AiMusicHistory::find($music->id);
        return Respond::respondWithData($music);
    }

    /**
     * 生成歌词
     */
    public function generateLyrics(Request $request){
        $this->validate($request, [
            'module' => ['required', Rule::in(['duomi']),
            ],
            'prompt' => 'required|string',
        ]);
        if (!SensitiveWordsHook::isLegal($request->input('prompt'), SensitiveWordsHook::SCENES_ALL, false)) {
            throw new AIMusicException(AIMusicException::HAS_SENSITIVE);
        }

        $serviceProviderName = Str::studly($request->input('module', 'duomi'));
        if (!class_exists($serviceProvider = 'Kanchangzhou\AI\Services\Music\Providers\\' . $serviceProviderName)) {
            throw new AIMusicException(AIMusicException::MISS_INTERFACE);
        }
        $aiUser = new AiUser(AuthFacade::adminUser()->getId(), 'kadmin');
        $service = new $serviceProvider($aiUser, $request);
        $res = $service->handler(__FUNCTION__);
        return Respond::respondWithData($res);

    }


    public function getMusicHistory(Request $request) {
        //PermissionHook::can('智能多模态.生成音乐');
        $date = $request->input('date', []);
        $user_id = AuthFacade::adminUser()->getId();
        $keyword = $request->input('keyword', '', 'trim');

        $where = [];
        if ($user_id) {
            $where[] = ['user_id', '=', $user_id];
        }
        if ($keyword) {
            $where[] = ['prompt', 'like', '%' . $keyword . '%'];
        }
        if (!empty($date) && ($date[0] || $date[1])) {
            $start_time = $date[0];
            $end_time = $date[1];
            $where[] = ['create_at', 'between', [$start_time, $end_time]];
        }

        $list = AiMusicHistory::where($where)
            ->orderByDesc('id')
            ->paginate();

        foreach($list as  $item){
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_at']);
                if (!empty($item['options'])) {
                    $item['options'] = @json_decode($item['options'], true);
                }

                if (!empty($item['result1'])) {
                    $item['result1'] = @json_decode($item['result1'], true);
                }
                if (!empty($item['result2'])) {
                    $item['result2'] = @json_decode($item['result2'], true);
                }
        }

        return Respond::respondWithData(JsonResource::collection($list));
    }

    public function getMusicResult(Request $request) {
        //PermissionHook::can('智能多模态.生成音乐');
        $this->validate($request, [
            'music_id' => 'required|integer',
        ]);

        $id = $request->input('music_id');
        $music = AiMusicHistory::find($id);
        if (!$music) {
            throw new AIMusicException(AIMusicException::NOT_FOUND);
        }
//        if ($music['state'] == 0) {
//            $now = time();
//            if ($now - strtotime($music['create_at']) > 600) {
//                $music['errMsg'] = '音乐生成失败';
//            }
//        }

        if (!empty($music['result1'])) {
            $music['result1'] = @json_decode($music['result1'], true);
        }
        if (!empty($music['result2'])) {
            $music['result2'] = @json_decode($music['result2'], true);
        }
        return Respond::respondWithData($music);
    }






}
