<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Kanchangzhou\User\Models\User;

class MakeRandomUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kcz:random {month} {number}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $month = $this->argument('month');
        $number = $this->argument('number');

        $userGrowData = User::selectRaw('count(*) as num')
                            ->selectRaw('DATE_FORMAT(created_at,"%Y-%m-%d") as date')
                            ->whereBetween('created_at', [
                                Carbon::create($month)
                                      ->firstOfMonth(),
                                Carbon::create($month)
                                      ->endOfMonth(),
                            ])
                            ->groupByRaw('date')
                            ->get();

        $sorted = $userGrowData->sortBy('num');

        $days = Carbon::create($month)->daysInMonth;
        $sum = $number + rand(0, 500);
        $sum = $sum - $days;

        $data = [];

        for ($i = $days; $i > 0; $i--) {
            $data[$i] = 1;
            $row = 0;
            if ($sum > 0) {
                if ($i == 1) {
                    $data[$i] += $sum;
                } else {
                    $max = floor($sum / $i * 2);
                    $row = mt_rand(0, $max);
                    $data[$i] += $row;
                }
            }
            $sum -= $row;
        }

        sort($data);

        $j = -1;
        $s = $sorted->map(function ($item) use ($data, &$j) {
            $j++;

            return [
                'num' => $data[$j],
                'date' => $item->date,
            ];
        })
                    ->pluck('num', 'date');

        \Cache::put('random_user' . $month, $s);

        dd($s);
    }
}
