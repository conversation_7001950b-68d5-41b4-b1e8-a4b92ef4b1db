<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->string('module_key')
                  ->after('is_hot')
                  ->nullable()
                  ->comment('使用模型');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->dropColumn('module_key');
        });
    }
};
