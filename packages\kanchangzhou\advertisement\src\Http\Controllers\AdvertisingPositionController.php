<?php

namespace Kanchangzhou\Advertisement\Http\Controllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingException;
use Kanchangzhou\Advertisement\Exceptions\AdvertisingPositionException;
use Kanchangzhou\Advertisement\Http\Resources\AdvertisingPositionResource;
use Kanchangzhou\Advertisement\Models\AdvertisingPosition;
use Kanchangzhou\Advertisement\Exceptions\AdException;
use Kanchangzhou\Advertisement\Http\Requests\StoreEditAdvertisingPositionPost;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Kanchangzhou\Kernel\Supports\Response;

class AdvertisingPositionController extends Controller
{

    /**
     * @param Request $request
     */
    public function adPosition(Request $request) {
        return Respond::respondWithData(AdvertisingPositionResource::collection(AdvertisingPosition::where("position_type_id", 1)
                                                                                                   ->get()), AdvertisingPositionException::OPERATION_SUCCESS);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function allPosWithAdv() {
        $nowData = date("Y-m-d H:i:s");

        $config = config("kadvertisement.advertising-type");

        $res = [];
        $listRes = AdvertisingPosition::whereIn('type', [0,1])
                                      ->with("advertisingInfo")
                                      ->get();//获取到广告位以及广告信息
        if (!empty($listRes)) {
            foreach ($listRes as $key => $everyPos) {
                $tmpArr = [];
                $everyPositionInfo = $config[$everyPos->position_type_id];//拿到广告位对应的广告位类型数据

                $tmpArr["id"] = $everyPos->id;
                $tmpArr["position_name"] = $everyPos->position_name;
                $tmpArr["key"] = $everyPos->key;
                $tmpArr["ad_tag"] = $everyPos->ad_tag;
                $tmpArr["advInfo"] = [];

                if (count($everyPos->advertisingInfo) > 0) {
                    if (isset($everyPositionInfo["image_num"])) {
                        $everyPosNum = $everyPositionInfo["image_num"];//拿到该广告位需要几张广告
                        if ($everyPosNum >= count($everyPos->advertisingInfo)) {//如果广告位类型需要3张 但是广告位下只有2张或者3张，直接给出
                            if (!empty($everyPos->advertisingInfo)) {
                                foreach ($everyPos->advertisingInfo as $eveAdv) {
                                    $tmpAdv = [];
                                    $tmpAdv["id"] = $eveAdv->id;
                                    if ($eveAdv->is_show_title == 1) {
                                        $tmpAdv["title"] = $eveAdv->title;
                                    } else {
                                        $tmpAdv["title"] = "";
                                    }

                                    $tmpAdv["thumb_img"] = $eveAdv->thumb_img;
                                    $tmpAdv["display_times"] = $eveAdv->display_times;
                                    $tmpAdv["redirectable"] = $eveAdv->redirectable;
                                    $tmpAdv["serve_time"] = $eveAdv->serve_time;
                                    $tmpAdv["type"] = $everyPositionInfo["type"];
                                    $tmpAdv["image_num"] = $everyPositionInfo["image_num"];
                                    $tmpAdv["attr"] = $everyPositionInfo["attr"];
                                    $tmpAdv["media_type"] = $eveAdv->media_type;
                                    array_push($tmpArr["advInfo"], $tmpAdv);
                                }
                            }
                        } else {//如果需要3张 但是却有5张，那就要根据广告位类型去随机还是权重
                            $adIdArray = [];
                            while (true) {
                                if ($everyPos->type == 0) {//如果是权重方式
                                    $tmpAdIdArray = [];
                                    if (!empty($everyPos->advertisingInfo)) {
                                        foreach ($everyPos->advertisingInfo as $everytmpAd) {
                                            for ($i = 0; $i < $everytmpAd->power; $i++) {
                                                array_push($tmpAdIdArray, $everytmpAd->id);
                                            }
                                        }
                                    }
                                    if (empty($tmpAdIdArray)) {
                                        throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
                                    }
                                    $idKey = array_rand($tmpAdIdArray);
                                    $adId = $tmpAdIdArray[$idKey];
                                    if (!in_array($adId, $adIdArray)) {
                                        array_push($adIdArray, $adId);
                                    }
                                }

                                if ($everyPos->type == 1) {//如果是随机方式
                                    $tmpAdIdArray = [];
                                    if (!empty($everyPos->advertisingInfo)) {
                                        foreach ($everyPos->advertisingInfo as $everytmpAd) {
                                            array_push($tmpAdIdArray, $everytmpAd->id);
                                        }
                                    }
                                    if (empty($tmpAdIdArray)) {
                                        throw new AdvertisingPositionException(AdvertisingPositionException::NOT_EXISTS);
                                    }
                                    $idKey = array_rand($tmpAdIdArray);
                                    $adId = $tmpAdIdArray[$idKey];
                                    if (!in_array($adId, $adIdArray)) {
                                        array_push($adIdArray, $adId);
                                    }
                                }

                                if (count($adIdArray) >= $everyPosNum) {
                                    break;
                                }
                            }


                            if (!empty($everyPos->advertisingInfo)) {
                                foreach ($everyPos->advertisingInfo as $value) {
                                    if (in_array($value->id, $adIdArray)) {
                                        $tmpAdv = [];
                                        $tmpAdv["id"] = $value->id;
                                        if ($value->is_show_title == 1) {
                                            $tmpAdv["title"] = $value->title;
                                        } else {
                                            $tmpAdv["title"] = "";
                                        }

                                        $tmpAdv["thumb_img"] = $value->thumb_img;
                                        $tmpAdv["display_times"] = $value->display_times;
                                        $tmpAdv["redirectable"] = $value->redirectable;
                                        $tmpAdv["serve_time"] = $value->serve_time;
                                        $tmpAdv["type"] = $everyPositionInfo["type"];
                                        $tmpAdv["image_num"] = $everyPositionInfo["image_num"];
                                        $tmpAdv["attr"] = $everyPositionInfo["attr"];
                                        $tmpAdv["media_type"] = $value->media_type;
                                        array_push($tmpArr["advInfo"], $tmpAdv);
                                    }
                                }
                            }
                        }
                    }
                }


                array_push($res, $tmpArr);
            }
        }

        return Respond::respondWithData($res, AdvertisingPositionException::OPERATION_SUCCESS);
    }
}
