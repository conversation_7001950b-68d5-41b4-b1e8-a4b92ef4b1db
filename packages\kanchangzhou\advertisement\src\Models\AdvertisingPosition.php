<?php

namespace Kanchangzhou\Advertisement\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kanchangzhou\Kernel\Classes\HomeAdConfig;
use Kanchangzhou\Kernel\Classes\HomeItemThumbnails;
use Kanchangzhou\Kernel\Classes\WidgetItems;
use Kanchangzhou\Kernel\Contracts\HomeItemSubject;
use Kanchangzhou\Kernel\Contracts\HomeItemTemplate;
use Kanchangzhou\Kernel\Contracts\RedirectableInterface;
use Kanchangzhou\Kernel\Supports\Redirectable;

/**
 * Kanchangzhou\Advertisement\Models\AdvertisingPosition
 *
 * @property int $id
 * @property string $position_name
 * @property string $key
 * @property int $position_type_id
 * @property int $type 广告位，广告提取方式，0为权重方式 1为随机 2为提取所有广告
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property array $has_pushed 备注信息
 * @property int $ad_type 广告位类型 0为固定位 1为推送位
 * @property int $image_num 图片数量
 * @property int $image_width 图片宽度
 * @property int $image_height 图片高度
 * @property string $ad_tag 广告位tag信息
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Kanchangzhou\Advertisement\Models\Advertising> $advertisingInfo
 * @property-read int|null $advertising_info_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereAdTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereAdType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereHasPushed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereImageHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereImageNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereImageWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition wherePositionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition wherePositionTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertisingPosition withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdvertisingPosition
 */
class AdvertisingPosition extends Model implements HomeItemSubject, RedirectableInterface, HomeItemTemplate
{
    use SoftDeletes;
    //
    protected $table = 'advertising_position';

    protected $casts = [
        "has_pushed"=> "array"
    ];

    public function advertisingInfo(){
        $nowData = date("Y-m-d H:i:s");
        return $this->hasMany("Kanchangzhou\Advertisement\Models\Advertising", "advertising_position_id", "id")->where("status", "=",1)->where("start_show_time", "<", $nowData)->where("stop_show_time", ">", $nowData);
    }

    /**
     * @inheritDoc
     */
    public function homeItemTitle()
    {
        return $this->position_name;
    }

    /**
     * @inheritDoc
     */
    public function homtItemThumbnails(): HomeItemThumbnails
    {
        return new HomeItemThumbnails();
    }

    /**
     * @inheritDoc
     */
    public function widgetItems(): WidgetItems
    {
        return WidgetItems::emptyItems();
    }

    /**
     * @inheritDoc
     */
    public function homeItemRedirectable(): Redirectable
    {
        return new Redirectable();
    }

    public function homeItemPublishedAt()
    {
       return Carbon::now();
    }

    public function homeItemTemplate()
    {
        return HomeItemTemplate::HOME_ITEM_TEMPLATE_AD;
    }

    public function homeAdConfig(): HomeAdConfig
    {
        $homeAdConfig = new HomeAdConfig();
        $homeAdConfig->setAdPositionKey($this->key);
        $homeAdConfig->setAdPositionTitle($this->position_name);
        $homeAdConfig->setAdPositionStyle($this->position_type_id);
        return $homeAdConfig;
    }

    public function getRedirectTo()
    {
        return null;
    }

    public function getRedirectableType()
    {
        return null;
    }

    public function getRedirectableId()
    {
        return $this->getKey();
    }


}
