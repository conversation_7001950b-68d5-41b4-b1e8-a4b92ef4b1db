<?php

namespace Kanchangzhou\AI\Http\AdminControllers;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Kanchangzhou\AI\Exceptions\TextCorrectionException;
use Kanchangzhou\AI\Models\AuditTextCorrection;
use Kanchangzhou\AI\Supports\XunFeiNLP;
use Kanchangzhou\AI\Supports\XunFeiTextRewrite;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Kanchangzhou\Kernel\Supports\Respond;

class NLPController extends BaseController
{
    public function formOptions() {
        $types = AuditTextCorrection::TYPE_MAPPING;

        return Respond::respondWithData(compact('types'));
    }

    public function textCorrectionWordsList(Request $request) {
        PermissionHook::can('NLP.关键词列表');

        $words = AuditTextCorrection::when($request->input('type'), function ($query, $type) {
            $query->where('type', $type);
        })
                                    ->paginate();

        return Respond::respondWithData(JsonResource::collection($words));
    }

    public function textCorrectionWords(Request $request) {
        PermissionHook::can('NLP.关键词新增');

        $this->validate($request, [
            'type' => 'required|in:' . implode(',', array_keys(AuditTextCorrection::TYPE_MAPPING)),
            'word' => 'required|unique:' . AuditTextCorrection::class . ',word',
            'replace' => 'required_if:type,' . AuditTextCorrection::TYPE_BLACK,
        ], []);

        AuditTextCorrection::create([
            'type' => $request->input('type'),
            'word' => $request->input('word'),
            'replace' => $request->input('replace'),
        ]);

        XunFeiNLP::updateList();

        return Respond::respondWithData();
    }

    public function delTextCorrectionWord($id) {
        PermissionHook::can('NLP.关键词删除');

        $word = AuditTextCorrection::where('id', $id)
                                   ->firstOrFail();

        $word->delete();

        XunFeiNLP::updateList();

        return Respond::respondWithData();
    }

    public function textCorrection(Request $request) {
        PermissionHook::can('NLP.文本纠正');

        $this->validate($request, [
            'content' => 'required',
        ], [
            'content.required' => '无内容',
        ]);

        $res = XunFeiNLP::correction($request->input('content'), $request->input('source_id', 0), $request->input('source_type', ''));

        if ($res === false) {
            throw new TextCorrectionException(TextCorrectionException::TEXT_CONTENT_FAIL);
        }

        return Respond::respondWithData($res);
    }

    public function contentKeywords(Request $request) {
        PermissionHook::can('NLP.文本提取');

        $this->validate($request, [
            'content' => 'required',
        ], [
            'content.required' => '无内容',
        ]);

        $res = XunFeiNLP::keywords($request->input('content'));

        if ($res === false) {
            throw new TextCorrectionException(TextCorrectionException::TEXT_CONTENT_FAIL);
        }

        return Respond::respondWithData($res);
    }

    public function textRewrite(Request $request) {
        PermissionHook::can('NLP.文本改写');

        $this->validate($request, [
            'content' => 'required',
        ], [
            'content.required' => '无内容',
        ]);

        $level = $request->input('level', 4);

        $res = XunFeiTextRewrite::rewrite($request->input('content'), $level);

        if ($res === false) {
            throw new TextCorrectionException();
        }

        return Respond::respondWithData($res);
    }
}
