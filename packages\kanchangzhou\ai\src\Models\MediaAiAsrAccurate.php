<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\MediaAiAsrAccurate
 *
 * @property int $id
 * @property int $media_ai_file_id 文件ID
 * @property string|null $sentence 句子
 * @property string|null $begin 起始时间
 * @property string|null $end 结束时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Kanchangzhou\AI\Models\MediaAiFile|null $mediaAiFile
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereBegin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereMediaAiFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereSentence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaAiAsrAccurate whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMediaAiAsrAccurate
 */
class MediaAiAsrAccurate extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function mediaAiFile() {
        return $this->belongsTo(MediaAiFile::class);
    }
}
