<?php

namespace Kanchangzhou\AI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class MediaAiFileResource extends JsonResource
{
    public function toArray($request) {
        return [
            'file_type'=>$this->mediaAiFile?->media_type,
            'file_type_str'=>$this->mediaAiFile?->media_type_str,
            'target_file_id'=>$this->mediaAiFile?->fileaiable_id,
            'target_file_title'=>$this->mediaAiFile?->fileaiable?->title,
        ];
    }
}
