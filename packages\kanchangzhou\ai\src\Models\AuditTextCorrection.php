<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Kanchangzhou\AI\Models\AuditTextCorrection
 *
 * @property-read mixed $type_str
 * @method static \Illuminate\Database\Eloquent\Builder|AuditTextCorrection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditTextCorrection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AuditTextCorrection query()
 * @mixin \Eloquent
 * @mixin IdeHelperAuditTextCorrection
 */
class AuditTextCorrection extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'type_str',
    ];

    const TYPE_BLACK = 'black';
    const TYPE_WHITE = 'white';

    const TYPE_MAPPING = [
        self::TYPE_BLACK => '黑名单',
        self::TYPE_WHITE => '白名单',
    ];

    public function getTypeStrAttribute() {
        return key_exists($this->type, self::TYPE_MAPPING) ? self::TYPE_MAPPING[$this->type] : '';
    }
}
