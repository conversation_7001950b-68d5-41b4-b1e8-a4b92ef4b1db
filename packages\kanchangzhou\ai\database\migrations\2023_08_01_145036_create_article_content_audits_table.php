<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticleContentAuditsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('article_content_audits', function (Blueprint $table) {
            $table->id();
            $table->string('source_type');
            $table->string('source_id');
            $table->string('content_hash')
                  ->index();
            $table->json('audit_result')
                  ->nullable();
            $table->unsignedTinyInteger('has_notice')
                  ->nullable()
                  ->default(\Kanchangzhou\AI\Models\ArticleContentAudit::HAS_NOTICE_NO);
            $table->unsignedTinyInteger('type');
            $table->timestamps();

            $table->index([
                'source_type',
                'source_id',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('article_content_audits');
    }
}
