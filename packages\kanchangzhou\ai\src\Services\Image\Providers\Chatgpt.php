<?php

namespace Kanchangzhou\AI\Services\Image\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AIImageException;
use Kanchangzhou\AI\Services\Image\Contacts\AIImageService;
use Kanchangzhou\AI\Supports\ChatLimit;
use Orhanerday\OpenAi\OpenAi;

class Chatgpt extends AIImageService
{

    public function handler($action) {
        if (method_exists($this, $action)) {
            return $this->$action();
        }else{
            throw new AIException("{$this->module}不支持{$action}操作");
        }
    }

    public function txt2imgv3() {
        ChatLimit::imageCheckLimit();

        $body = [
            'prompt' => $this->request->input('prompt'),    //所需图像的文本描述。最大长度为 1000 个字符。必填字段。
            'size' => $this->request->input('size') ?: '1024x1024', //生成的图像的大小。 必须是 1024x1024、1792x1024 或 1024x1792 之一，用于 DALL·E-3 型号。
            'response_format' => $this->request->input('response_format') ?: 'url', //返回生成的图像的格式。必须是“url”或“b64_json”之一。默认为“url”。
            'style' => $this->request->input('style') ?: 'vivid',   //样式‘natural’ or ‘vivid’（“自然”或“生动”）：生成图像的样式。必须是生动或自然的。生动使模型倾向于生成超真实和戏剧性的图像。自然使模型生成更自然、看起来不那么逼真的图像。默认为“vivid”。
            'quality' => $this->request->input('quality') ?: 'standard',   //质量‘standard’ or ‘hd’（“标准”或“高清”）：将生成的图像的质量。“HD”创建的图像具有更精细的细节和更高的图像一致性。默认为“standard”。
            //'num' => intval($this->request->input('num')) ?: 1,   //要生成的图像数量。必须介于 1 和 10 之间。默认值为 1。对于 dall-e-3，仅支持 n=1。
        ];

        $openAi = new OpenAi(config('kai.image.providers.openai.api_key'));
        $openAi->setBaseURL(config('kai.image.providers.openai.base_url'));

        $res = $openAi->image([
            "model" => "dall-e-3",
            "n" => 1,
            "quality" => $body['quality'],
            "prompt" => $body['prompt'],
            "style" => $body['style'],
            "size" => $body['size'],
            "response_format" => $body['response_format'],
        ]);
        $res = json_decode($res);

        if (isset($res->error)) {
            //throw new \Exception($res->error->message);
            throw new AIImageException(AIImageException::INVALID_CONTENT,[],400,__("{$res->error->message}"));
        }else{
            /*
             * 计费
            Model	    Quality	    Resolution	            Price
            DALL·E 3	Standard	1024×1024	            $0.040 / image
                        Standard	1024×1792, 1792×1024	$0.080 / image
            DALL·E 3	HD	        1024×1024	            $0.080 / image
                        HD	        1024×1792, 1792×1024	$0.120 / image
            */

            if($body['quality'] == 'standard'){
                if($body['size'] == '1024x1024'){ $price = 0.04*8; }else{ $price = 0.08*8; };
            }else{
                if($body['size'] == '1024x1024'){ $price = 0.08*8; }else{ $price = 0.12*8; };
            }
            $basePoint = 0.04*8;    //基础张数 Standard 1024×1024
            $point = $price/$basePoint; //总张数
            ChatLimit::setProvider('Chatgpt')->setModule('txt2imgv3','AIImage')->imageIncrement($point);

            $data = $res->data[0];

            $image_local = null;
            $task_id = \Str::uuid();
            $path = 'ai-images'.'/'.date('Ymd').'/'.$task_id;
            if(isset($data->url) && $data->url){
                //url图片存储
                if (!\Storage::disk('oss')->exists($path)) {
                    \Storage::disk('oss')->putRemoteFile($path, $data->url);
                }
                $image_local = \Storage::disk('oss')->url($path);
            }
            if(isset($data->b64_json) && $data->b64_json){
                //base64图片存储
                if (!\Storage::disk('oss')->exists($path)) {
                    \Storage::disk('oss')->putRemoteFile($path, $data->b64_json);
                }
                $image_local = \Storage::disk('oss')->url($path);
            }

            list($width, $height) = explode('x', $body['size']);

            //历史记录
            $history = [
                'module' => Str::studly(class_basename(get_class($this))),
                'provider' => __FUNCTION__,
                'prompt' => $body['prompt'],
                'revised_prompt' => $data->revised_prompt,
                'width' => $width,
                'height' => $height,
                'image_num' => 1,
                'progress' => 1,
                'point' => $point,
                'price' => $price,
                'status' => 'SUCCESS',  //始终为 SUCCESS 成功状态
                'log_id' => $task_id,
                'task_id' => $task_id,
                'error_code' => 0,
                'approve_conclusion' => 'pass',
            ];

            //历史图片记录
            $history['result_list'][0] = [
                'image_orgin' => $data->url ?? null,
                'image_base64' => $data->b64_json ?? null,
                'image_local' => $image_local,
                'status' => 'SUCCESS',
                'progress' => 1,
                'error_code' => 0,
                'approve_conclusion' => 'pass',
            ];

            $res = $this->createHistoryUrl($history);
            return $res;
        }
    }
}
