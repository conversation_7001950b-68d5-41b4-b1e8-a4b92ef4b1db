<?php

namespace Changguan\LaravelSDK\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * 常观 SDK Facade
 *
 * @method static \Changguan\SDK\Push\PushClient push() 获取推送客户端实例
 * @method static \Changguan\SDK\Crypto\GmSm crypto() 获取加密服务实例
 * @method static \Changguan\SDK\Auth\JWTDecoder jwt() 获取JWT解码器实例
 * @method static \Changguan\SDK\Auth\SignatureValidator signature() 获取签名验证器实例
 * @method static \Changguan\SDK\Http\Tools tools() 获取工具集实例
 * @method static \Changguan\SDK\Navigation\RedirectToBuilder redirect() 获取跳转链接构建器实例
 * @method static \Changguan\LaravelSDK\Application project(string $project) 切换项目
 * @method static \Changguan\SDK\OAuth\OauthClient oauth() 获取Oauth客户端实例
 * @method static string getCurrentProject() 获取当前项目名称
 * @method static string version() 获取SDK版本
 *
 * @see \Changguan\LaravelSDK\Application
 */
class Changguan extends Facade
{
    /**
     * 获取组件的注册名称
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'changguan';
    }
}
