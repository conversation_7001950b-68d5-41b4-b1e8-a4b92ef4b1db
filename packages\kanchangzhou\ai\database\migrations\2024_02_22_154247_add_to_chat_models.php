<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->string('api_gateway')
                  ->nullable()
                  ->comment('api网关');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->dropColumn('api_gateway');
        });
    }
};
