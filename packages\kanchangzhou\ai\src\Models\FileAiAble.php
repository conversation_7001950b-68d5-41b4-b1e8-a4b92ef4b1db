<?php

namespace Kanchangzhou\AI\Models;

use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait FileAiAble
{
    use HasRelationships;

    /**
     * 一个模型对应一个MediaAiFile
     */
    public function mediaAiFile() {
        return $this->morphOne(MediaAiFile::class, 'fileaiable');
    }

    /**
     * 创建 MediaAiFile的media_type 默认为 doc
     */
    public function createMediaAiFile($media_type = MediaAiFile::MEDIA_TYPE_DOC) {
        return $this->mediaAiFile()
                    ->create([
                        'media_type' => $media_type,
                    ]);
    }

    /**
     * 创建 MediaAiFile 的 media_type 为 image
     */
    public function createMediaAiFileImage() {
        return $this->createMediaAiFile(MediaAiFile::MEDIA_TYPE_IMAGE);
    }

    /**
     * 创建 MediaAiFile 的 media_type 为 video
     */
    public function createMediaAiFileVideo() {
        return $this->createMediaAiFile(MediaAiFile::MEDIA_TYPE_VIDEO);
    }

    /**
     * 创建 MediaAiFile 的 media_type 为 audio
     */
    public function createMediaAiFileAudio() {
        return $this->createMediaAiFile(MediaAiFile::MEDIA_TYPE_AUDIO);
    }

    /**
     * 创建 MediaAiFile 的 media_type 为 doc
     */
    public function createMediaAiFileDoc() {
        return $this->createMediaAiFile(MediaAiFile::MEDIA_TYPE_DOC);
    }

    /**
     * 一个模型可以有多个MediaAiEventname, 通过MediaAiFile模型对应, 多态远程一对多
     */
    public function eventnames() {
        return $this->hasManyThrough(MediaAiEventname::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型可以有多个MediaAiNewsclassification
     */
    public function newsclassifications() {
        return $this->hasManyThrough(MediaAiNewsclassification::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型可以有多个MediaAiNer
     */
    public function ners() {
        return $this->hasManyThrough(MediaAiNer::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型可以有多个MediaAiAsrAccurate
     */
    public function asrAccurates() {
        return $this->hasManyThrough(MediaAiAsrAccurate::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型可以有多个MediaAiFace
     */
    public function faces() {
        return $this->hasManyThrough(MediaAiFace::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型可以有多个MediaAiKeyword
     */
    public function keywords() {
        return $this->hasManyThrough(MediaAiKeyword::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型可以有一个MediaAiSummary
     */
    public function summary() {
        return $this->hasOneThrough(MediaAiSummary::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }

    /**
     *  一个模型多个MediaAiTask
     */
    public function tasks() {
        return $this->hasManyThrough(MediaAiTask::class, MediaAiFile::class, 'fileaiable_id', 'media_ai_file_id', 'id', 'id');
    }
}
