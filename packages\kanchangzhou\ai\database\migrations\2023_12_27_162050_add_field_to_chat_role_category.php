<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_role_categories', function (Blueprint $table) {
            $table->tinyInteger('sort')
                  ->nullable()
                  ->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_role_categories', function (Blueprint $table) {
            $table->dropColumn('sort');
        });
    }
};
