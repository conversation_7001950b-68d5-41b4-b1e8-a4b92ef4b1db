<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('ai_audio_histories', function (Blueprint $table) {
            $table->string('failed_reason')
				->nullable()
                ->comment('失败原因')
                ->after('status');
			$table->string('task_params',1000)
				->nullable()
                ->comment('请求参数')
                ->after('task_id');
			$table->string('result_params',1000)
				->nullable()
                ->comment('返回参数')
                ->after('task_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('ai_audio_histories', function (Blueprint $table) {
            $table->dropColumn('failed_reason');
        });
		Schema::table('ai_audio_histories', function (Blueprint $table) {
            $table->dropColumn('task_params');
        });
		Schema::table('ai_audio_histories', function (Blueprint $table) {
            $table->dropColumn('result_params');
        });
    }
};
