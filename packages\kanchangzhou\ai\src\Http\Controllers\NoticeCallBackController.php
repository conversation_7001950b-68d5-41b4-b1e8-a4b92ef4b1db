<?php

namespace Kanchangzhou\AI\Http\Controllers;

use AlibabaCloud\SDK\Live\V20161101\Models\GetMultiRateConfigListResponseBody\groupInfo\info;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

//use Intervention\Image\ImageManager;
use Intervention\Image\ImageManagerStatic as Image;
use Kanchangzhou\AI\Exceptions\AiAudioException;
use Kanchangzhou\AI\Exceptions\AIException;
use Kanchangzhou\AI\Exceptions\AiVideoException;
use Kanchangzhou\AI\Jobs\MediaAi\MediaAiResponseDispatchJob;
use Kanchangzhou\AI\Jobs\VideoModerationCallbackJob;
use Kanchangzhou\AI\Jobs\VideoModerationTaskJob;
use Kanchangzhou\AI\Models\AiAudioHistory;
use Kanchangzhou\AI\Models\AiImageHistory;
use Kanchangzhou\AI\Models\AiImageUrl;
use Kanchangzhou\AI\Models\AiMusicHistory;
use Kanchangzhou\AI\Models\AiVideoAudit;
use Kanchangzhou\AI\Models\AiVideoAuditLabel;
use Kanchangzhou\AI\Models\AiVideoHistory;
use Kanchangzhou\AI\Models\ChatModel;
use Kanchangzhou\AI\Models\ChatRole;
use Kanchangzhou\AI\Services\AiUser;
use Kanchangzhou\AI\Supports\GreenContent;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class NoticeCallBackController extends BaseController
{
    use AiVideoAuditLabel;

    public function volcengineCallback(Request $request) {
        //info('audio ai callback', $request->all());
        $data = $request->all();

        $history = AiAudioHistory::where('task_id', $data['task_id'])
                                 ->firstOrFail()
                                 ->toArray();
        if ($history['status'] == 'FAILED') {
            throw new AiAudioException(AiAudioException::TASK_FAILED, [], 400, $history['failed_reason']);
        }
        if ($history['status'] == 'SUCCESS') {
            throw new AiAudioException(AiAudioException::TASK_SUCCESS);
        }

        if ($data['task_status'] == 1) { //成功
            //本地化视频
            $path = 'ai-audio' . '/' . date('Ymd') . '/' . $data['task_id'];
            if (!\Storage::disk('oss')
                         ->exists($path)) {
                \Storage::disk('oss')
                        ->putRemoteFile($path, $data['audio_url']);
            }
            $audioLocal = \Storage::disk('oss')
                                  ->url($path);
            if ($history) {
                $updateData = [
                    'progress' => 100,
                    'status' => 'SUCCESS',
                    'video_origin' => $data['audio_url'],
                    'video_local' => $audioLocal,
                    'result_params' => json_encode($data),
                ];
                AiAudioHistory::updateByTaskId($data['task_id'], $updateData);
            }
        } else {
            //失败
            if ($data['task_status'] == 2) {
                AiAudioHistory::updateByTaskId($data['task_id'], [
                    'status' => 'FAILED',
                    'failed_reason' => $data['message'],
                ]);
                throw new AiAudioException(AiAudioException::TASK_FAILED);
            }
        }

        return Respond::respondWithData();
    }

    public function mjCallback(Request $request) {
        //info('image ai callback', $request->all());

        $data = $request->all();
        if ($data['task_id']) {
            $body = [
                'progress' => $data['progress'] ?? null,
                'status' => ($data['status'] == 3 ? 'SUCCESS' : 'RUNNING'),
                'task_id' => $data['task_id'],
                //'progress_data' => $data['progress'],
                //'image_orgin' => $data['image_url'],
                //'actions' => $data['actions'] ?? null,
            ];

            //生图失败
            if ($data['status'] == 2) {
                $body = [
                    'progress' => '0%',
                    'task_id' => $data['task_id'],
                    'status' => 'FAILED',
                    'failed_reason' => $data['msg'],
                ];
            }

            //成功状态更新放到图片生成后
            //AiImageHistory::where('task_id', $body['task_id'])->update($body);

            if ($data['status'] == 3 && $data['image_url']) {
                $history = AiImageHistory::where('task_id', $body['task_id'])
                                         ->first();
                if ($history) {
                    //成功新增子数据 不成功不新增
                    $path = 'ai-images' . '/' . date('Ymd') . '/' . $body['task_id'];

                    //本地化图片
                    if (!\Storage::disk('oss')
                                 ->exists($path)) {
                        \Storage::disk('oss')
                                ->putRemoteFile($path, $data['image_url']);
                    }
                    $imageLocal = \Storage::disk('oss')
                                          ->url($path);

                    if (strpos($history['action'], 'upsample') !== false) {
                        //upsample 只生成一张图
                        $url_data = [];
                        $url_data['image_orgin'] = $data['image_url'];
                        $url_data['image_local'] = $imageLocal;
                        $url_data['task_id'] = $history['task_id'];
                        $url_data['user_id'] = $history['user_id'];
                        $url_data['guard_name'] = $history['guard_name'];
                        $url_data['status'] = $body['status'] ?? null;
                        $url_data['progress'] = $body['progress'] ?? null;
                        $url_data['history_id'] = $history['id'];

                        AiImageUrl::create($url_data);
                    } else {
                        // generate variation reroll 将图片一分为四
                        // 创建临时目录（如果不存在）
                        $tempDir = storage_path('images/temp');
                        if (!file_exists($tempDir)) {
                            mkdir($tempDir, 0755, true);
                        }

                        // 下载图片到本地临时文件夹
                        $tempPath = storage_path('images/temp/temp.jpg');
                        file_put_contents($tempPath, file_get_contents($imageLocal));

                        $index = 1;
                        $subImages = [];
                        for ($row = 0; $row < 2; $row++) {
                            for ($col = 0; $col < 2; $col++) {

                                // 使用 Intervention Image 处理图像
                                //$image = ImageManager::gd()->read($tempPath);
                                $image = Image::make($tempPath);    //使用新版本

                                $width = $image->width();
                                $height = $image->height();
                                $subWidth = $width / 2;
                                $subHeight = $height / 2;

                                // 裁剪子图像
                                $subImage = $image->crop($subWidth, $subHeight, $col * $subWidth, $row * $subHeight);
                                $subImageName = $data['task_id'] . '-' . $index;
                                $subImage->save(storage_path('images/temp/' . $subImageName));

                                // 上传分割后的图片到 OSS
                                $subImagePath = 'ai-images' . '/' . date('Ymd') . '/' . $subImageName;
                                Storage::disk('oss')
                                       ->put($subImagePath, file_get_contents(storage_path('images/temp/' . $subImageName)));
                                $subImages[] = Storage::disk('oss')
                                                      ->url($subImagePath);

                                $index++;
                            }
                        }

                        // 删除临时文件
                        unlink($tempPath);
                        foreach ($subImages as $subImage) {
                            unlink(storage_path('images/temp/' . basename($subImage)));
                        }

                        //批量插入
                        foreach ($subImages as $k => $v) {
                            $url_data = [];
                            $url_data['image_local'] = $v;
                            $url_data['task_id'] = $history['task_id'];
                            $url_data['user_id'] = $history['user_id'];
                            $url_data['guard_name'] = $history['guard_name'];
                            $url_data['status'] = $body['status'] ?? null;
                            $url_data['progress'] = $body['progress'] ?? null;
                            $url_data['history_id'] = $history['id'];

                            AiImageUrl::create($url_data);
                        }
                    }
                }
            }

            // 更新任务状态
            AiImageHistory::where('task_id', $body['task_id'])
                          ->update($body);

        } else {
            throw new AIImageException(AIImageException::INVALID_PARAMETER, [], 400, '参数请求错误');
        }

        return Respond::respondWithData();
    }

    public function mediaAiCallback(Request $request) {
//        info('media ai notice', $request->input());

        dispatch(new MediaAiResponseDispatchJob($request->input()));

        return Respond::respondWithData();
    }

    public function pixverseCallback(Request $request) {
        //info('video ai callback', $request->all());
        $data = $request->all();

        if ($data['status'] == 3 && ($data['video_url'] ?? null)) { //成功
            $history = AiVideoHistory::where('task_id', $data['task_id'])
                                     ->first();
            if ($history) {
                //本地化视频
                $path = 'ai-videos' . '/' . date('Ymd') . '/' . $data['task_id'] . '.mp4';
                if (!\Storage::disk('oss')
                             ->exists($path)) {
                    \Storage::disk('oss')
                            ->putRemoteFile($path, $data['video_url']);
                }
                $videoLocal = \Storage::disk('oss')
                                      ->url($path);

                // 更新任务状态
                AiVideoHistory::where('task_id', $data['task_id'])
                              ->update([
                                  'progress' => 100,
                                  'status' => 'SUCCESS',
                                  'video_origin' => $data['video_url'],
                                  'video_local' => $videoLocal,
                              ]);
            }
        } else {
            throw new AiVideoException(AiVideoException::INVALID_PARAMETER, [], 400, '参数请求错误');
        }

        return Respond::respondWithData();
    }

    /**
     * 多米suno
     */
    public function DuomiCallback($task_code, Request $request) {
        $data = $request->post();

        if (empty($task_code) || empty($data)) {
            exit;
        }
        $music = AiMusicHistory::where('task_code', $task_code)
                               ->orderByDesc('id')
                               ->first();

        if (!$music || $music['state'] == 1) {
            exit;
        }

        // status 1执行任务中; 2失败; 3成功
        if ($data['status'] == 3) {
            if (empty($music['result1'])) {
                $field = 'result1';
                $state = 0; // state 0生成中 1完成 2失败
                $finish_time = 0;
            } elseif (empty($music['result2'])) {
                $field = 'result2';
                $state = 1;
                $finish_time = time();
            } else {
                exit;
            }

            AiMusicHistory::where('id', $music['id'])
                          ->update([
                              'state' => $state,
                              $field => json_encode($data),
                              'errmsg' => '',
                              'finish_time' => $finish_time,
                          ]);

            // 保存到oss
            try {
//                Log::info($data);
                $path = 'ai-music' . '/' . date('Ymd') . '/';
                $audio_path = $path . explode('cdn1.suno.ai/', stripslashes($data['audio_url']))[1];
                $video_path = $path . explode('cdn1.suno.ai/', stripslashes($data['video_url']))[1];
                $image_path = $path . explode('cdn1.suno.ai/', stripslashes($data['image_url']))[1];
                $image_large_path = $path . explode('cdn1.suno.ai/', stripslashes($data['image_large_url']))[1];

                Storage::disk('oss')
                       ->putRemoteFile($audio_path, $data['audio_url']);
                Storage::disk('oss')
                       ->putRemoteFile($video_path, $data['video_url']);
                Storage::disk('oss')
                       ->putRemoteFile($image_path, $data['image_url']);
                Storage::disk('oss')
                       ->putRemoteFile($image_large_path, $data['image_large_url']);

                $data['audio_url'] = Storage::disk('oss')
                                            ->url($audio_path);
                $data['video_url'] = Storage::disk('oss')
                                            ->url($video_path);
                $data['image_url'] = Storage::disk('oss')
                                            ->url($image_path);
                $data['image_large_url'] = Storage::disk('oss')
                                                  ->url($image_large_path);
                AiMusicHistory::where('id', $music['id'])
                              ->update([
                                  $field => json_encode($data),
                              ]);
            } catch (\Exception $error) {
                Log::info($error->getMessage());
                exit;
            }

        } elseif ($data['status'] == 2) {
            throw new AIException(AIException::NETWORK_ERROR, [], 400, '请求失败');
        }

    }

    public function ossUploadCallback(Request $request) {
//        info('oss-upload', $request->input());
//        info('oss-server', $request->server());
        // 验证数据
        $authorization = base64_decode($request->server('HTTP_AUTHORIZATION'));
        $pubKeyUrl = base64_decode($request->server('HTTP_X_OSS_PUB_KEY_URL'));
        $pubKey = Http::get($pubKeyUrl)
                      ->body();
        $path = $request->server('REQUEST_URI');
//        $path = '/api/v1/ai/oss/callback';
        $bodyStr = http_build_query($request->input());
        $authStr = urldecode($path) . "\n" . $bodyStr;

        if (!openssl_verify($authStr, $authorization, $pubKey, OPENSSL_ALGO_MD5)) {
//            info('oss-server', compact('authorization', 'pubKeyUrl', 'pubKey', 'path', 'bodyStr', 'authStr'));

            return Respond::respondWithData()
                          ->setStatusCode(400);
        }

        // 记录数据
        $video = AiVideoAudit::firstOrCreate([
            'etag' => strtolower($request->input('etag')),
        ], [
            'video_uuid' => $request->input('uuid'),
            'title' => $request->input('name'),
            'video_path' => $request->input('filename'),
            'etag' => strtolower($request->input('etag')),
            'nickname' => $request->input('nickname'),
            'user_id' => $request->input('uid'),
            'status' => AiVideoAudit::STATUS_PENDING,
        ]);

        if ($video->status == AiVideoAudit::STATUS_PROCESSING || $video->status == AiVideoAudit::STATUS_SUCCESS) {
            return Respond::respondWithData($video);
        }

//        dispatch_sync(new VideoModerationTaskJob($video));
        dispatch(new VideoModerationTaskJob($video))->onQueue('media_ai');

        return Respond::respondWithData();
    }

    public function moderationCallback(Request $request) {
//        info('moderation-info', $request->input());

        // 验证数据合法性
        $content = json_decode($request->input('content'), true);

        $signStr = config('kai.other.aliyun.oss.uid') . Str::replace('-', '', $content['Data']['DataId']) . $request->input('content');

        if ($request->input('checksum') != hash('sha256', $signStr)) {
            return Respond::respondWithData()
                          ->setStatusCode(400);
        }

        $video = AiVideoAudit::where('task_id', $request->input('taskId'))
                             ->firstOrFail();

        if ($video->status == AiVideoAudit::STATUS_SUCCESS) {
            return Respond::respondWithData();
        }
        // 获取回调数据

//        dispatch_sync(new VideoModerationCallbackJob($video));
        dispatch(new VideoModerationCallbackJob($video))->onQueue('media_ai');

        // 保存数据
        return Respond::respondWithData();
    }
}
