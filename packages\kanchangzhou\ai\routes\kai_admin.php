<?php

use Illuminate\Support\Facades\Route;
use Kanchangzhou\AI\Http\AdminControllers\{Chat<PERSON>ontroller,
    ChatRoleController,
    ChatRoleCategoryController,
    ChatModelsController,
    AgentsController
};

Route::prefix('api/v1/admin/ai')
     ->middleware(config('kauth.auth_middleware.admin'))
     ->as('ai.admin.')
     ->group(function () {

//         Route::post('gpt/chat', [
//             ChatController::class,
//             'index',
//         ]);
//
         Route::get('gpt/chat/history', [
             ChatController::class,
             'chatHistory',
         ]);

         Route::get('gpt/chat/history/{uuid}', [
             ChatController::class,
             'chatHistoryDetail',
         ]);

         Route::delete('gpt/chat/history/{uuid}', [
             ChatController::class,
             'delHistory',
         ]);
//
//         Route::post('gpt/role-chat', [
//             ChatController::class,
//             'roleChat',
//         ]);

         Route::get('gpt/roles/form-options', [
             ChatRoleController::class,
             'formOptions',
         ]);

         Route::get('gpt/chat-models/form-options', [
             ChatModelsController::class,
             'formOptions',
         ]);

         Route::post('gpt/roles/{id}/approve', [
             ChatRoleController::class,
             'approve',
         ]);

         Route::resource('gpt/roles', ChatRoleController::class);


         Route::get('gpt/agents/form-options', [
             AgentsController::class,
             'formOptions',
         ]);

         Route::resource('gpt/agents', AgentsController::class);

         Route::resource('gpt/role-category', ChatRoleCategoryController::class);

         Route::resource('gpt/chat-models', ChatModelsController::class);

//         Route::post('gpt/qwen', [
//             ChatController::class,
//             'qianwen',
//         ]);
//         Route::post('gpt/qwen-baichuan', [
//             ChatController::class,
//             'qianwenBaichuan',
//         ]);
//         Route::post('gpt/qwen-vl', [
//             ChatController::class,
//             'qianwenVL',
//         ]);
//         Route::post('gpt/qwen-glm', [
//             ChatController::class,
//             'qianwenGLM',
//         ]);

         Route::post('aigc/wanx', [
             \Kanchangzhou\AI\Http\AdminControllers\AigcController::class,
             'wanx',
         ]);

         Route::get('aigc/wanx-query-task', [
             \Kanchangzhou\AI\Http\AdminControllers\AigcController::class,
             'wanxQuestTask',
         ]);

         //图像生成
         Route::post('imagehan/generate-image-with-text', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageHanController::class,
             'generateImageWithText',
         ]);

         //图像生成，任务查询
         Route::get('imagehan/get-imageenhan-job-result', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageHanController::class,
             'getImageenhanJobResult',
         ]);

         //图像超分
         Route::post('imagehan/make-super-resolution-image', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageHanController::class,
             'makeSuperResolutionImage',
         ]);

         //图像分割
         Route::post('imagehan/segment-common-image', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageHanController::class,
             'segmentCommonImage',
         ]);

         //图像分割，任务查询
         Route::get('imagehan/get-imageseg-job-result', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageHanController::class,
             'getImagesegJobResult',
         ]);

         //百度人工智能，logo商标识别
         Route::post('image-ai/logo-delete', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'logoDelete',
         ]);

         //百度人工智能，图片无损放大
         Route::post('image-ai/image-quality-enhance', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'imageQualityEnhance',
         ]);

         //百度人工智能，图像修复
         Route::post('image-ai/inpainting', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'inpainting',
         ]);

         //百度人工智能，文档图片去底纹
         Route::post('image-ai/doc-repair', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'docRepair',
         ]);

         //百度人工智能，图像去噪
         Route::post('image-ai/denoise', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'denoise',
         ]);

         //百度人工智能，文生图
         Route::post('image-ai/txt2img', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'txt2img',
         ]);

         //百度人工智能，文生图结果查询
         Route::post('image-ai/get-img', [
             \Kanchangzhou\AI\Http\AdminControllers\ImageAiController::class,
             'getImg',
         ]);

         //通用文生图
         Route::post('ai-image/txt2img', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'txt2img',
         ]);

         //通用文生图结果查询
         Route::post('ai-image/get-img', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'getImg',
         ]);

         //通用文生图高级版
         Route::post('ai-image/txt2imgv2', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'txt2imgv2',
         ]);

         //通用文生图高级版结果查询
         Route::post('ai-image/get-img-v2', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'getImgv2',
         ]);

         //通用文生图高级版历史记录
         Route::post('ai-image/get-img-history', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'getImgHistory',
         ]);

         //通用AI成片
         Route::post('ai-image/ttv', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'ttv',
         ]);

         //通用AI成片高级版结果查询
         Route::post('ai-image/get-ttv', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'getTtv',
         ]);

         //通用图像去噪
         Route::post('ai-image/denoise', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'denoise',
         ]);

         //通用文档图片去底纹
         Route::post('ai-image/doc-repair', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'docRepair',
         ]);

         //通用图像修复
         Route::post('ai-image/inpainting', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'inpainting',
         ]);

         //通用图片无损放大
         Route::post('ai-image/image-quality-enhance', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'imageQualityEnhance',
         ]);

         //通用logo商标识别
         Route::post('ai-image/logo-search', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'logoSearch',
         ]);

         //通用logo商标识别
         Route::post('ai-image/logo-add', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'logoAdd',
         ]);

         //通用logo商标识别
         Route::post('ai-image/logo-delete', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'logoDelete',
         ]);

         //通用文生图实时版本
         Route::post('ai-image/txt2imgv3', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'txt2imgv3',
         ]);

         //文本转语音
         Route::post('ai-image/text2audio', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'text2audio',
         ]);

         //文本转语音结果查询
         Route::post('ai-image/text2audio-query', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'text2audioQuery',
         ]);

         //语音识别
         Route::post('ai-image/audio2text', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'audio2text',
         ]);

         //语音识别结果查询
         Route::post('ai-image/audio2text-query', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'audio2textQuery',
         ]);

         //图片转视频
         Route::post('ai-image/img2video', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'img2video',
         ]);

         //获取图片转视频结果
         Route::post('ai-image/img2video-query', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'getImg2video',
         ]);

         //通用文生图SD3
         Route::post('ai-image/txt2imgv4', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'txt2imgv4',
         ]);

         //通用文生图MJ生成
         Route::post('ai-image/txt2imgv5', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'txt2imgv5',
         ]);

         //通用文生图MJ状态显示
         Route::post('ai-image/get-img-v5', [
             \Kanchangzhou\AI\Http\AdminControllers\AIImageController::class,
             'getImgv5',
         ]);
         //生成音乐
         Route::post('ai-music/generate', [
             \Kanchangzhou\AI\Http\AdminControllers\AIMusicController::class,
             'generate',
         ]);
         //生成歌词
         Route::post('ai-music/generate-lyric', [
             \Kanchangzhou\AI\Http\AdminControllers\AIMusicController::class,
             'generateLyrics',
         ]);
         //生成音乐列表
         Route::post('ai-music/list', [
             \Kanchangzhou\AI\Http\AdminControllers\AIMusicController::class,
             'getMusicHistory',
         ]);
         //生成音乐查询结果
         Route::post('ai-music/result', [
             \Kanchangzhou\AI\Http\AdminControllers\AIMusicController::class,
             'getMusicResult',
         ]);


     });

