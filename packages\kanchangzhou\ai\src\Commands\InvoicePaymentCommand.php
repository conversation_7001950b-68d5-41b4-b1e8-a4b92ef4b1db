<?php

namespace Kanchangzhou\AI\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Kanchangzhou\AI\Models\AiOrder;
use Kanchangzhou\AI\Models\AiResourcePackage;
use Kanchangzhou\AI\Models\AiUserPackage;

class InvoicePaymentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:payment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        AiOrder::with('items')
               ->where('status', AiOrder::STATUS_PENDING)
               ->where('order_type', AiOrder::ORDER_TYPE_CHAT)
               ->chunk(100, function ($orders) {
                   foreach ($orders as $order) {
                       // 按item进行结算, 优先使用对应provider,model_id,model_type的资源包,如果不够则使用通用资源包
                       foreach ($order->items as $item) {
                           $paidFlag = false;

                           $userPackage = AiUserPackage::where('user_id', $order->user_id)
                                                       ->where('guard_name', $order->guard_name)
                                                       ->where('model_id', $item->model_id)
                                                       ->where('model_type', $item->order_type)
                                                       ->where('provider', $item->provider)
                                                       ->where('package_type', $item->order_type)
                                                       ->where('expired_at', '>=', $order->created_at)
                                                       ->first();

                           if (!$userPackage) {
                               $userPackage = AiUserPackage::where('user_id', $order->user_id)
                                                           ->where('guard_name', $order->guard_name)
                                                           ->where('is_general', AiResourcePackage::IS_GENERAL_YES)
                                                           ->where('package_type', $item->order_type)
                                                           ->where('expired_at', '>=', $order->created_at)
                                                           ->first();
                           }

                           if (!$userPackage || ($order->items[0]->id == $item->id && $userPackage->total_tokens <= $userPackage->used_tokens)) {
                               break;
                           }

                           $userPackage->used_tokens += $item->total_tokens;
                           $userPackage->save();

                           $item->status = AiOrder::STATUS_PAID;
                           $item->save();

                           $paidFlag = true;
                       }

                       if ($paidFlag ?? false) {
                           $order->status = AiOrder::STATUS_PAID;
                           $order->paid_at = Carbon::now();
                           $order->payment_no = 'P' . date('YmdHis') . rand(1000, 9999);
                           $order->payment_method = AiOrder::PAYMENT_METHOD_PACKAGE;
                           $order->save();
                       }
                   }
               });
    }
}
