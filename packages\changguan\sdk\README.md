# Changguan SDK

常观 SDK 提供了一套完整的工具集，用于与常观平台进行交互。本 SDK 支持认证、消息推送、加密服务、签名验证等核心功能。

## 功能特性

### HTTP 客户端
- 通用请求处理
    - 支持动态切换基础 URL
    - 自动签名验证
    - 统一错误处理
    - 响应解析
    - 支持所有标准 HTTP 方法
    - 可配置的请求选项

### OAuth2.0 认证
- 授权码模式
    - 获取访问令牌
    - 刷新访问令牌
- 用户信息
    - 获取基本信息
    - 支持可选字段
- 令牌管理
    - 有效期检查
    - 状态管理

### 消息推送
- 单用户消息
    - 模板消息
    - 支持扩展参数
    - 自定义跳转
- 广播消息
    - 全局广播
    - 分组广播
- 订阅管理
    - 创建订阅
    - 用户订阅
    - 触发推送

### 加密服务
- SM2 加解密
    - 支持公钥加密
    - 支持私钥解密
- SM2 签名
    - 数字签名
    - 签名验证
- SM3 摘要
    - 数据摘要生成
    - 摘要验证

### 导航构建
- 内部跳转
    - 模块间导航
    - 参数传递
- 小程序跳转
    - 微信小程序
    - 支付宝小程序
    - 自有小程序
- 外部链接
    - URL跳转
    - 参数处理

## 安装

### Composer 安装

```bash
composer config repositories.changguan/<NAME_EMAIL>:zhong5w/changguan-sdk.git
composer require changguan/sdk
```

### Laravel 安装

Laravel 用户请查看 [常观Laravel SDK](https://gitee.com/zhong5w/changguan-laravel-sdk)。

## 快速开始

### 1. 初始化 SDK

```php
use Changguan\SDK\ChangguanSDK;

$config = [
    'client_id' => 'your_client_id',
    'client_access_key' => 'your_access_key',
    'client_access_secret' => 'your_access_secret',
    'push_base_url' => 'https://api.changguan.com',
    'oauth_base_url' => 'https://oauth.changguan.com',
    'http_timeout' => 30,
    'http_connect_timeout' => 10
];

$sdk = new ChangguanSDK($config);
```

### 2. HTTP 客户端使用

#### 基本用法
```php
// 使用默认配置的客户端
$response = $sdk->http()->get('/api/users', ['page' => 1]);

// POST 请求
$response = $sdk->http()->post('/api/users', [
    'name' => 'test',
    'email' => '<EMAIL>'
]);
```

#### 动态切换基础URL
```php
// 使用新的基础URL
$response = $sdk->http('https://api2.example.com')
    ->get('/other-service');

// 或者使用 withBaseUrl 方法
$newClient = $sdk->http()->withBaseUrl('https://api2.example.com');
$response = $newClient->get('/endpoint');
```

#### 自定义配置
```php
// 使用自定义配置
$client = $sdk->http()
    ->withConfig([
        'timeout' => 60,
        'verify' => false
    ]);

$response = $client->post('/api/upload', $data);
```

### 3. OAuth2.0 认证

```php
try {
    // 获取访问令牌
    $tokenInfo = $sdk->oauth()->getAccessToken('authorization_code');
    
    // 获取用户信息
    $userInfo = $sdk->oauth()->getUserInfo(
        $tokenInfo['access_token'],
        $tokenInfo['open_id']
    );
    
    // 刷新访问令牌
    $newToken = $sdk->oauth()->refreshToken($tokenInfo['refresh_token']);
    
    // 工作台应用
    // 同步通讯录部门
    $departments = $sdk->oauth()->getEnterpriseDepartmentList();
    
    // 同步通讯录
    $adminUsers = $sdk->oauth()->getEnterpriseFullContacts();

} catch (\Changguan\SDK\Exceptions\OAuthException $e) {
    echo "OAuth错误: " . $e->getMessage();
}
```

### 4. 消息推送

```php
try {
    // 单用户推送
    $result = $sdk->push()
        ->withUser('user_open_id')
        ->withTemplate('template_code')
        ->withParams([
            'title' => '消息标题',
            'content' => '消息内容'
        ])
        ->withRedirect('changguan://module/page')
        ->pushToUser();

    // 广播消息
    $result = $sdk->push()
        ->withTemplate('broadcast_template')
        ->withParams(['content' => '广播内容'])
        ->broadcast();

    // 订阅管理
    $sdk->push()
        ->withTemplate('subscription_template')
        ->withSubscriptionName('每日提醒')
        ->withSubscriptionDescription('每日资讯推送')
        ->createSubscription();

} catch (\Changguan\SDK\Exceptions\PushException $e) {
    echo "推送错误: " . $e->getMessage();
}
```

### 5. 加密服务

```php
try {
    // SM2 加密
    $encrypted = $sdk->crypto()->sm2Encrypt('明文数据');

    // SM2 解密
    $decrypted = $sdk->crypto()->sm2Decrypt($encrypted);

    // SM2 签名
    $signature = $sdk->crypto()->sm2Sign('待签名数据', 'user_id');

    // SM2 验签
    $isValid = $sdk->crypto()->sm2VerifySign('原始数据', $signature, 'user_id');

    // SM3 摘要
    $digest = $sdk->crypto()->sm3Digest('待计算数据');

} catch (\Changguan\SDK\Exceptions\CryptoException $e) {
    echo "加密错误: " . $e->getMessage();
}
```

### 6. 导航链接

```php
// 构建内部跳转链接
$url = $sdk->redirect()->rongmei('article', '123');

// 微信小程序跳转
$url = $sdk->redirect()->wxMiniApp(
    'wx1234567890',     // appId
    'pages/index/main',  // 页面路径
    ['id' => '123']     // 参数
);

// 支付宝小程序跳转
$url = $sdk->redirect()->aliMiniApp(
    '2019123456789012',
    'pages/home/<USER>',
    ['source' => 'sdk']
);
```

## 异常处理

SDK 使用不同的异常类型来表示不同类型的错误：

```php
use Changguan\SDK\Exceptions\{
    HttpException,
    OAuthException,
    PushException,
    CryptoException,
    SDKException
};

try {
    // SDK 操作
} catch (HttpException $e) {
    // 处理 HTTP 请求相关异常
    // 错误码范围：5001-5999
} catch (OAuthException $e) {
    // 处理认证相关异常
    // 错误码范围：6001-6999
} catch (PushException $e) {
    // 处理推送相关异常
    // 错误码范围：3001-3999
} catch (CryptoException $e) {
    // 处理加密相关异常
    // 错误码范围：2001-2999
} catch (SDKException $e) {
    // 处理其他 SDK 异常
    // 错误码范围：4001-4999
}
```

### 错误码说明

- HTTP 异常 (5001-5999)
    - 5001: 请求失败
    - 5002: 响应解析失败
    - 5003: API错误
    - 5004: 网络错误
    - 5005: 配置错误

- OAuth 异常 (6001-6999)
    - 6001: 参数无效
    - 6002: 令牌过期
    - 6003: 令牌无效
    - 6004: 授权码无效

- 推送异常 (3001-3999)
    - 3001: 参数无效
    - 3002: 请求失败

- 加密异常 (2001-2999)
    - 2001: 密钥未配置
    - 2002: 无效的密钥
    - 2003: 解密失败
    - 2004: 加密失败

- SDK 异常 (4001-4999)
    - 4001: 配置缺失
    - 4002: 配置无效
    - 4003: 服务初始化失败
    - 4004: 服务不可用

## 版本说明

当前版本：1.0.15

## 许可证

MIT License

## 作者

常观团队
