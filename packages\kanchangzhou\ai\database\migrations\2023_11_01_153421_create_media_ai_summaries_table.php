<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiSummariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('media_ai_summaries', function (Blueprint $table) {
            $table->id();
            $table->integer('media_ai_file_id')
                  ->comment('文件ID');
            $table->string('title', 1024)
                  ->nullable()
                  ->comment('标题');
            $table->string('summary', 1024)
                  ->nullable()
                  ->comment('摘要');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('media_ai_summaries');
    }
}
