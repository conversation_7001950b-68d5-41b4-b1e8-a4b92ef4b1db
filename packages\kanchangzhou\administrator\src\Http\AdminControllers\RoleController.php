<?php

namespace Kanchangzhou\Administrator\Http\AdminControllers;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Exceptions\PermissionFailException;
use Kanchangzhou\Administrator\Http\Resources\RoleResource;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Hooks\PermissionHook;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;
use Spatie\Permission\Models\Role;

class RoleController extends BaseController
{
    /**
     * 角色列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function index() {
        PermissionHook::can('角色.列表');

        $roles = Role::where('guard_name', 'kadmin')
                     ->get();

        return Respond::respondWithData(RoleResource::collection($roles));
    }

    /**
     * 角色详情
     *
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     */
    public function show($id) {
        PermissionHook::can('角色.详情', $id);

        try {
            $role = Role::findById($id, 'kadmin');
        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        $role->loadMissing('permissions');

        return Respond::respondWithData(RoleResource::make($role));
    }

    /**
     * 通用请求验证
     *
     * @param Request $request
     * @param Role|null $model
     *
     * @throws ValidateFailedException
     */
    protected function _validate(Request $request, $model = null) {
        try {
            $this->validate($request, [
                'name' => [
                    'required',
                    $model ? Rule::unique(Role::class)
                                 ->ignore($model->getKey()) : Rule::unique(Role::class),
                ],
                'permissions' => 'array',
                'permissions.*' => 'exists:permissions,id',
            ], [

            ]);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }
    }

    /**
     * 创建角色
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     * @throws ValidateFailedException
     */
    public function store(Request $request) {
        PermissionHook::can('角色.新建');

        $this->_validate($request);

        try {
            $role = Role::create([
                'name' => $request->input('name'),
                'guard_name' => 'kadmin',
            ]);

            $role->syncPermissions($request->input('permissions'));

        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        return Respond::respondWithData(RoleResource::make($role));
    }

    /**
     * 更新角色
     *
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     * @throws ValidateFailedException
     */
    public function update(Request $request, $id) {
        PermissionHook::can('角色.更新', $id);

        try {
            $role = Role::findById($id, 'kadmin');
        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        $this->_validate($request, $role);

        $role->name = $request->input('name');
        $role->save();

        $role->syncPermissions($request->input('permissions'));

        return Respond::respondWithData(RoleResource::make($role));
    }

    /**
     * 删除角色
     *
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws PermissionFailException
     */
    public function destroy($id) {
        PermissionHook::can('角色.删除', $id);

        try {
            $role = Role::findById($id, 'kadmin');
        } catch (\Exception $exception) {
            throw new PermissionFailException(10000, [], $exception);
        }

        if ($role->id == 1) {
            throw new PermissionFailException(PermissionFailException::SUPER_ADMIN_CANT_DEL);
        }

        $role->delete();

        return Respond::respondWithData();
    }
}
