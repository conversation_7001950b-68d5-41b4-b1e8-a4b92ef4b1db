<?php

namespace Kanchangzhou\AI\Jobs\MediaAi;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Kanchangzhou\AI\Jobs\MediaAi\Process\{MediaAiNlpEventnamesJob,
    MediaAiNlpKeywordsJob,
    MediaAiNlpNerJob,
    MediaAiNlpNewsclassificationJob,
    MediaAiNlpSummaryJob};
use Kanchangzhou\AI\Models\MediaAiFile;
use Kanchangzhou\AI\Supports\StringTools;
use Kanchangzhou\Article\Models\Article;

class ArticleForMediaAIJob extends MediaAiBaseJob
{
    protected $articleId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($articleId) {
        $this->articleId = $articleId;
        parent::__construct();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        $article = Article::where('type', Article::TYPE_NORMAL)
                          ->find($this->articleId);

        if (!$article) {
            return;
        }

        $mediaAiFile = MediaAiFile::where('fileaiable_id', $article->id)
                                  ->where('fileaiable_type', Article::class)
                                  ->first();

        if (!$mediaAiFile) {
            $mediaAiFile = new MediaAiFile();
            $mediaAiFile->fileaiable_id = $article->id;
            $mediaAiFile->fileaiable_type = Article::class;
            $mediaAiFile->file_id = Str::uuid();
            $mediaAiFile->media_type = MediaAiFile::MEDIA_TYPE_DOC;
            $mediaAiFile->nlp_text = StringTools::trimString($article->content);
            $mediaAiFile->save();
        }

        dispatch(new MediaAiNlpKeywordsJob($mediaAiFile));
        dispatch(new MediaAiNlpSummaryJob($mediaAiFile));
        dispatch(new MediaAiNlpNerJob($mediaAiFile));
        dispatch(new MediaAiNlpEventnamesJob($mediaAiFile));
        dispatch(new MediaAiNlpNewsclassificationJob($mediaAiFile));
    }
}
