<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->string('role_step')
                  ->nullable()
                  ->comment('角色步骤');
            $table->json('step_input')
                  ->nullable()
                  ->comment('步骤输入');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_histories', function (Blueprint $table) {
            $table->dropColumn('role_step');
            $table->dropColumn('step_input');
        });
    }
};
