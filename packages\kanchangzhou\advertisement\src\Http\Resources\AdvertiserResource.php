<?php

namespace Kanchangzhou\Advertisement\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AdvertiserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id"=>$this->id,
            "advertiser_name"=>$this->advertiser_name,
            "phone"=>$this->phone,
            "email"=>$this->email,
            "addr"=>$this->addr,
            "start_coperation"=>$this->start_coperation,
            "stop_coperation"=>$this->stop_coperation,
            "bz"=>$this->bz,
            "salesman"=>$this->salesman
        ];
    }
}
