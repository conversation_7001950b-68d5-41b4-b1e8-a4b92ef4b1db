<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->renameColumn('top_p', 'penalty');
        });
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->renameColumn('top_p', 'penalty');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('chat_models', function (Blueprint $table) {
            $table->renameColumn('penalty', 'top_p');
        });
        Schema::table('chat_roles', function (Blueprint $table) {
            $table->renameColumn('penalty', 'top_p');
        });
    }
};
