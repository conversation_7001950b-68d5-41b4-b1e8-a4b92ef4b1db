<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Article\Models\Article;
use Kanchangzhou\Article\Models\ArticleCategory;
use Kanchangzhou\Article\Models\ArticleSpecial;
use Kanchangzhou\Article\Models\ArticleSpecialCategory;
use Kanchangzhou\Article\Models\ArticleSpecialItem;
use Kanchangzhou\HomePage\Facades\HomePageFacade;
use Kanchangzhou\HomePage\Models\HomeCategory;
use Kanchangzhou\HomePage\Models\HomeColumn;
use Kanchangzhou\HomePage\Models\HomeItem;
use Kanchangzhou\HomePage\Models\HomeWidget;
use Kanchangzhou\HomePage\Models\HomeWidgetItem;
use Kanchangzhou\Kernel\Supports\Redirectable;

class NewDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run() {
        $faker = \Faker\Factory::create('zh_CN');

        Article::truncate();
        ArticleCategory::truncate();

        $category = ArticleCategory::create([
            'type' => ArticleCategory::TYPE_NORMAL,
            'title' => $faker->word(),
            'sort' => 0,
            'status' => ArticleCategory::STATUS_VALID,
        ]);

        $articleNormalBig = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用大图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalOne = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用左单图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_ONE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalThree = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用三小图列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_THREE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalNone = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用无图列表展示，123并且它的标题还特别特别特别特别的长abcd word',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_NO_IMG,
            'thumbnails' => [],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalVideo = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇视频文稿,它的标题一般长',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_VIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=video_thumbnail',
                ],
            ],
            'materials' => [
                'video' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/uploads/20191023/ca3e2d6db8d445e97b101790d2e214c6.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalAudio = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇音频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_AUDIO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=audio_thumnail',
                ],
            ],
            'materials' => [
                'audio' => [
                    'title' => $faker->sentence,
                    'src' => 'http://zt.xiao5.cn/2020/ljfl/audio.mp3',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalMiniVideo = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇微视频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_MINIVIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/320x500',
                ],
            ],
            'materials' => [
                'minivideo' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/1569309388489VID_20190924_151620.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalGallery = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一个图集图文,它里面有好多好多图',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_GALLERY,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [
                'gallery' => [
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery1',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery2',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery3',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery4',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery5',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery6',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery7',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery8',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery9',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery10',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery11',
                    ],
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);


        $articleNormalBig1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用大图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalOne1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用左单图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_ONE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalThree1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用三小图列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_THREE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalNone1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用无图列表展示，123并且它的标题还特别特别特别特别的长abcd word',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_NO_IMG,
            'thumbnails' => [],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalVideo1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇视频文稿,它的标题一般长',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_VIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=video_thumbnail',
                ],
            ],
            'materials' => [
                'video' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/uploads/20191023/ca3e2d6db8d445e97b101790d2e214c6.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalAudio1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇音频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_AUDIO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=audio_thumnail',
                ],
            ],
            'materials' => [
                'audio' => [
                    'title' => $faker->sentence,
                    'src' => 'http://zt.xiao5.cn/2020/ljfl/audio.mp3',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalMiniVideo1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇微视频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_MINIVIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/320x500',
                ],
            ],
            'materials' => [
                'minivideo' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/1569309388489VID_20190924_151620.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalGallery1 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一个图集图文,它里面有好多好多图',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_GALLERY,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [
                'gallery' => [
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery1',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery2',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery3',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery4',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery5',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery6',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery7',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery8',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery9',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery10',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery11',
                    ],
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);


        $articleNormalBig2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用大图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalOne2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用左单图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_ONE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalThree2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用三小图列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_THREE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalNone2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用无图列表展示，123并且它的标题还特别特别特别特别的长abcd word',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_NO_IMG,
            'thumbnails' => [],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalVideo2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇视频文稿,它的标题一般长',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_VIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=video_thumbnail',
                ],
            ],
            'materials' => [
                'video' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/uploads/20191023/ca3e2d6db8d445e97b101790d2e214c6.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalAudio2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇音频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_AUDIO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=audio_thumnail',
                ],
            ],
            'materials' => [
                'audio' => [
                    'title' => $faker->sentence,
                    'src' => 'http://zt.xiao5.cn/2020/ljfl/audio.mp3',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalMiniVideo2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇微视频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_MINIVIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/320x500',
                ],
            ],
            'materials' => [
                'minivideo' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/1569309388489VID_20190924_151620.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalGallery2 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一个图集图文,它里面有好多好多图',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_GALLERY,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [
                'gallery' => [
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery1',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery2',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery3',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery4',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery5',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery6',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery7',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery8',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery9',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery10',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery11',
                    ],
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);


        $articleNormalBig3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用大图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalOne3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用左单图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_ONE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalThree3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用三小图列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_THREE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalNone3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用无图列表展示，123并且它的标题还特别特别特别特别的长abcd word',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_NO_IMG,
            'thumbnails' => [],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalVideo3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇视频文稿,它的标题一般长',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_VIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=video_thumbnail',
                ],
            ],
            'materials' => [
                'video' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/uploads/20191023/ca3e2d6db8d445e97b101790d2e214c6.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalAudio3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇音频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_AUDIO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=audio_thumnail',
                ],
            ],
            'materials' => [
                'audio' => [
                    'title' => $faker->sentence,
                    'src' => 'http://zt.xiao5.cn/2020/ljfl/audio.mp3',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalMiniVideo3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇微视频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_MINIVIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/320x500',
                ],
            ],
            'materials' => [
                'minivideo' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/1569309388489VID_20190924_151620.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalGallery3 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一个图集图文,它里面有好多好多图',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_GALLERY,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [
                'gallery' => [
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery1',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery2',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery3',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery4',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery5',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery6',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery7',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery8',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery9',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery10',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery11',
                    ],
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);


        $articleNormalBig4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用大图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalOne4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用左单图于列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_ONE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalThree4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用三小图列表展示',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_THREE_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
                [
                    'src' => 'https://dummyimage.com/230x175',
                ],
            ],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalNone4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇图文文稿使用无图列表展示，123并且它的标题还特别特别特别特别的长abcd word',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_NORMAL,
            'list_template' => Article::LIST_TEMPLATE_NO_IMG,
            'thumbnails' => [],
            'materials' => [],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalVideo4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇视频文稿,它的标题一般长',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_VIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=video_thumbnail',
                ],
            ],
            'materials' => [
                'video' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/uploads/20191023/ca3e2d6db8d445e97b101790d2e214c6.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalAudio4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇音频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_AUDIO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422?text=audio_thumnail',
                ],
            ],
            'materials' => [
                'audio' => [
                    'title' => $faker->sentence,
                    'src' => 'http://zt.xiao5.cn/2020/ljfl/audio.mp3',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalMiniVideo4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一篇微视频图文',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_MINIVIDEO,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/320x500',
                ],
            ],
            'materials' => [
                'minivideo' => [
                    'title' => $faker->sentence,
                    'src' => 'https://zwrongmei.oss-cn-shanghai.aliyuncs.com/1569309388489VID_20190924_151620.mp4',
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);
        $articleNormalGallery4 = Article::create([
            'uuid' => $faker->uuid,
            'article_category_id' => $category->id,
            'title' => '这是一个图集图文,它里面有好多好多图',
            'slug' => pinyin_permalink($faker->sentence(rand(8, 12))),
            'summary' => $faker->sentences(rand(2, 4), true),
            'content' => $faker->paragraphs(rand(3, 8), true),
            'type' => Article::TYPE_GALLERY,
            'list_template' => Article::LIST_TEMPLATE_BIG_IMG,
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x422',
                ],
            ],
            'materials' => [
                'gallery' => [
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery1',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery2',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery3',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery4',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery5',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery6',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery7',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery8',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery9',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery10',
                    ],
                    [
                        'title' => $faker->sentence,
                        'src' => 'https://dummyimage.com/750x422?text=gallery11',
                    ],
                ],
            ],
            'redirect_to' => '',
            'author' => $faker->name,
            'can_comment' => rand(1, 2),
            'virtual_likes' => rand(20, 200),
            'virtual_views' => rand(100, 800),
            'true_views' => rand(100, 300),
            'published_at' => \Carbon\Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
            'status' => Article::STATUS_FINAL,
        ]);


        ArticleSpecialCategory::truncate();
        ArticleSpecial::truncate();
        ArticleSpecialItem::truncate();

        $special = ArticleSpecial::create([
            'title' => '这是一篇专题',
            'summary' => '这是专题的简述,它可能有很多很多的字,abcd word ,1235131, 还有很多奇奇怪怪的组合,比如abcd word ,1235131, 还有很多奇奇怪怪的组合abcd word ,1235131, 还有很多奇奇怪怪的组合abcd word ,1235131, 还有很多奇奇怪怪的组合',
            'thumbnails' => [
                [
                    'src' => 'https://dummyimage.com/750x211',
                ],
            ],
            'share_image' => 'https://dummyimage.com/200x200?text=share',
            'content' => $faker->paragraph,
            'can_comment' => ArticleSpecial::COMMENT_CAN,
            'type' => ArticleSpecial::TYPE_ARTICLE,
            'status' => ArticleSpecial::STATUS_FINAL,
            'published_at' => Carbon::now(),
            'creator_id' => rand(1, 2),
            'modifier_id' => rand(1, 2),
        ]);

        $special->categories()
                ->createMany([
                    ['title' => '专题分类1'],
                    ['title' => '专题分类2'],
                    ['title' => '专题分类3'],
                ]);

        $special->articles()
                ->attach([
                    $articleNormalBig->id => [
                        'category_id' => 1,
                        'sort' => 1,
                    ],
                    $articleNormalOne->id => [
                        'category_id' => 1,
                        'sort' => 2,
                    ],
                    $articleNormalThree->id => [
                        'category_id' => 2,
                        'sort' => 3,
                    ],
                    $articleNormalNone->id => [
                        'category_id' => 2,
                        'sort' => 4,
                    ],
                    $articleNormalVideo->id => [
                        'category_id' => 3,
                        'sort' => 5,
                    ],
                    $articleNormalGallery->id => [
                        'category_id' => 3,
                        'sort' => 6,
                    ],
                ]);


        HomeWidget::truncate();
        HomeWidgetItem::truncate();

        $carouselWidget = HomeWidget::create([
            'title' => '首页轮播模块1',
            'home_column_id' => null,
            'type' => HomeWidget::TYPE_CAROUSEL,
            'status' => HomeWidget::STATUS_VALID,
        ]);

        $carouselWidget->items()
                       ->createMany([
                           [
                               'title' => '轮播组件第一张,跳转图文文稿',
                               'sub_title' => '轮播组件第一张副标题',
                               'summary' => '轮播组件第一张简述',
                               'image' => 'https://dummyimage.com/750x422?text=Carousel ONE',
                               'redirect_to' => Redirectable::make()
                                                            ->buildRedirectToByModel($articleNormalBig),
                               'status' => HomeWidgetItem::STATUS_VALID,
                               'sort' => 10,
                           ],
                           [
                               'title' => '轮播组件第二张,跳转图集文稿',
                               'sub_title' => '轮播组件第二张副标题',
                               'summary' => '轮播组件第二张简述',
                               'image' => 'https://dummyimage.com/750x422?text=Carousel TWO',
                               'redirect_to' => Redirectable::make()
                                                            ->buildRedirectToByModel($articleNormalGallery),
                               'status' => HomeWidgetItem::STATUS_VALID,
                               'sort' => 9,
                           ],
                           [
                               'title' => '轮播组件第三张,跳转视频文稿',
                               'sub_title' => '轮播组件第三张副标题',
                               'summary' => '轮播组件第三张简述',
                               'image' => 'https://dummyimage.com/750x422?text=Carousel THREE',
                               'redirect_to' => Redirectable::make()
                                                            ->buildRedirectToByModel($articleNormalVideo),
                               'status' => HomeWidgetItem::STATUS_VALID,
                               'sort' => 8,
                           ],
                           [
                               'title' => '轮播组件第四张,跳转微视频文稿',
                               'sub_title' => '轮播组件第四张副标题',
                               'summary' => '轮播组件第四张简述',
                               'image' => 'https://dummyimage.com/750x422?text=Carousel FOUR',
                               'redirect_to' => Redirectable::make()
                                                            ->buildRedirectToByModel($articleNormalMiniVideo),
                               'status' => HomeWidgetItem::STATUS_VALID,
                               'sort' => 7,
                           ],
                       ]);

        $cardWidget = HomeWidget::create([
            'title' => '卡片小组件',
            'home_column_id' => null,
            'type' => HomeWidget::TYPE_CARD,
            'status' => HomeWidget::STATUS_VALID,
        ]);

        $cardWidget->items()
                   ->createMany([
                       [
                           'title' => '卡片组件第一张',
                           'sub_title' => '副标题',
                           'summary' => '简述内容',
                           'image' => 'https://dummyimage.com/200x200?text=Card ONE',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalBig),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 10,
                       ],
                       [
                           'title' => '卡片组件第二张,跳转图集文稿',
                           'sub_title' => '卡片组件第二张副标题',
                           'summary' => '卡片组件第二张简述',
                           'image' => 'https://dummyimage.com/200x200?text=Card TWO',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalGallery),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 9,
                       ],
                       [
                           'title' => '卡片组件第三张,跳转视频文稿',
                           'sub_title' => '卡片组件第三张副标题',
                           'summary' => '卡片组件第三张简述',
                           'image' => 'https://dummyimage.com/200x200?text=Card THREE',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalVideo),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 8,
                       ],
                       [
                           'title' => '卡片组件第四张,跳转微视频文稿',
                           'sub_title' => '卡片组件第四张副标题',
                           'summary' => '卡片组件第四张简述',
                           'image' => 'https://dummyimage.com/200x200?text=Card FOUR',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalMiniVideo),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 7,
                       ],
                   ]);

        $iconWidget = HomeWidget::create([
            'title' => 'Icon小组件',
            'home_column_id' => null,
            'type' => HomeWidget::TYPE_ICON,
            'status' => HomeWidget::STATUS_VALID,
        ]);

        $iconWidget->items()
                   ->createMany([
                       [
                           'title' => '第一个',
                           'sub_title' => '副标题',
                           'summary' => '简述内容',
                           'image' => 'https://dummyimage.com/150x150?text=I1',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalGallery),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 10,
                       ],
                       [
                           'title' => '第二个',
                           'sub_title' => '图标列表组件第二张副标题',
                           'summary' => '图标列表组件第二张简述',
                           'image' => 'https://dummyimage.com/150x150?text=I2',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalGallery2),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 9,
                       ],
                       [
                           'title' => '第三个',
                           'sub_title' => '图标列表组件第三张副标题',
                           'summary' => '图标列表组件第三张简述',
                           'image' => 'https://dummyimage.com/150x150?text=I3',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalBig),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 8,
                       ],
                       [
                           'title' => '第四个',
                           'sub_title' => '图标列表组件第四张副标题',
                           'summary' => '图标列表组件第四张简述',
                           'image' => 'https://dummyimage.com/150x150?text=I4',
                           'redirect_to' => Redirectable::make()
                                                        ->buildRedirectToByModel($articleNormalThree1),
                           'status' => HomeWidgetItem::STATUS_VALID,
                           'sort' => 7,
                       ],
                   ]);


        HomeColumn::truncate();
        HomeCategory::truncate();
        HomeItem::truncate();

        auth()
            ->guard('kadmin')
            ->login(AdminUser::first());

        $colum1 = HomeColumn::create([
            'title' => '推荐',
            'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
            'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
            'template' => HomeColumn::TEMPLATE_NORMAL,
            'redirect_to' => '',
            'is_recommended' => HomeColumn::IS_RECOMMENDED_YES,
            'can_sort' => HomeColumn::CAN_SORT_NO,
            'can_delete' => HomeColumn::CAN_DELETE_NO,
            'is_show' => HomeColumn::IS_SHOW_YES,
            'default_sort' => 1,
        ]);

        HomePageFacade::pushToHomePage($articleNormalBig, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalNone, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalOne, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalThree, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalVideo, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalAudio, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalGallery, $colum1->id);
        HomePageFacade::pushToHomePage($special, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalBig1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalNone1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalOne1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalThree1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalVideo1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalAudio1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalGallery1, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalBig2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalNone2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalOne2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalThree2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalVideo2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalAudio2, $colum1->id);
        HomePageFacade::pushToHomePage($articleNormalGallery2, $colum1->id);

        $colum2 = HomeColumn::create([
            'title' => '民生',
            'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
            'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
            'template' => HomeColumn::TEMPLATE_NORMAL,
            'redirect_to' => '',
            'is_recommended' => HomeColumn::IS_RECOMMENDED_YES,
            'can_sort' => HomeColumn::CAN_SORT_NO,
            'can_delete' => HomeColumn::CAN_DELETE_NO,
            'is_show' => HomeColumn::IS_SHOW_YES,
            'default_sort' => 1,
        ]);


        HomePageFacade::pushToHomePage($articleNormalBig, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalNone, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalOne, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalThree, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalVideo, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalAudio, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalGallery, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalBig1, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalOne1, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalBig3, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalOne3, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalThree3, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalNone3, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalVideo3, $colum2->id);
        HomePageFacade::pushToHomePage($articleNormalAudio3, $colum2->id);
        HomePageFacade::pushToHomePage($iconWidget, $colum2->id);
        HomePageFacade::pushToHomePage($carouselWidget, $colum2->id);


        $colum3 = HomeColumn::create([
            'title' => '微视频',
            'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
            'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
            'template' => HomeColumn::TEMPLATE_TWO_COLUMN,
            'redirect_to' => '',
            'is_recommended' => HomeColumn::IS_RECOMMENDED_YES,
            'can_sort' => HomeColumn::CAN_SORT_NO,
            'can_delete' => HomeColumn::CAN_DELETE_NO,
            'is_show' => HomeColumn::IS_SHOW_YES,
            'default_sort' => 1,
        ]);


        HomePageFacade::pushToHomePage($articleNormalMiniVideo, $colum3->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo1, $colum3->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo2, $colum3->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo3, $colum3->id);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo4, $colum3->id);


        $colum4 = HomeColumn::create([
            'title' => '市辖区',
            'thumbnail_img' => 'https://dummyimage.com/100x100?text=Thumb',
            'icon_img' => 'https://dummyimage.com/20x20?text=ICON',
            'template' => HomeColumn::TEMPLATE_NORMAL,
            'redirect_to' => '',
            'is_recommended' => HomeColumn::IS_RECOMMENDED_YES,
            'can_sort' => HomeColumn::CAN_SORT_NO,
            'can_delete' => HomeColumn::CAN_DELETE_NO,
            'is_show' => HomeColumn::IS_SHOW_YES,
            'default_sort' => 1,
        ]);

        $colum4->categories()
               ->createMany([
                   [
                       'title' => '溧阳市',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
                   [
                       'title' => '金坛区',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
                   [
                       'title' => '武进区',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
                   [
                       'title' => '新北区',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
                   [
                       'title' => '天宁区',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
                   [
                       'title' => '钟楼区',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
                   [
                       'title' => '经开区',
                       'icon' => '',
                       'url' => '',
                       'sort' => 10,
                       'is_show' => HomeCategory::IS_SHOW_YES,
                   ],
               ]);


        HomePageFacade::pushToHomePage($articleNormalBig, $colum4->id, 1);
        HomePageFacade::pushToHomePage($articleNormalNone, $colum4->id, 1);
        HomePageFacade::pushToHomePage($articleNormalOne, $colum4->id, 1);
        HomePageFacade::pushToHomePage($articleNormalThree, $colum4->id, 1);
        HomePageFacade::pushToHomePage($articleNormalVideo, $colum4->id, 1);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo, $colum4->id, 1);
        HomePageFacade::pushToHomePage($articleNormalAudio, $colum4->id, 2);
        HomePageFacade::pushToHomePage($articleNormalGallery, $colum4->id, 2);
        HomePageFacade::pushToHomePage($special, $colum4->id, 2);
        HomePageFacade::pushToHomePage($articleNormalBig1, $colum4->id, 2);
        HomePageFacade::pushToHomePage($articleNormalNone1, $colum4->id, 3);
        HomePageFacade::pushToHomePage($articleNormalOne1, $colum4->id, 3);
        HomePageFacade::pushToHomePage($articleNormalThree1, $colum4->id, 3);
        HomePageFacade::pushToHomePage($articleNormalVideo1, $colum4->id, 3);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo1, $colum4->id, 3);
        HomePageFacade::pushToHomePage($articleNormalAudio1, $colum4->id, 3);
        HomePageFacade::pushToHomePage($articleNormalGallery1, $colum4->id, 4);
        HomePageFacade::pushToHomePage($articleNormalBig2, $colum4->id, 4);
        HomePageFacade::pushToHomePage($articleNormalNone2, $colum4->id, 4);
        HomePageFacade::pushToHomePage($articleNormalOne2, $colum4->id, 4);
        HomePageFacade::pushToHomePage($articleNormalThree2, $colum4->id, 4);
        HomePageFacade::pushToHomePage($articleNormalVideo2, $colum4->id, 4);
        HomePageFacade::pushToHomePage($articleNormalMiniVideo2, $colum4->id, 5);
        HomePageFacade::pushToHomePage($articleNormalAudio2, $colum4->id, 5);
        HomePageFacade::pushToHomePage($articleNormalGallery2, $colum4->id, 6);

    }
}
