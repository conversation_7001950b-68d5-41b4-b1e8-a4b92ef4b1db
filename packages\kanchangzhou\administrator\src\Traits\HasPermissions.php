<?php


namespace Kanchangzhou\Administrator\Traits;


use Kanchangzhou\Administrator\Exceptions\NotHasPermissionException;
use Kanchangzhou\Administrator\Facades\AdministratorFacade;
use Illuminate\Database\Eloquent\Model;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Kernel\Exceptions\AuthFailException;

trait HasPermissions
{
    /**
     * 验证是否登录
     *
     * @return AdminUser
     * @throws AuthFailException
     */
    protected function isAuth() {
        if (!$adminUser = AdministratorFacade::user()) {
            throw new AuthFailException(AuthFailException::UNAUTHENTICATED,[],401);
        }

        return $adminUser;
    }

    /**
     * 判断是否有权限
     *
     * @param string|int|\Spatie\Permission\Contracts\Permission $permission $permission
     * @param Model|null $targetModel 目标数据模型
     * @param bool $isThr 是否抛出异常
     *
     * @return bool
     * @throws AuthFailException
     * @throws NotHasPermissionException
     */
    protected function can($permission, Model $targetModel = null, $isThr = true) {
        $adminUser = $this->isAuth();

        // 判断是否有权限
        $hasPermission = $adminUser->checkPermissionTo($permission, 'kadmin');

        // 判断该用户是否仅有目标数据模型权限
        $onlyPermission = true;
        if (!$targetModel) {
            $onlyPermission = $adminUser->targetModels($targetModel)
                                        ->count() == 0 ? true : $adminUser->targetModels($targetModel)
                                                                          ->where('target_id', $targetModel->getKey())
                                                                          ->exists();
        }

        if (!($hasPermission && $onlyPermission) && $isThr) {
            throw new NotHasPermissionException(NotHasPermissionException::HAS_NO_PERMISSION, [
                'permission' => $permission,
            ]);
        }

        return $hasPermission && $onlyPermission;
    }

    /**
     * 仅判断是否具有目标数据模型权限
     *
     * @param Model $targetModel
     * @param bool $isThr
     *
     * @return bool
     * @throws AuthFailException
     * @throws NotHasPermissionException
     */
    protected function canOnly(Model $targetModel, $isThr = true) {
        $adminUser = $this->isAuth();

        $onlyPermission = $adminUser->targetModels($targetModel)
                                    ->count() == 0 ? true : $adminUser->targetModels($targetModel)
                                                                      ->where('target_id', $targetModel->getKey())
                                                                      ->exists();

        if (!$onlyPermission && $isThr) {
            throw new NotHasPermissionException(NotHasPermissionException::HAS_NO_PERMISSION);
        }

        return $onlyPermission;
    }

    /**
     * 验证是否有角色
     *
     * @param $roles
     *
     * @return bool
     * @throws AuthFailException
     */
    protected function hasRole($roles) {
        $adminUser = $this->isAuth();

        return $adminUser->hasRole($roles);
    }

    /**
     * 验证是否
     *
     * @param $roles
     *
     * @return bool
     * @throws AuthFailException
     */
    protected function hasAnyRole($roles) {
        $adminUser = $this->isAuth();

        return $adminUser->hasAnyRole($roles);
    }

}