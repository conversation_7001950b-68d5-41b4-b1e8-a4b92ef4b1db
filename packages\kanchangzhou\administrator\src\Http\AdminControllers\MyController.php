<?php


namespace Kanchangzhou\Administrator\Http\AdminControllers;


use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Kanchangzhou\Administrator\Facades\AdministratorFacade;
use Kanchangzhou\Administrator\Http\Resources\AdminUserResource;
use Kanchangzhou\Administrator\Models\AdminUser;
use Kanchangzhou\Administrator\Supports\AuthSafe;
use Kanchangzhou\Auth\Facades\AuthFacade;
use Kanchangzhou\Kernel\Exceptions\ValidateFailedException;
use Kanchangzhou\Kernel\Http\Controllers\BaseController;
use Kanchangzhou\Kernel\Supports\Respond;

class MyController extends BaseController
{
    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws \Kanchangzhou\Administrator\Exceptions\AuthSafeException
     */
    public function show() {
        $adminUser = AuthFacade::adminUser();
        $adminUser->load('roles', 'permissions');
        $adminUser->setAttribute('pwd_expired_at', '账号的密码将在 ' . AuthSafe::pwdExpiredCheck($adminUser->getId(), $adminUser->updated_at) . ' 天过期，请在到期之前修改密码');

        return Respond::respondWithData(JsonResource::make($adminUser));
    }

    public function update(Request $request) {
        $adminUser = AdministratorFacade::user();

        try {
            $this->validate($request, [
                'password' => [
                    'nullable',
                    'confirmed',
                    'min:8',
                    'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
                ],
                'nickname' => [
                    'required',
                    Rule::unique(AdminUser::class)
                        ->ignore($adminUser->id),
                ],
                'true_name' => 'required',
                'mobile' => [
                    'required',
                    Rule::unique(AdminUser::class)
                        ->ignore($adminUser->id),
                ],
            ], [
                'password.regex' => '密码必须包含大小写字母,数字和特殊符号(!@#$%^&*?-_)且最少8位',
            ]);
        } catch (ValidationException $exception) {
            throw new ValidateFailedException(ValidateFailedException::VALIDATION_ERROR, $exception->errors());
        }

        if ($request->input('password')) {
            $adminUser->password = bcrypt($request->input('password'));
        }
        $adminUser->nickname = $request->input('nickname') ?: $adminUser->nickname;
        $adminUser->true_name = $request->input('true_name') ?: $adminUser->true_name;
        $adminUser->mobile = $request->input('mobile') ?: $adminUser->mobile;
        $adminUser->avatar = $request->input('avatar') ?: $adminUser->avatar;
        $adminUser->updated_at = Carbon::now();
        $adminUser->save();

        return Respond::respondWithData();
    }
}
