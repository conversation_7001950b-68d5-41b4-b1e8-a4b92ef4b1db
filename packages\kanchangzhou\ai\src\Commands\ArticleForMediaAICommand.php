<?php

namespace Kanchangzhou\AI\Commands;

use Illuminate\Console\Command;
use Kanchangzhou\AI\Jobs\MediaAi\ArticleForMediaAIJob;
use Kanchangzhou\Article\Models\Article;

class ArticleForMediaAICommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:mediaAiArticle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '自动批处理文章';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        Article::where('status', Article::STATUS_FINAL)
               ->where('type', Article::TYPE_NORMAL)
               ->whereRaw("COALESCE(redirect_to,'') = ''")
               ->orderByDesc('id')
               ->chunk(100, function ($articles) {
                   foreach ($articles as $article) {
                       $this->line("------ {$article->id} ------");

                       dispatch(new ArticleForMediaAIJob($article->id));
                   }
               });

        return 0;
    }
}
