<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('skill_competition_scores', function (Blueprint $table) {
            $table->id();
            $table->integer('skill_competition_id');
            $table->integer('judge_id');
            $table->string('judge_name');
            $table->string('score', 1000);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('skill_competition_scores');
    }
};
