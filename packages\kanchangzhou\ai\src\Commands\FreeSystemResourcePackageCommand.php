<?php

namespace Kanchangzhou\AI\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Kanchangzhou\AI\Exceptions\AiOrderException;
use Kanchangzhou\AI\Models\AiOrder;
use Kanchangzhou\AI\Models\AiResourcePackage;
use Kanchangzhou\AI\Models\AiUserPackage;
use Kanchangzhou\Auth\Facades\AuthFacade;

class FreeSystemResourcePackageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:freePackage {packageId} {userId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    public function handle() {
        $package = AiResourcePackage::where('status', AiResourcePackage::STATUS_VALID)
                                    ->where('id', $this->argument('packageId'))
                                    ->firstOrFail();

        // 检查用户是否已经购买过免费套餐,并且套餐未过期
        $userPackage = AiUserPackage::where('user_id', $this->argument('userId'))
                                    ->where('guard_name', 'kadmin')
                                    ->where('resource_package_id', $package->id)
                                    ->where('expired_at', '>', Carbon::now())
                                    ->where('used_tokens', '<', $package->total_tokens)
                                    ->first();

        if ($userPackage) {
            //throw new AiOrderException(AiOrderException::FREE_REPEAT_BUY);
            return 0;
        }

        // 创建订单
        $order = AiOrder::create([
            'user_id' => $this->argument('userId'),
            'guard_name' => 'kadmin',
            // 'kadmin' is the default guard name
            'order_no' => 'AI' . date('YmdHis') . rand(1000, 9999),
            'total' => 0,
            'status' => AiOrder::STATUS_PAID,
            'order_type' => AiOrder::ORDER_TYPE_CHAT_PACKAGE,
            'payment_no' => 'P' . date('YmdHis') . rand(1000, 9999),
            'payment_method' => AiOrder::PAYMENT_METHOD_SYSTEM,
            'paid_at' => Carbon::now(),
        ]);

        $order->items()
              ->create([
                  'model_id' => 0,
                  'model_uuid' => '',
                  'provider' => '',
                  'input_tokens' => 0,
                  'output_tokens' => 0,
                  'total_tokens' => $package->total_tokens,
                  'price' => 0,
                  'unit_count' => 0,
                  'total' => 0,
                  'order_type' => $package->type,
                  'status' => AiOrder::STATUS_PAID,
                  'user_package_id' => 0,
                  'chat_history_id' => 0,
              ]);


        // 创建用户套餐
        AiUserPackage::create([
            'user_id' => $this->argument('userId'),
            'guard_name' => 'kadmin',
            // 'kadmin' is the default guard name
            'total_tokens' => $package->total_tokens,
            'used_tokens' => 0,
            'package_type' => $package->type,
            'provider' => $package->provider,
            'model_type' => $package->model_type,
            'model_id' => $package->model_id,
            'is_general' => $package->is_general,
            'order_id' => $order->id,
            'expired_at' => Carbon::now()
                                  ->addDays($package->expired_days)
                                  ->endOfDay(),
            'resource_package_id' => $package->id,
        ]);
    }
}
