<?php

namespace Kanchangzhou\AI\Services\Audio\Handlers;

use GuzzleHttp\Psr7\Response;
use WebSocket\Client;

class AiAudioWsHandler
{
    protected $client;

    protected $input;

    public function __construct($uri, $input, $timeout = 300) {
        $this->client = new Client($uri);
        $this->client->setTimeout($timeout);
        $this->input = $input;
    }

    public function sendAndReceive() {
        $result = '';

        try {
            $this->client->send($this->input);
            while (true) {
                $message = $this->jsondecode($this->client->receive());

                if ($message->code !== 0) {
                    throw new \Exception($message->message);
                }

                switch ($message->data->status) {
                    case 1:
                        $result .= base64_decode($message->data->audio);
                        break;
                    case 2:
                        $result .= base64_decode($message->data->audio);
                        break 2;
                }
            }

            return new Response(200, [], $result);
        } catch (\Exception $e) {
            throw $e;
        } finally {
            $this->client->close();
        }
    }

    public function send($message = null) {
        try {
            if (empty($message)) {
                if (!empty($this->input)) {
                    $message = $this->input;
                } else {
                    throw new \Exception('Message is empty');
                }
            }
            $this->client->send($message);
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function receive() {
        return $this->client->receive();
    }

    private static function jsondecode($json, $assoc = false, $depth = 512, $options = 0) {
        $data = json_decode($json, $assoc, $depth, $options);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('json_decode error: ' . json_last_error_msg());
        }

        return $data;
    }
}
