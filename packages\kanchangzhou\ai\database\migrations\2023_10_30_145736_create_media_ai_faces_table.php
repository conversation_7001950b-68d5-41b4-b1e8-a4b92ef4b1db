<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMediaAiFacesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_ai_faces', function (Blueprint $table) {
            $table->id();
            // 字段: 姓名,tag,是否已知,人物ID,游标ID,情绪,情绪概率,偏移角度,坐标,时间
            $table->integer('media_ai_file_id')->comment('文件ID');
            $table->string('name')->nullable()->comment('姓名');
            $table->string('tag')->nullable()->comment('tag');
            $table->boolean('is_known')->default(false)->comment('是否已知');
            $table->string('person_id')->nullable()->comment('人物ID');
            $table->string('kind')->nullable()->comment('情绪');
            $table->float('kind_confidence')->nullable()->comment('情绪概率');
            $table->string('rotation')->nullable()->comment('偏移角度');
            $table->json('keyframe')->nullable()->comment('坐标高宽');
            $table->string('offset_time')->nullable()->comment('时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_ai_faces');
    }
}
